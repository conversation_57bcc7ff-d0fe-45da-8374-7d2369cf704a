#!/bin/bash

# 完整本地开发环境启动脚本（数据库 + 后端应用）
set -e

echo "🚀 启动完整本地开发环境..."

# 切换到项目根目录
cd "$(dirname "$0")/../.."

# 检查 Docker 是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker 未运行，请先启动 Docker"
    exit 1
fi

# 检查Go环境
if ! command -v go &> /dev/null; then
    echo "❌ Go环境未安装，请先安装Go"
    exit 1
fi

# 启动数据库和 Redis
echo "📦 启动数据库和 Redis..."
docker compose -f docker-compose.dev.yml up -d postgres redis

# 等待数据库启动
echo "⏳ 等待数据库启动..."
sleep 5

# 检查数据库是否健康
echo "🔍 检查数据库连接..."
until docker exec beijing-nav-postgres-dev pg_isready -U postgres > /dev/null 2>&1; do
    echo "等待 PostgreSQL 启动..."
    sleep 2
done

echo "✅ 数据库已就绪"

# 设置开发环境变量
echo "🎯 启动后端应用..."
export APP_ENV=development
export LOG_LEVEL=debug
export LOG_FORMAT=text

# 数据库配置
export DATABASE_URL="postgresql://postgres:password@localhost:5432/beijing_navigation_dev?sslmode=disable"
export DB_HOST=localhost
export DB_USER=postgres
export DB_PASSWORD=password
export DB_NAME=beijing_navigation_dev

# Redis配置
export REDIS_HOST=localhost
export REDIS_PORT=6379

echo "🌐 应用程序将在以下地址启动:"
echo "   - API: http://localhost:8080"
echo "   - WebSocket: ws://localhost:8081/ws"
echo ""
echo "💡 提示："
echo "   - 如需前端开发，请运行: ./deploy/dev/start-dev.sh"
echo "   - 如需仅后端调试，请运行: ./deploy/dev/debug-start.sh"
echo ""
echo "按 Ctrl+C 停止应用程序"
echo ""

# 启动应用
go run cmd/main.go
