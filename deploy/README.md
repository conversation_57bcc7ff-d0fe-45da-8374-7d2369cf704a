# 部署方案重构

## 部署分类

### 1. 开发环境部署 (development)
- **目标**: 本地开发调试
- **技术栈**: Docker Compose
- **特点**: 快速启动，支持热重载，包含完整的开发工具

### 2. 本地部署 (local-k3s)  
- **目标**: 本地生产环境模拟
- **技术栈**: k3s (轻量级Kubernetes)
- **特点**: 接近生产环境，资源占用少，适合单机部署

### 3. 远程部署 (remote)
- **目标**: 生产环境部署
- **技术栈**: Ansible + k3s
- **特点**: 无需在目标服务器安装额外软件，支持多节点，自动化程度高

## 目录结构

```
deploy/
├── README.md                    # 部署说明文档
├── development/                 # 开发环境部署
│   ├── docker-compose.yml      # 开发环境容器编排
│   ├── start.sh                # 启动开发环境
│   ├── stop.sh                 # 停止开发环境
│   └── debug.sh                # 纯后端调试模式
├── local-k3s/                  # 本地k3s部署
│   ├── install-k3s.sh          # k3s安装脚本
│   ├── deploy.sh               # 本地k3s部署
│   ├── build.sh                # 镜像构建
│   └── manifests/              # k8s资源清单
│       ├── namespace.yaml
│       ├── configmap.yaml
│       ├── deployment.yaml
│       └── service.yaml
└── remote/                     # 远程部署
    ├── ansible.cfg             # Ansible配置
    ├── inventory.yml           # 服务器清单
    ├── deploy.yml              # 部署playbook
    ├── install-k3s.yml         # k3s安装playbook
    └── roles/                  # Ansible角色
        ├── k3s/
        ├── app/
        └── monitoring/
```

## 使用方式

### 开发环境
```bash
# 启动完整开发环境
./deploy/development/start.sh

# 仅启动后端调试
./deploy/development/debug.sh

# 停止开发环境
./deploy/development/stop.sh
```

### 本地k3s部署
```bash
# 安装k3s
./deploy/local-k3s/install-k3s.sh

# 构建镜像
./deploy/local-k3s/build.sh

# 部署应用
./deploy/local-k3s/deploy.sh
```

### 远程部署

#### 方式1: 简单部署（推荐，无需额外依赖）
```bash
# 检查连接
./deploy/remote/simple-deploy.sh -H 192.168.1.100 --check

# 完整部署
./deploy/remote/simple-deploy.sh -H 192.168.1.100 --all

# 仅安装k3s
./deploy/remote/simple-deploy.sh -H server.com -u ubuntu --install-k3s

# 仅部署应用
./deploy/remote/simple-deploy.sh -H 10.0.1.10 -k ~/.ssh/id_rsa --deploy-app
```

#### 方式2: Ansible部署（功能更强大）
```bash
# 配置服务器信息
vim deploy/remote/inventory.yml

# 使用Ansible部署
./deploy/remote/deploy.sh --all
```

## 优势

1. **清晰分类**: 三种部署方式职责明确
2. **简化依赖**: 每种方式依赖最小化
3. **自动化**: Ansible实现真正的一键部署
4. **可扩展**: 支持多节点集群部署
5. **标准化**: 使用业界标准工具和实践