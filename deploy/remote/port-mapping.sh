#!/bin/bash

# 远程 K3s 端口映射管理脚本
# 用法: ./port-mapping.sh [enable|disable|status|custom] -H <server-ip>

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 默认配置
DEFAULT_SSH_USER="root"
DEFAULT_SSH_PORT="22"
SERVER_IP=""
SSH_USER="$DEFAULT_SSH_USER"
SSH_PORT="$DEFAULT_SSH_PORT"

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 解析命令行参数
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -H|--host)
                SERVER_IP="$2"
                shift 2
                ;;
            -u|--user)
                SSH_USER="$2"
                shift 2
                ;;
            -p|--port)
                SSH_PORT="$2"
                shift 2
                ;;
            *)
                break
                ;;
        esac
    done
}

# 检查服务器连接
check_server_connection() {
    if [ -z "$SERVER_IP" ]; then
        print_error "请指定服务器 IP 地址: -H <server-ip>"
        exit 1
    fi
    
    print_info "测试服务器连接: $SSH_USER@$SERVER_IP:$SSH_PORT"
    
    if ! ssh -o ConnectTimeout=10 -o BatchMode=yes -p "$SSH_PORT" "$SSH_USER@$SERVER_IP" "echo 'Connection test successful'" &>/dev/null; then
        print_error "无法连接到服务器 $SERVER_IP"
        print_info "请确保："
        print_info "1. 服务器 IP 地址正确"
        print_info "2. SSH 密钥已配置"
        print_info "3. 服务器防火墙允许 SSH 连接"
        exit 1
    fi
    
    print_success "服务器连接正常"
}

# 远程执行 kubectl 命令
remote_kubectl() {
    ssh -p "$SSH_PORT" "$SSH_USER@$SERVER_IP" "kubectl $*"
}

# 启用端口映射
enable_port_mapping() {
    print_info "在远程服务器 $SERVER_IP 上启用端口映射..."
    
    # 检查集群状态
    if ! remote_kubectl cluster-info &>/dev/null; then
        print_error "远程服务器上的 k3s 集群不可用"
        exit 1
    fi
    
    print_info "创建 NodePort 服务..."
    
    # PostgreSQL NodePort
    ssh -p "$SSH_PORT" "$SSH_USER@$SERVER_IP" "kubectl apply -f -" <<EOF
apiVersion: v1
kind: Service
metadata:
  name: postgres-nodeport
  namespace: app
  labels:
    app: postgres
    service-type: external
spec:
  selector:
    app: postgres
  ports:
  - name: postgres
    port: 5432
    targetPort: 5432
    nodePort: 30432
  type: NodePort
EOF

    # Redis NodePort
    ssh -p "$SSH_PORT" "$SSH_USER@$SERVER_IP" "kubectl apply -f -" <<EOF
apiVersion: v1
kind: Service
metadata:
  name: redis-nodeport
  namespace: app
  labels:
    app: redis
    service-type: external
spec:
  selector:
    app: redis
  ports:
  - name: redis
    port: 6379
    targetPort: 6379
    nodePort: 30379
  type: NodePort
EOF

    # Backend NodePort
    ssh -p "$SSH_PORT" "$SSH_USER@$SERVER_IP" "kubectl apply -f -" <<EOF
apiVersion: v1
kind: Service
metadata:
  name: app-nodeport
  namespace: app
  labels:
    app: app
    service-type: external
spec:
  selector:
    app: app
  ports:
  - name: http
    port: 8080
    targetPort: 8080
    nodePort: 30080
  type: NodePort
EOF

    print_success "远程端口映射已启用"
    print_info "服务访问地址："
    print_info "PostgreSQL: $SERVER_IP:30432"
    print_info "Redis: $SERVER_IP:30379"
    print_info "Backend API: $SERVER_IP:30080"
    
    # 检查防火墙提醒
    print_warning "请确保服务器防火墙允许以下端口："
    print_warning "- 30432 (PostgreSQL)"
    print_warning "- 30379 (Redis)"
    print_warning "- 30080 (Backend API)"
}

# 禁用端口映射
disable_port_mapping() {
    print_info "在远程服务器 $SERVER_IP 上禁用端口映射..."
    
    # 删除 NodePort 服务
    remote_kubectl delete service postgres-nodeport -n app --ignore-not-found=true
    remote_kubectl delete service redis-nodeport -n app --ignore-not-found=true
    remote_kubectl delete service app-nodeport -n app --ignore-not-found=true
    
    print_success "远程端口映射已禁用"
    print_info "服务现在只能在集群内部访问"
}

# 自定义端口映射
custom_port_mapping() {
    print_info "配置远程服务器 $SERVER_IP 的自定义端口映射..."
    
    echo "请输入端口配置（直接回车使用默认值）："
    
    read -p "PostgreSQL 外部端口 [30432]: " postgres_port
    read -p "Redis 外部端口 [30379]: " redis_port
    read -p "Backend 外部端口 [30080]: " backend_port
    
    # 设置默认值
    postgres_port=${postgres_port:-30432}
    redis_port=${redis_port:-30379}
    backend_port=${backend_port:-30080}
    
    # 验证端口范围
    validate_nodeport() {
        local port=$1
        if [[ $port -lt 30000 || $port -gt 32767 ]]; then
            print_error "端口 $port 超出 NodePort 范围 (30000-32767)"
            return 1
        fi
    }
    
    validate_nodeport $postgres_port || exit 1
    validate_nodeport $redis_port || exit 1
    validate_nodeport $backend_port || exit 1
    
    print_info "应用自定义端口配置到远程服务器..."
    
    # PostgreSQL
    ssh -p "$SSH_PORT" "$SSH_USER@$SERVER_IP" "kubectl apply -f -" <<EOF
apiVersion: v1
kind: Service
metadata:
  name: postgres-nodeport
  namespace: app
  labels:
    app: postgres
    service-type: external
spec:
  selector:
    app: postgres
  ports:
  - name: postgres
    port: 5432
    targetPort: 5432
    nodePort: $postgres_port
  type: NodePort
EOF

    # Redis
    ssh -p "$SSH_PORT" "$SSH_USER@$SERVER_IP" "kubectl apply -f -" <<EOF
apiVersion: v1
kind: Service
metadata:
  name: redis-nodeport
  namespace: app
  labels:
    app: redis
    service-type: external
spec:
  selector:
    app: redis
  ports:
  - name: redis
    port: 6379
    targetPort: 6379
    nodePort: $redis_port
  type: NodePort
EOF

    # Backend
    ssh -p "$SSH_PORT" "$SSH_USER@$SERVER_IP" "kubectl apply -f -" <<EOF
apiVersion: v1
kind: Service
metadata:
  name: app-nodeport
  namespace: app
  labels:
    app: app
    service-type: external
spec:
  selector:
    app: app
  ports:
  - name: http
    port: 8080
    targetPort: 8080
    nodePort: $backend_port
  type: NodePort
EOF

    print_success "远程自定义端口映射已配置"
    print_info "PostgreSQL: $SERVER_IP:$postgres_port"
    print_info "Redis: $SERVER_IP:$redis_port"
    print_info "Backend API: $SERVER_IP:$backend_port"
    
    print_warning "请确保服务器防火墙允许以下端口："
    print_warning "- $postgres_port (PostgreSQL)"
    print_warning "- $redis_port (Redis)"
    print_warning "- $backend_port (Backend API)"
}

# 显示当前状态
show_status() {
    print_info "远程服务器 $SERVER_IP 的端口映射状态："
    echo "----------------------------------------"
    
    # 检查 NodePort 服务
    local postgres_nodeport=$(remote_kubectl get service postgres-nodeport -n app -o jsonpath='{.spec.ports[0].nodePort}' 2>/dev/null || echo "")
    local redis_nodeport=$(remote_kubectl get service redis-nodeport -n app -o jsonpath='{.spec.ports[0].nodePort}' 2>/dev/null || echo "")
    local app_nodeport=$(remote_kubectl get service app-nodeport -n app -o jsonpath='{.spec.ports[0].nodePort}' 2>/dev/null || echo "")
    
    echo "PostgreSQL: ${postgres_nodeport:+$SERVER_IP:$postgres_nodeport}${postgres_nodeport:-[禁用]}"
    echo "Redis: ${redis_nodeport:+$SERVER_IP:$redis_nodeport}${redis_nodeport:-[禁用]}"
    echo "Backend API: ${app_nodeport:+$SERVER_IP:$app_nodeport}${app_nodeport:-[禁用]}"
    
    echo "----------------------------------------"
    
    # 显示 Pod 状态
    print_info "Pod 状态："
    remote_kubectl get pods -n app -o wide 2>/dev/null || print_warning "无法获取 Pod 状态"
    
    echo "----------------------------------------"
    
    # 显示所有服务
    print_info "所有服务："
    remote_kubectl get services -n app 2>/dev/null || print_warning "无法获取服务状态"
}

# 测试连接
test_connections() {
    print_info "测试远程服务连接..."
    
    # 测试 PostgreSQL
    local postgres_port=$(remote_kubectl get service postgres-nodeport -n app -o jsonpath='{.spec.ports[0].nodePort}' 2>/dev/null || echo "")
    if [ -n "$postgres_port" ]; then
        if command -v pg_isready &> /dev/null; then
            if pg_isready -h $SERVER_IP -p $postgres_port -U driving_nav_user &> /dev/null; then
                print_success "PostgreSQL 连接正常 ($SERVER_IP:$postgres_port)"
            else
                print_warning "PostgreSQL 连接失败 ($SERVER_IP:$postgres_port)"
            fi
        else
            print_info "PostgreSQL 端口开放: $SERVER_IP:$postgres_port (需要 postgresql-client 测试连接)"
        fi
    fi
    
    # 测试 Redis
    local redis_port=$(remote_kubectl get service redis-nodeport -n app -o jsonpath='{.spec.ports[0].nodePort}' 2>/dev/null || echo "")
    if [ -n "$redis_port" ]; then
        if command -v redis-cli &> /dev/null; then
            if redis-cli -h $SERVER_IP -p $redis_port ping &> /dev/null; then
                print_success "Redis 连接正常 ($SERVER_IP:$redis_port)"
            else
                print_warning "Redis 连接失败 ($SERVER_IP:$redis_port)"
            fi
        else
            print_info "Redis 端口开放: $SERVER_IP:$redis_port (需要 redis-tools 测试连接)"
        fi
    fi
    
    # 测试 Backend API
    local app_port=$(remote_kubectl get service app-nodeport -n app -o jsonpath='{.spec.ports[0].nodePort}' 2>/dev/null || echo "")
    if [ -n "$app_port" ]; then
        if command -v curl &> /dev/null; then
            if curl -s --connect-timeout 5 http://$SERVER_IP:$app_port/api/health &> /dev/null; then
                print_success "Backend API 连接正常 ($SERVER_IP:$app_port)"
            else
                print_warning "Backend API 连接失败 ($SERVER_IP:$app_port)"
            fi
        else
            print_info "Backend API 端口开放: $SERVER_IP:$app_port (需要 curl 测试连接)"
        fi
    fi
}

# 配置防火墙
configure_firewall() {
    print_info "配置远程服务器防火墙..."
    
    # 检查防火墙状态
    local firewall_status=$(ssh -p "$SSH_PORT" "$SSH_USER@$SERVER_IP" "systemctl is-active ufw 2>/dev/null || systemctl is-active firewalld 2>/dev/null || echo 'unknown'")
    
    if [ "$firewall_status" = "active" ]; then
        print_info "检测到活跃的防火墙，配置端口规则..."
        
        # UFW 配置
        if ssh -p "$SSH_PORT" "$SSH_USER@$SERVER_IP" "command -v ufw &>/dev/null"; then
            print_info "配置 UFW 防火墙规则..."
            ssh -p "$SSH_PORT" "$SSH_USER@$SERVER_IP" "
                ufw allow 30432/tcp comment 'PostgreSQL NodePort'
                ufw allow 30379/tcp comment 'Redis NodePort'
                ufw allow 30080/tcp comment 'Backend API NodePort'
            "
            print_success "UFW 防火墙规则已配置"
        fi
        
        # Firewalld 配置
        if ssh -p "$SSH_PORT" "$SSH_USER@$SERVER_IP" "command -v firewall-cmd &>/dev/null"; then
            print_info "配置 Firewalld 防火墙规则..."
            ssh -p "$SSH_PORT" "$SSH_USER@$SERVER_IP" "
                firewall-cmd --permanent --add-port=30432/tcp
                firewall-cmd --permanent --add-port=30379/tcp
                firewall-cmd --permanent --add-port=30080/tcp
                firewall-cmd --reload
            "
            print_success "Firewalld 防火墙规则已配置"
        fi
    else
        print_warning "未检测到活跃的防火墙，请手动确保端口开放"
    fi
}

# 显示帮助信息
show_help() {
    echo "远程 K3s 端口映射管理脚本"
    echo ""
    echo "用法: $0 [命令] -H <server-ip> [选项]"
    echo ""
    echo "命令:"
    echo "  enable      启用默认端口映射"
    echo "  disable     禁用所有端口映射"
    echo "  custom      配置自定义端口映射"
    echo "  status      显示当前端口映射状态"
    echo "  test        测试服务连接"
    echo "  firewall    配置防火墙规则"
    echo "  help        显示此帮助信息"
    echo ""
    echo "必需参数:"
    echo "  -H, --host <ip>     远程服务器 IP 地址"
    echo ""
    echo "可选参数:"
    echo "  -u, --user <user>   SSH 用户名 (默认: root)"
    echo "  -p, --port <port>   SSH 端口 (默认: 22)"
    echo ""
    echo "默认端口映射:"
    echo "  PostgreSQL: 30432"
    echo "  Redis: 30379"
    echo "  Backend API: 30080"
    echo ""
    echo "示例:"
    echo "  $0 enable -H *************"
    echo "  $0 custom -H ************* -u ubuntu"
    echo "  $0 status -H *************"
    echo "  $0 firewall -H *************"
    echo "  $0 test -H *************"
}

# 解析参数
parse_args "$@"
shift $((OPTIND-1))

command=${1:-help}

# 对于需要服务器连接的命令，检查连接
case "$command" in
    "enable"|"disable"|"custom"|"status"|"test"|"firewall")
        check_server_connection
        ;;
esac

# 主逻辑
case "$command" in
    "enable")
        enable_port_mapping
        ;;
    "disable")
        disable_port_mapping
        ;;
    "custom")
        custom_port_mapping
        ;;
    "status")
        show_status
        ;;
    "test")
        test_connections
        ;;
    "firewall")
        configure_firewall
        ;;
    "help"|*)
        show_help
        ;;
esac
