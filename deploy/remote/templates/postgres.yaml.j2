apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgres
  namespace: {{ app_namespace }}
spec:
  replicas: 1
  selector:
    matchLabels:
      app: postgres
  template:
    metadata:
      labels:
        app: postgres
    spec:
      containers:
      - name: postgres
        image: postgres:15-alpine
        env:
        - name: POSTGRES_DB
          value: "{{ database_name }}"
        - name: POSTGRES_USER
          valueFrom:
            secretKeyRef:
              name: {{ app_name }}-secret
              key: DATABASE_USER
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: {{ app_name }}-secret
              key: DATABASE_PASSWORD
        ports:
        - containerPort: 5432
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
        readinessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - {{ database_user }}
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: postgres-storage
        emptyDir: {}

---
apiVersion: v1
kind: Service
metadata:
  name: postgres-service
  namespace: {{ app_namespace }}
spec:
  selector:
    app: postgres
  ports:
  - port: 5432
    targetPort: 5432