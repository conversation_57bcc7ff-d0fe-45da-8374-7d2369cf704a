# Ansible 服务器清单配置
# 复制此文件并根据实际环境修改

all:
  children:
    k3s_cluster:
      children:
        k3s_server:
          hosts:
            # k3s 主节点
            server1:
              ansible_host: *************
              ansible_user: root
              # ansible_ssh_private_key_file: ~/.ssh/id_rsa
        k3s_agent:
          hosts:
            # k3s 工作节点（可选）
            # agent1:
            #   ansible_host: *************
            #   ansible_user: root
      vars:
        # k3s 配置
        k3s_version: v1.28.5+k3s1
        k3s_token: "your-cluster-token-here"
        
        # 应用配置
        app_name: "app"
        app_namespace: "app"
        app_image: "app:latest"
        
        # 数据库配置
        database_user: "postgres"
        database_password: "password123"
        database_name: "app"
        
        # 其他配置
        jwt_secret: "your-jwt-secret-key"

# 示例配置：
# 1. 单节点部署
# server1:
#   ansible_host: your-server-ip
#   ansible_user: root

# 2. 多节点部署
# server1:
#   ansible_host: *************
#   ansible_user: root
# agent1:
#   ansible_host: *************
#   ansible_user: root
# agent2:
#   ansible_host: *************
#   ansible_user: root