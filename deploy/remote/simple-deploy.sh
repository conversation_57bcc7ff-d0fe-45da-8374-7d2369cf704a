#!/bin/bash

# 简单远程部署脚本 - 无需Ansible，仅使用SSH
set -e

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

# 默认配置
REMOTE_HOST=""
REMOTE_USER="root"
SSH_KEY=""
SSH_PORT=22
APP_NAME="app"
NAMESPACE="app"

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 显示帮助
show_help() {
    echo "简单远程部署脚本 - 无需Ansible，仅使用SSH"
    echo ""
    echo "用法: $0 -H <host> [选项]"
    echo ""
    echo "必需参数:"
    echo "  -H, --host HOST       远程主机地址"
    echo ""
    echo "可选参数:"
    echo "  -u, --user USER       SSH用户名 [默认: root]"
    echo "  -k, --key PATH        SSH私钥路径"
    echo "  -p, --port PORT       SSH端口 [默认: 22]"
    echo "  --install-k3s         安装k3s"
    echo "  --deploy-app          部署应用"
    echo "  --all                 完整部署"
    echo "  --check               检查连接"
    echo "  -h, --help            显示帮助"
    echo ""
    echo "示例:"
    echo "  $0 -H ************* --check"
    echo "  $0 -H server.com -u ubuntu --all"
    echo "  $0 -H ********* -k ~/.ssh/id_rsa --deploy-app"
}

# 解析参数
INSTALL_K3S=false
DEPLOY_APP=false
CHECK_ONLY=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -H|--host) REMOTE_HOST="$2"; shift 2 ;;
        -u|--user) REMOTE_USER="$2"; shift 2 ;;
        -k|--key) SSH_KEY="$2"; shift 2 ;;
        -p|--port) SSH_PORT="$2"; shift 2 ;;
        --install-k3s) INSTALL_K3S=true; shift ;;
        --deploy-app) DEPLOY_APP=true; shift ;;
        --all) INSTALL_K3S=true; DEPLOY_APP=true; shift ;;
        --check) CHECK_ONLY=true; shift ;;
        -h|--help) show_help; exit 0 ;;
        *) log_error "未知选项: $1"; show_help; exit 1 ;;
    esac
done

# 验证参数
if [[ -z "$REMOTE_HOST" ]]; then
    log_error "必须指定远程主机地址 (-H)"
    show_help
    exit 1
fi

# 构建SSH命令
build_ssh_cmd() {
    local cmd="ssh -p $SSH_PORT"
    [[ -n "$SSH_KEY" ]] && cmd="$cmd -i $SSH_KEY"
    cmd="$cmd -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null"
    cmd="$cmd $REMOTE_USER@$REMOTE_HOST"
    echo "$cmd"
}

build_scp_cmd() {
    local cmd="scp -P $SSH_PORT"
    [[ -n "$SSH_KEY" ]] && cmd="$cmd -i $SSH_KEY"
    cmd="$cmd -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null"
    echo "$cmd"
}

SSH_CMD=$(build_ssh_cmd)
SCP_CMD=$(build_scp_cmd)

# 检查连接
check_connection() {
    log_info "检查SSH连接到 $REMOTE_HOST..."
    if $SSH_CMD "echo 'SSH连接成功'" >/dev/null 2>&1; then
        log_success "SSH连接正常"
        
        # 显示系统信息
        log_info "远程系统信息:"
        $SSH_CMD "echo '  OS: '$(cat /etc/os-release | grep PRETTY_NAME | cut -d'=' -f2 | tr -d '\"')"
        $SSH_CMD "echo '  内存: '$(free -h | awk '/^Mem:/{print \$2}')"
        $SSH_CMD "echo '  磁盘: '$(df -h / | awk 'NR==2{print \$4\" 可用\"}')"
        return 0
    else
        log_error "SSH连接失败"
        return 1
    fi
}

# 安装k3s
install_k3s() {
    log_info "在远程服务器安装k3s..."
    
    # 检查是否已安装
    if $SSH_CMD "systemctl is-active --quiet k3s" 2>/dev/null; then
        log_success "k3s已安装并运行"
        return 0
    fi
    
    # 安装k3s
    log_info "下载并安装k3s..."
    $SSH_CMD "curl -sfL https://get.k3s.io | sh -s - --write-kubeconfig-mode 644"
    
    # 等待启动
    log_info "等待k3s启动..."
    sleep 15
    
    # 验证安装
    if $SSH_CMD "systemctl is-active --quiet k3s"; then
        log_success "k3s安装成功"
        $SSH_CMD "kubectl get nodes"
    else
        log_error "k3s安装失败"
        return 1
    fi
}

# 上传项目代码
upload_project() {
    log_info "上传项目代码到远程服务器..."
    
    # 创建远程目录
    $SSH_CMD "mkdir -p /opt/app"
    
    # 创建项目压缩包
    local temp_archive="/tmp/app-deploy-$(date +%s).tar.gz"
    log_info "创建项目压缩包..."
    tar --exclude='.git' \
        --exclude='node_modules' \
        --exclude='logs' \
        --exclude='*.log' \
        --exclude='deploy-backup-*' \
        -czf "$temp_archive" -C "$(dirname "$0")/../.." .
    
    # 上传压缩包
    log_info "上传项目文件..."
    $SCP_CMD "$temp_archive" "$REMOTE_USER@$REMOTE_HOST:/opt/app/project.tar.gz"
    
    # 解压项目文件
    log_info "解压项目文件..."
    $SSH_CMD "cd /opt/app && tar -xzf project.tar.gz && rm project.tar.gz"
    
    # 清理本地临时文件
    rm -f "$temp_archive"
    
    log_success "项目代码上传完成"
}

# 构建应用镜像
build_app_image() {
    log_info "构建应用镜像..."
    
    # 检查是否有buildah
    if ! $SSH_CMD "command -v buildah" >/dev/null 2>&1; then
        log_info "安装buildah..."
        $SSH_CMD "apt update && apt install -y buildah"
    fi
    
    # 上传项目代码
    upload_project
    
    # 构建镜像
    log_info "使用buildah构建镜像..."
    $SSH_CMD "cd /opt/app && buildah build -t docker.io/library/$APP_NAME:latest -f Dockerfile ."
    
    # 导入到k3s
    log_info "导入镜像到k3s..."
    $SSH_CMD "buildah push docker.io/library/$APP_NAME:latest oci-archive:/tmp/$APP_NAME.tar:docker.io/library/$APP_NAME:latest"
    $SSH_CMD "k3s ctr images import /tmp/$APP_NAME.tar"
    $SSH_CMD "rm -f /tmp/$APP_NAME.tar"
    
    log_success "镜像构建完成"
}

# 部署应用
deploy_app() {
    log_info "部署应用到远程k3s..."
    
    # 检查k3s状态
    if ! $SSH_CMD "systemctl is-active --quiet k3s" 2>/dev/null; then
        log_error "k3s未运行，请先安装k3s"
        return 1
    fi
    
    # 构建镜像
    build_app_image
    
    # 创建临时目录
    local temp_dir="/tmp/k8s-deploy-$(date +%s)"
    $SSH_CMD "mkdir -p $temp_dir"
    
    # 生成k8s资源文件
    log_info "生成Kubernetes资源文件..."
    
    # 创建namespace
    $SSH_CMD "cat > $temp_dir/namespace.yaml << 'EOF'
apiVersion: v1
kind: Namespace
metadata:
  name: $NAMESPACE
  labels:
    name: $NAMESPACE
EOF"

    # 创建configmap
    $SSH_CMD "cat > $temp_dir/configmap.yaml << 'EOF'
apiVersion: v1
kind: ConfigMap
metadata:
  name: $APP_NAME-config
  namespace: $NAMESPACE
data:
  APP_ENV: \"production\"
  LOG_LEVEL: \"info\"
  DATABASE_HOST: \"postgres-service\"
  DATABASE_PORT: \"5432\"
  DATABASE_NAME: \"$APP_NAME\"
  REDIS_HOST: \"redis-service\"
  REDIS_PORT: \"6379\"
EOF"

    # 创建secret
    $SSH_CMD "cat > $temp_dir/secret.yaml << 'EOF'
apiVersion: v1
kind: Secret
metadata:
  name: $APP_NAME-secret
  namespace: $NAMESPACE
type: Opaque
data:
  DATABASE_USER: $(echo -n 'postgres' | base64 -w 0)
  DATABASE_PASSWORD: $(echo -n 'password123' | base64 -w 0)
  JWT_SECRET: $(echo -n 'your-jwt-secret-key' | base64 -w 0)
EOF"

    # 创建postgres部署
    $SSH_CMD "cat > $temp_dir/postgres.yaml << 'EOF'
apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgres
  namespace: $NAMESPACE
spec:
  replicas: 1
  selector:
    matchLabels:
      app: postgres
  template:
    metadata:
      labels:
        app: postgres
    spec:
      containers:
      - name: postgres
        image: postgres:15-alpine
        env:
        - name: POSTGRES_DB
          value: \"$APP_NAME\"
        - name: POSTGRES_USER
          valueFrom:
            secretKeyRef:
              name: $APP_NAME-secret
              key: DATABASE_USER
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: $APP_NAME-secret
              key: DATABASE_PASSWORD
        ports:
        - containerPort: 5432
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
        readinessProbe:
          exec:
            command: [\"pg_isready\", \"-U\", \"postgres\"]
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: postgres-storage
        emptyDir: {}
---
apiVersion: v1
kind: Service
metadata:
  name: postgres-service
  namespace: $NAMESPACE
spec:
  selector:
    app: postgres
  ports:
  - port: 5432
    targetPort: 5432
EOF"

    # 创建redis部署
    $SSH_CMD "cat > $temp_dir/redis.yaml << 'EOF'
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
  namespace: $NAMESPACE
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
    spec:
      containers:
      - name: redis
        image: redis:7-alpine
        ports:
        - containerPort: 6379
        readinessProbe:
          exec:
            command: [\"redis-cli\", \"ping\"]
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: redis-service
  namespace: $NAMESPACE
spec:
  selector:
    app: redis
  ports:
  - port: 6379
    targetPort: 6379
EOF"

    # 创建应用部署
    $SSH_CMD "cat > $temp_dir/app.yaml << 'EOF'
apiVersion: apps/v1
kind: Deployment
metadata:
  name: $APP_NAME
  namespace: $NAMESPACE
spec:
  replicas: 1
  selector:
    matchLabels:
      app: $APP_NAME
  template:
    metadata:
      labels:
        app: $APP_NAME
    spec:
      containers:
      - name: $APP_NAME
        image: docker.io/library/$APP_NAME:latest
        imagePullPolicy: Never
        ports:
        - containerPort: 8080
        env:
        - name: DATABASE_URL
          value: \"postgresql://\$(DATABASE_USER):\$(DATABASE_PASSWORD)@\$(DATABASE_HOST):\$(DATABASE_PORT)/\$(DATABASE_NAME)?sslmode=disable\"
        envFrom:
        - configMapRef:
            name: $APP_NAME-config
        - secretRef:
            name: $APP_NAME-secret
        readinessProbe:
          httpGet:
            path: /api/health
            port: 8080
          initialDelaySeconds: 10
          periodSeconds: 5
        livenessProbe:
          httpGet:
            path: /api/health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        resources:
          requests:
            memory: \"128Mi\"
            cpu: \"100m\"
          limits:
            memory: \"512Mi\"
            cpu: \"500m\"
---
apiVersion: v1
kind: Service
metadata:
  name: $APP_NAME-service
  namespace: $NAMESPACE
spec:
  selector:
    app: $APP_NAME
  ports:
  - name: http
    port: 8080
    targetPort: 8080
  type: ClusterIP
EOF"

    # 应用资源
    log_info "应用Kubernetes资源..."
    $SSH_CMD "kubectl apply -f $temp_dir/"
    
    # 等待部署完成
    log_info "等待部署完成..."
    $SSH_CMD "kubectl wait --for=condition=ready pod -l app=postgres -n $NAMESPACE --timeout=300s" || log_warning "PostgreSQL启动超时"
    $SSH_CMD "kubectl wait --for=condition=ready pod -l app=redis -n $NAMESPACE --timeout=300s" || log_warning "Redis启动超时"
    $SSH_CMD "kubectl wait --for=condition=available deployment/$APP_NAME -n $NAMESPACE --timeout=600s" || log_warning "应用启动超时"
    
    # 显示状态
    log_info "部署状态:"
    $SSH_CMD "kubectl get all -n $NAMESPACE"
    
    # 清理临时文件
    $SSH_CMD "rm -rf $temp_dir"
    
    log_success "应用部署完成！"
    echo ""
    echo "📋 访问方式："
    echo "  1. SSH到服务器: $SSH_CMD"
    echo "  2. 端口转发: kubectl port-forward -n $NAMESPACE svc/$APP_NAME-service 8080:8080"
    echo "  3. 本地访问: http://localhost:8080"
}

# 主逻辑
main() {
    log_info "远程部署配置:"
    log_info "  主机: $REMOTE_HOST"
    log_info "  用户: $REMOTE_USER"
    log_info "  端口: $SSH_PORT"
    log_info "  密钥: ${SSH_KEY:-默认}"
    echo ""
    
    # 检查连接
    if ! check_connection; then
        exit 1
    fi
    
    if [[ "$CHECK_ONLY" == "true" ]]; then
        log_success "连接检查完成"
        exit 0
    fi
    
    # 执行操作
    [[ "$INSTALL_K3S" == "true" ]] && install_k3s
    [[ "$DEPLOY_APP" == "true" ]] && deploy_app
    
    log_success "远程部署完成！"
}

# 执行主函数
main