#!/bin/bash

# 远程部署便捷脚本
set -e

echo "🚀 远程部署脚本"

# 切换到脚本目录
cd "$(dirname "$0")"

# 检查 Ansible
if ! command -v ansible-playbook &> /dev/null; then
    echo "❌ Ansible 未安装"
    echo "💡 安装方法："
    echo "  Ubuntu/Debian: sudo apt install ansible"
    echo "  CentOS/RHEL: sudo yum install ansible"
    echo "  macOS: brew install ansible"
    echo "  Python: pip install ansible"
    exit 1
fi

# 检查清单文件
if [[ ! -f "inventory.yml" ]]; then
    echo "❌ 清单文件不存在: inventory.yml"
    echo "💡 请复制并配置清单文件："
    echo "  cp inventory.yml.example inventory.yml"
    echo "  vim inventory.yml"
    exit 1
fi

# 显示帮助
show_help() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help        显示帮助"
    echo "  --install-k3s     安装 k3s 集群"
    echo "  --deploy-app      部署应用"
    echo "  --all             执行完整部署（安装k3s + 部署应用）"
    echo "  --check           检查连接"
    echo ""
    echo "示例:"
    echo "  $0 --check           # 检查服务器连接"
    echo "  $0 --install-k3s     # 安装 k3s"
    echo "  $0 --deploy-app      # 部署应用"
    echo "  $0 --all             # 完整部署"
}

# 检查连接
check_connection() {
    echo "🔍 检查服务器连接..."
    ansible all -i inventory.yml -m ping
}

# 安装 k3s
install_k3s() {
    echo "📦 安装 k3s 集群..."
    ansible-playbook -i inventory.yml install-k3s.yml
}

# 部署应用
deploy_app() {
    echo "🚀 部署应用..."
    
    # 检查是否需要安装 kubernetes.core collection
    if ! ansible-galaxy collection list | grep -q kubernetes.core; then
        echo "📦 安装 Ansible Kubernetes collection..."
        ansible-galaxy collection install kubernetes.core
    fi
    
    ansible-playbook -i inventory.yml deploy.yml
}

# 解析参数
case "${1:-}" in
    -h|--help)
        show_help
        exit 0
        ;;
    --check)
        check_connection
        ;;
    --install-k3s)
        install_k3s
        ;;
    --deploy-app)
        deploy_app
        ;;
    --all)
        check_connection
        install_k3s
        deploy_app
        ;;
    "")
        echo "❌ 请指定操作选项"
        show_help
        exit 1
        ;;
    *)
        echo "❌ 未知选项: $1"
        show_help
        exit 1
        ;;
esac

echo "✅ 操作完成！"