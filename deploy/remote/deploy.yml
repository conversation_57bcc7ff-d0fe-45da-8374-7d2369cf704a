---
# 应用部署 Playbook
- name: Deploy application to k3s
  hosts: k3s_server
  become: yes
  gather_facts: no
  
  vars:
    manifests_dir: "/tmp/k8s-manifests"
    
  tasks:
    - name: Create manifests directory
      file:
        path: "{{ manifests_dir }}"
        state: directory
        mode: '0755'

    - name: Copy k8s manifests
      template:
        src: "{{ item }}"
        dest: "{{ manifests_dir }}/{{ item | basename | regex_replace('\\.j2$', '') }}"
        mode: '0644'
      with_fileglob:
        - "templates/*.yaml.j2"

    - name: Create namespace
      kubernetes.core.k8s:
        name: "{{ app_namespace }}"
        api_version: v1
        kind: Namespace
        state: present

    - name: Apply ConfigMap
      kubernetes.core.k8s:
        state: present
        definition:
          apiVersion: v1
          kind: ConfigMap
          metadata:
            name: "{{ app_name }}-config"
            namespace: "{{ app_namespace }}"
          data:
            APP_ENV: "production"
            LOG_LEVEL: "info"
            DATABASE_HOST: "postgres-service"
            DATABASE_PORT: "5432"
            DATABASE_NAME: "{{ database_name }}"
            REDIS_HOST: "redis-service"
            REDIS_PORT: "6379"

    - name: Apply Secret
      kubernetes.core.k8s:
        state: present
        definition:
          apiVersion: v1
          kind: Secret
          metadata:
            name: "{{ app_name }}-secret"
            namespace: "{{ app_namespace }}"
          type: Opaque
          data:
            DATABASE_USER: "{{ database_user | b64encode }}"
            DATABASE_PASSWORD: "{{ database_password | b64encode }}"
            JWT_SECRET: "{{ jwt_secret | b64encode }}"

    - name: Apply all manifests
      shell: kubectl apply -f {{ manifests_dir }}/
      register: apply_result
      
    - name: Wait for PostgreSQL to be ready
      kubernetes.core.k8s_info:
        api_version: apps/v1
        kind: Deployment
        name: postgres
        namespace: "{{ app_namespace }}"
        wait: true
        wait_condition:
          type: Available
          status: "True"
        wait_timeout: 300

    - name: Wait for Redis to be ready
      kubernetes.core.k8s_info:
        api_version: apps/v1
        kind: Deployment
        name: redis
        namespace: "{{ app_namespace }}"
        wait: true
        wait_condition:
          type: Available
          status: "True"
        wait_timeout: 300

    - name: Wait for application to be ready
      kubernetes.core.k8s_info:
        api_version: apps/v1
        kind: Deployment
        name: "{{ app_name }}"
        namespace: "{{ app_namespace }}"
        wait: true
        wait_condition:
          type: Available
          status: "True"
        wait_timeout: 600

    - name: Get deployment status
      shell: kubectl get all -n {{ app_namespace }}
      register: status_output
      
    - name: Display deployment status
      debug:
        msg: "{{ status_output.stdout_lines }}"

    - name: Get service information
      shell: kubectl get svc -n {{ app_namespace }}
      register: service_output
      
    - name: Display service information
      debug:
        msg: "{{ service_output.stdout_lines }}"

    - name: Display access information
      debug:
        msg: |
          部署完成！
          
          访问方式：
          1. 端口转发: kubectl port-forward -n {{ app_namespace }} svc/{{ app_name }}-service 8080:8080
          2. 本地访问: http://localhost:8080
          
          管理命令：
          - 查看状态: kubectl get all -n {{ app_namespace }}
          - 查看日志: kubectl logs -f -l app={{ app_name }} -n {{ app_namespace }}
          - 删除部署: kubectl delete namespace {{ app_namespace }}