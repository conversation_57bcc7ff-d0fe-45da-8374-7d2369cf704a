---
# k3s 安装 Playbook
- name: Install k3s cluster
  hosts: k3s_cluster
  become: yes
  gather_facts: yes
  
  tasks:
    - name: Update system packages
      package:
        name: "*"
        state: latest
      when: ansible_os_family == "RedHat"
      
    - name: Update system packages (Debian/Ubuntu)
      apt:
        upgrade: dist
        update_cache: yes
      when: ansible_os_family == "Debian"

    - name: Install required packages
      package:
        name:
          - curl
          - wget
        state: present

- name: Install k3s server
  hosts: k3s_server
  become: yes
  serial: 1
  
  tasks:
    - name: Check if k3s is already installed
      systemd:
        name: k3s
      register: k3s_service
      ignore_errors: yes

    - name: Install k3s server
      shell: |
        curl -sfL https://get.k3s.io | INSTALL_K3S_VERSION={{ k3s_version }} sh -s - server \
          --token {{ k3s_token }} \
          --write-kubeconfig-mode 644
      when: k3s_service.status.ActiveState != "active"

    - name: Wait for k3s to be ready
      wait_for:
        port: 6443
        host: "{{ ansible_default_ipv4.address }}"
        delay: 10
        timeout: 300

    - name: Get k3s kubeconfig
      fetch:
        src: /etc/rancher/k3s/k3s.yaml
        dest: ./kubeconfig
        flat: yes
      run_once: true

- name: Install k3s agents
  hosts: k3s_agent
  become: yes
  
  tasks:
    - name: Check if k3s agent is already installed
      systemd:
        name: k3s-agent
      register: k3s_agent_service
      ignore_errors: yes

    - name: Install k3s agent
      shell: |
        curl -sfL https://get.k3s.io | INSTALL_K3S_VERSION={{ k3s_version }} sh -s - agent \
          --server https://{{ hostvars[groups['k3s_server'][0]]['ansible_default_ipv4']['address'] }}:6443 \
          --token {{ k3s_token }}
      when: k3s_agent_service.status.ActiveState != "active"

- name: Verify k3s installation
  hosts: k3s_server
  become: yes
  
  tasks:
    - name: Check k3s nodes
      shell: kubectl get nodes
      register: nodes_output
      
    - name: Display nodes
      debug:
        msg: "{{ nodes_output.stdout_lines }}"