#!/bin/bash

# k3s 镜像构建脚本
set -e

echo "🔨 构建应用镜像..."

# 切换到项目根目录
cd "$(dirname "$0")/../.."

# 检查 Docker 或其他构建工具
if command -v docker &> /dev/null; then
    BUILD_TOOL="docker"
elif command -v podman &> /dev/null; then
    BUILD_TOOL="podman"
elif command -v buildah &> /dev/null; then
    BUILD_TOOL="buildah"
else
    echo "❌ 未找到容器构建工具 (docker/podman/buildah)"
    echo "💡 安装建议："
    echo "  - Docker: curl -fsSL https://get.docker.com | sh"
    echo "  - Podman: sudo apt install podman"
    exit 1
fi

echo "📦 使用 $BUILD_TOOL 构建镜像..."

# 获取版本信息
VERSION=$(git describe --tags --always --dirty 2>/dev/null || echo "local-$(date +%Y%m%d-%H%M%S)")

# 构建后端镜像
echo "🔧 构建后端镜像..."
$BUILD_TOOL build --no-cache -t "docker.io/library/app:$VERSION" -t "docker.io/library/app:latest" .

# 如果使用 Docker，需要导入到 k3s
if [[ "$BUILD_TOOL" == "docker" ]]; then
    echo "📥 导入镜像到 k3s..."
    docker save docker.io/library/app:latest | sudo k3s ctr images import -
fi

# 如果使用 Podman，需要导入到 k3s
if [[ "$BUILD_TOOL" == "podman" ]]; then
    echo "📥 导入镜像到 k3s..."
    podman save docker.io/library/app:latest | sudo k3s ctr images import -
fi

# 如果使用 Buildah，需要导入到 k3s
if [[ "$BUILD_TOOL" == "buildah" ]]; then
    echo "📥 导入镜像到 k3s..."
    buildah push docker.io/library/app:latest oci-archive:/tmp/app.tar:docker.io/library/app:latest
    sudo k3s ctr images import /tmp/app.tar
    rm -f /tmp/app.tar
fi

echo "✅ 镜像构建完成！"
echo "📋 镜像信息："
echo "  - 名称: app:$VERSION, app:latest"
echo "  - 构建工具: $BUILD_TOOL"

# 验证镜像
echo "🔍 验证镜像..."
sudo k3s ctr images list | grep app || echo "⚠️  镜像验证失败"