#!/bin/bash

# 完整重新部署脚本 - 解决DNS问题
set -e

echo "🔄 完整重新部署应用..."

# 切换到脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 检查 k3s 是否运行
if ! systemctl is-active --quiet k3s; then
    echo "❌ k3s 未运行，请先安装: ./install-k3s.sh"
    exit 1
fi

# 检查 kubectl
if ! command -v kubectl &> /dev/null; then
    echo "❌ kubectl 未找到"
    exit 1
fi

echo "🗑️  清理现有部署..."
# 删除现有的namespace（这会删除所有资源）
kubectl delete namespace app --ignore-not-found=true

# 等待namespace完全删除
echo "⏳ 等待资源清理完成..."
while kubectl get namespace app &> /dev/null; do
    echo "等待namespace删除..."
    sleep 2
done

echo "🔧 重启CoreDNS以确保DNS正常工作..."
kubectl rollout restart deployment/coredns -n kube-system
kubectl wait --for=condition=available deployment/coredns -n kube-system --timeout=60s

echo "📦 重新部署应用..."

# 创建命名空间
kubectl apply -f manifests/namespace.yaml

# 等待namespace就绪
sleep 2

# 应用配置
kubectl apply -f manifests/configmap.yaml
kubectl apply -f manifests/secret.yaml

# 部署数据库
kubectl apply -f manifests/postgres.yaml
kubectl apply -f manifests/redis.yaml

# 等待数据库就绪
echo "⏳ 等待PostgreSQL就绪..."
kubectl wait --for=condition=ready pod -l app=postgres -n app --timeout=300s

echo "⏳ 等待Redis就绪..."
kubectl wait --for=condition=ready pod -l app=redis -n app --timeout=300s

# 验证服务DNS解析
echo "🔍 验证DNS解析..."
kubectl run dns-test --image=busybox -n app --rm -it --restart=Never -- nslookup postgres-service || {
    echo "⚠️  DNS解析失败，但继续部署（应用有重试机制）"
}

# 部署应用
kubectl apply -f manifests/deployment.yaml
kubectl apply -f manifests/service.yaml

# 等待应用就绪
echo "⏳ 等待应用就绪..."
kubectl wait --for=condition=available deployment/app -n app --timeout=300s

echo "✅ 重新部署完成！"
echo ""
echo "📋 访问信息："
echo "  - 端口转发: kubectl port-forward -n app svc/app-service 8080:8080"
echo "  - 本地访问: http://localhost:8080"
echo ""
echo "📊 管理命令："
echo "  - 查看状态: kubectl get all -n app"
echo "  - 查看日志: kubectl logs -f -l app=app -n app"
echo "  - 删除部署: kubectl delete namespace app"
