#!/bin/bash

# K3s 端口映射管理脚本
# 用法: ./port-mapping.sh [enable|disable|status|custom] [local|remote]

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
MANIFESTS_DIR="$SCRIPT_DIR/manifests"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查 kubectl 是否可用
check_kubectl() {
    if ! command -v kubectl &> /dev/null; then
        print_error "kubectl 未安装或不在 PATH 中"
        exit 1
    fi
    
    # 检查集群连接
    if ! kubectl cluster-info &> /dev/null; then
        print_error "无法连接到 Kubernetes 集群"
        print_info "请确保 k3s 已启动并且 kubeconfig 配置正确"
        exit 1
    fi
}

# 启用端口映射
enable_port_mapping() {
    local deployment_type=${1:-local}
    
    print_info "启用 $deployment_type 部署的端口映射..."
    
    # 应用 NodePort 服务
    print_info "创建 NodePort 服务..."
    
    # PostgreSQL NodePort
    kubectl apply -f - <<EOF
apiVersion: v1
kind: Service
metadata:
  name: postgres-nodeport
  namespace: app
  labels:
    app: postgres
    service-type: external
spec:
  selector:
    app: postgres
  ports:
  - name: postgres
    port: 5432
    targetPort: 5432
    nodePort: 30432
  type: NodePort
EOF

    # Redis NodePort
    kubectl apply -f - <<EOF
apiVersion: v1
kind: Service
metadata:
  name: redis-nodeport
  namespace: app
  labels:
    app: redis
    service-type: external
spec:
  selector:
    app: redis
  ports:
  - name: redis
    port: 6379
    targetPort: 6379
    nodePort: 30379
  type: NodePort
EOF

    # Backend NodePort
    kubectl apply -f - <<EOF
apiVersion: v1
kind: Service
metadata:
  name: app-nodeport
  namespace: app
  labels:
    app: app
    service-type: external
spec:
  selector:
    app: app
  ports:
  - name: http
    port: 8080
    targetPort: 8080
    nodePort: 30080
  type: NodePort
EOF

    print_success "端口映射已启用"
    print_info "服务访问地址："
    
    if [ "$deployment_type" = "local" ]; then
        print_info "PostgreSQL: localhost:30432"
        print_info "Redis: localhost:30379"
        print_info "Backend API: localhost:30080"
    else
        local_ip=$(kubectl get nodes -o jsonpath='{.items[0].status.addresses[?(@.type=="InternalIP")].address}')
        print_info "PostgreSQL: $local_ip:30432"
        print_info "Redis: $local_ip:30379"
        print_info "Backend API: $local_ip:30080"
    fi
}

# 禁用端口映射
disable_port_mapping() {
    print_info "禁用端口映射..."
    
    # 删除 NodePort 服务
    kubectl delete service postgres-nodeport -n app --ignore-not-found=true
    kubectl delete service redis-nodeport -n app --ignore-not-found=true
    kubectl delete service app-nodeport -n app --ignore-not-found=true
    
    print_success "端口映射已禁用"
    print_info "服务现在只能在集群内部访问"
}

# 自定义端口映射
custom_port_mapping() {
    local deployment_type=${1:-local}
    
    print_info "配置自定义端口映射..."
    
    echo "请输入端口配置（直接回车使用默认值）："
    
    read -p "PostgreSQL 外部端口 [30432]: " postgres_port
    read -p "Redis 外部端口 [30379]: " redis_port
    read -p "Backend 外部端口 [30080]: " backend_port
    
    # 设置默认值
    postgres_port=${postgres_port:-30432}
    redis_port=${redis_port:-30379}
    backend_port=${backend_port:-30080}
    
    # 验证端口范围（NodePort 范围：30000-32767）
    validate_nodeport() {
        local port=$1
        if [[ $port -lt 30000 || $port -gt 32767 ]]; then
            print_error "端口 $port 超出 NodePort 范围 (30000-32767)"
            return 1
        fi
    }
    
    validate_nodeport $postgres_port || exit 1
    validate_nodeport $redis_port || exit 1
    validate_nodeport $backend_port || exit 1
    
    # 应用自定义 NodePort 服务
    print_info "应用自定义端口配置..."
    
    # PostgreSQL
    kubectl apply -f - <<EOF
apiVersion: v1
kind: Service
metadata:
  name: postgres-nodeport
  namespace: app
  labels:
    app: postgres
    service-type: external
spec:
  selector:
    app: postgres
  ports:
  - name: postgres
    port: 5432
    targetPort: 5432
    nodePort: $postgres_port
  type: NodePort
EOF

    # Redis
    kubectl apply -f - <<EOF
apiVersion: v1
kind: Service
metadata:
  name: redis-nodeport
  namespace: app
  labels:
    app: redis
    service-type: external
spec:
  selector:
    app: redis
  ports:
  - name: redis
    port: 6379
    targetPort: 6379
    nodePort: $redis_port
  type: NodePort
EOF

    # Backend
    kubectl apply -f - <<EOF
apiVersion: v1
kind: Service
metadata:
  name: app-nodeport
  namespace: app
  labels:
    app: app
    service-type: external
spec:
  selector:
    app: app
  ports:
  - name: http
    port: 8080
    targetPort: 8080
    nodePort: $backend_port
  type: NodePort
EOF

    print_success "自定义端口映射已配置"
    
    if [ "$deployment_type" = "local" ]; then
        print_info "PostgreSQL: localhost:$postgres_port"
        print_info "Redis: localhost:$redis_port"
        print_info "Backend API: localhost:$backend_port"
    else
        local_ip=$(kubectl get nodes -o jsonpath='{.items[0].status.addresses[?(@.type=="InternalIP")].address}')
        print_info "PostgreSQL: $local_ip:$postgres_port"
        print_info "Redis: $local_ip:$redis_port"
        print_info "Backend API: $local_ip:$backend_port"
    fi
}

# 显示当前状态
show_status() {
    local deployment_type=${1:-local}
    
    print_info "当前端口映射状态："
    echo "----------------------------------------"
    
    # 检查 NodePort 服务
    local postgres_nodeport=$(kubectl get service postgres-nodeport -n app -o jsonpath='{.spec.ports[0].nodePort}' 2>/dev/null || echo "")
    local redis_nodeport=$(kubectl get service redis-nodeport -n app -o jsonpath='{.spec.ports[0].nodePort}' 2>/dev/null || echo "")
    local app_nodeport=$(kubectl get service app-nodeport -n app -o jsonpath='{.spec.ports[0].nodePort}' 2>/dev/null || echo "")
    
    if [ "$deployment_type" = "local" ]; then
        local host="localhost"
    else
        local host=$(kubectl get nodes -o jsonpath='{.items[0].status.addresses[?(@.type=="InternalIP")].address}')
    fi
    
    echo "PostgreSQL: ${postgres_nodeport:+$host:$postgres_nodeport}${postgres_nodeport:-[禁用]}"
    echo "Redis: ${redis_nodeport:+$host:$redis_nodeport}${redis_nodeport:-[禁用]}"
    echo "Backend API: ${app_nodeport:+$host:$app_nodeport}${app_nodeport:-[禁用]}"
    
    echo "----------------------------------------"
    
    # 显示 Pod 状态
    print_info "Pod 状态："
    kubectl get pods -n app -o wide 2>/dev/null || print_warning "无法获取 Pod 状态"
    
    echo "----------------------------------------"
    
    # 显示所有服务
    print_info "所有服务："
    kubectl get services -n app 2>/dev/null || print_warning "无法获取服务状态"
}

# 测试连接
test_connections() {
    local deployment_type=${1:-local}
    
    print_info "测试服务连接..."
    
    if [ "$deployment_type" = "local" ]; then
        local host="localhost"
    else
        local host=$(kubectl get nodes -o jsonpath='{.items[0].status.addresses[?(@.type=="InternalIP")].address}')
    fi
    
    # 测试 PostgreSQL
    local postgres_port=$(kubectl get service postgres-nodeport -n app -o jsonpath='{.spec.ports[0].nodePort}' 2>/dev/null || echo "")
    if [ -n "$postgres_port" ]; then
        if command -v pg_isready &> /dev/null; then
            if pg_isready -h $host -p $postgres_port -U driving_nav_user &> /dev/null; then
                print_success "PostgreSQL 连接正常 ($host:$postgres_port)"
            else
                print_warning "PostgreSQL 连接失败 ($host:$postgres_port)"
            fi
        else
            print_info "PostgreSQL 端口开放: $host:$postgres_port (需要 postgresql-client 测试连接)"
        fi
    fi
    
    # 测试 Redis
    local redis_port=$(kubectl get service redis-nodeport -n app -o jsonpath='{.spec.ports[0].nodePort}' 2>/dev/null || echo "")
    if [ -n "$redis_port" ]; then
        if command -v redis-cli &> /dev/null; then
            if redis-cli -h $host -p $redis_port ping &> /dev/null; then
                print_success "Redis 连接正常 ($host:$redis_port)"
            else
                print_warning "Redis 连接失败 ($host:$redis_port)"
            fi
        else
            print_info "Redis 端口开放: $host:$redis_port (需要 redis-tools 测试连接)"
        fi
    fi
    
    # 测试 Backend API
    local app_port=$(kubectl get service app-nodeport -n app -o jsonpath='{.spec.ports[0].nodePort}' 2>/dev/null || echo "")
    if [ -n "$app_port" ]; then
        if command -v curl &> /dev/null; then
            if curl -s --connect-timeout 5 http://$host:$app_port/api/health &> /dev/null; then
                print_success "Backend API 连接正常 ($host:$app_port)"
            else
                print_warning "Backend API 连接失败 ($host:$app_port)"
            fi
        else
            print_info "Backend API 端口开放: $host:$app_port (需要 curl 测试连接)"
        fi
    fi
}

# 显示帮助信息
show_help() {
    echo "K3s 端口映射管理脚本"
    echo ""
    echo "用法: $0 [命令] [部署类型]"
    echo ""
    echo "命令:"
    echo "  enable    启用默认端口映射"
    echo "  disable   禁用所有端口映射"
    echo "  custom    配置自定义端口映射"
    echo "  status    显示当前端口映射状态"
    echo "  test      测试服务连接"
    echo "  help      显示此帮助信息"
    echo ""
    echo "部署类型:"
    echo "  local     本地部署 (默认)"
    echo "  remote    远程部署"
    echo ""
    echo "默认端口映射:"
    echo "  PostgreSQL: 30432"
    echo "  Redis: 30379"
    echo "  Backend API: 30080"
    echo ""
    echo "示例:"
    echo "  $0 enable local        # 启用本地部署端口映射"
    echo "  $0 disable             # 禁用端口映射"
    echo "  $0 custom remote       # 配置远程部署自定义端口"
    echo "  $0 status              # 查看当前状态"
    echo "  $0 test local          # 测试本地连接"
}

# 检查依赖
check_kubectl

# 主逻辑
command=${1:-help}
deployment_type=${2:-local}

case "$command" in
    "enable")
        enable_port_mapping $deployment_type
        ;;
    "disable")
        disable_port_mapping
        ;;
    "custom")
        custom_port_mapping $deployment_type
        ;;
    "status")
        show_status $deployment_type
        ;;
    "test")
        test_connections $deployment_type
        ;;
    "help"|*)
        show_help
        ;;
esac
