apiVersion: v1
kind: Service
metadata:
  name: app-service
  namespace: app
  labels:
    app: app
spec:
  selector:
    app: app
  ports:
  - name: http
    port: 8080
    targetPort: 8080
  type: ClusterIP

---
# NodePort 服务用于外部访问（可选启用）
apiVersion: v1
kind: Service
metadata:
  name: app-nodeport
  namespace: app
  labels:
    app: app
    service-type: external
spec:
  selector:
    app: app
  ports:
  - name: http
    port: 8080
    targetPort: 8080
    nodePort: 30080  # 外部访问端口 (映射到主机8080)
  type: NodePort