apiVersion: v1
kind: ConfigMap
metadata:
  name: app-config
  namespace: app
  annotations:
    description: "Updated to use service names for better DNS resolution"
data:
  APP_ENV: "production"
  LOG_LEVEL: "info"
  # 使用完整的数据库URL，避免DNS解析问题
  DATABASE_URL: "postgresql://driving_nav_user:<EMAIL>:5432/driving_navigation?sslmode=disable"
  DB_HOST: "postgres-service"
  DB_PORT: "5432"
  DB_NAME: "driving_navigation"
  DB_SSL_MODE: "disable"
  REDIS_HOST: "redis-service"
  REDIS_PORT: "6379"
  # 高德地图API配置
  AMAP_API_KEY: "9e471ba01ae18f216cd0fb84032ec7e2"
  AMAP_WEB_SERVICE_KEY: "9e471ba01ae18f216cd0fb84032ec7e2"
  AMAP_BASE_URL: "https://restapi.amap.com"
  AMAP_TIMEOUT: "30s"
  AMAP_RATE_LIMIT: "10"
  AMAP_BURST: "20"