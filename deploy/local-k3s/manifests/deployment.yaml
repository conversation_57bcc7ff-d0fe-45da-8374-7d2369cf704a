apiVersion: apps/v1
kind: Deployment
metadata:
  name: app
  namespace: app
spec:
  replicas: 1
  selector:
    matchLabels:
      app: app
  template:
    metadata:
      labels:
        app: app
    spec:
      # 添加初始化容器等待数据库服务可用
      initContainers:
      - name: wait-for-postgres
        image: busybox:1.35
        command: ['sh', '-c']
        args:
        - |
          echo "等待 PostgreSQL 服务可用..."
          until nc -z postgres-service 5432; do
            echo "PostgreSQL 服务不可用，等待..."
            sleep 2
          done
          echo "PostgreSQL 服务已可用"
      - name: wait-for-redis
        image: busybox:1.35
        command: ['sh', '-c']
        args:
        - |
          echo "等待 Redis 服务可用..."
          until nc -z redis-service 6379; do
            echo "Redis 服务不可用，等待..."
            sleep 2
          done
          echo "Redis 服务已可用"
      containers:
      - name: app
        image: docker.io/library/app:latest
        imagePullPolicy: Never  # 使用本地镜像
        ports:
        - containerPort: 8080
        envFrom:
        - configMapRef:
            name: app-config
        - secretRef:
            name: app-secret
        startupProbe:
          httpGet:
            path: /api/health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 30
        readinessProbe:
          httpGet:
            path: /api/health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        livenessProbe:
          httpGet:
            path: /api/health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "500m"