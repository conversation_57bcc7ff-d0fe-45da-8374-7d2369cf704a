#!/bin/bash

# 状态检查脚本
# 检查 k3s 中应用的运行状态

set -e

NAMESPACE="app"

echo "🔍 检查应用状态..."
echo ""

# 检查命名空间
if ! kubectl get namespace $NAMESPACE >/dev/null 2>&1; then
    echo "❌ 命名空间 $NAMESPACE 不存在"
    echo "请先运行 ./deploy.sh 部署应用"
    exit 1
fi

echo "📦 Pod 状态:"
kubectl get pods -n $NAMESPACE -o wide

echo ""
echo "🌐 服务状态:"
kubectl get services -n $NAMESPACE -o wide

echo ""
echo "🚀 部署状态:"
kubectl get deployments -n $NAMESPACE -o wide

echo ""
echo "📊 资源使用情况:"
kubectl top pods -n $NAMESPACE 2>/dev/null || echo "⚠️  Metrics server 未安装，无法显示资源使用情况"

echo ""
echo "🔗 访问信息:"
echo "  - 健康检查: kubectl port-forward service/app-service 8081:8080 -n app"
echo "  - 然后访问: http://localhost:8081/api/health"
echo "  - 前端界面: http://localhost:8081/"
echo ""
echo "💡 使用 ./port-forward.sh 启动端口转发"