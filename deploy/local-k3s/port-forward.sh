#!/bin/bash

# 端口转发脚本
# 用于在本地访问 k3s 中的应用

set -e

APP_PORT=${1:-8081}
NAMESPACE="app"
SERVICE="app-service"

echo "🚀 设置端口转发..."
echo "📍 本地端口: $APP_PORT"
echo "📍 服务: $SERVICE (命名空间: $NAMESPACE)"
echo "📍 访问地址: http://localhost:$APP_PORT"
echo ""

# 检查服务是否存在
if ! kubectl get service $SERVICE -n $NAMESPACE >/dev/null 2>&1; then
    echo "❌ 服务 $SERVICE 在命名空间 $NAMESPACE 中不存在"
    echo "请先运行 ./deploy.sh 部署应用"
    exit 1
fi

# 检查端口是否被占用
if lsof -Pi :$APP_PORT -sTCP:LISTEN -t >/dev/null 2>&1; then
    echo "⚠️  端口 $APP_PORT 已被占用，正在尝试清理..."
    pkill -f "kubectl port-forward.*$APP_PORT" || true
    sleep 2
fi

echo "🔗 启动端口转发..."
echo "💡 按 Ctrl+C 停止端口转发"
echo ""

# 启动端口转发
kubectl port-forward service/$SERVICE $APP_PORT:8080 -n $NAMESPACE