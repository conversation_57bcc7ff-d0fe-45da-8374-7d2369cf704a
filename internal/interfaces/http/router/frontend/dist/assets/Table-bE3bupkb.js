import{N as Et,r as a,O as nt,P as ln,Q as xe,d as G,U as B,_ as ve,V as j,c as me,g as Xt,m as sn,f as W,h as ft,X as cn,C as rt,Y as ro,Z as oo,$ as Mt,a0 as ao,W as lo,a1 as io,a2 as ya,k as so,q as Wt,a3 as st,a4 as ht,y as dn,l as ce,a5 as co,a6 as hr,a7 as Ca,a8 as xa,a9 as Sa,aa as wa,ab as Ea,ac as $a,ad as ka,ae as Na,af as Gt,ag as Ra,ah as Oa,ai as Ia,aj as Pa,ak as br,al as Ka,am as Ta,an as uo,ao as Da,ap as yr,B as Rn,aq as Ma,ar as Ba,as as fo,at as La,i as _a,au as Ha,I as ot,av as Kt,aw as za,ax as bt,ay as Vt,az as Cr,aA as vo,aB as jn,aC as Fn,aD as xr,aE as ja,aF as po,aG as Sr,aH as Fa,aI as wr,aJ as Aa,aK as An,aL as mo,t as Wa,aM as Va,aN as qa,aO as Xa,aP as Ga,aQ as fe,aR as gt,aS as Ua,aT as Ya,aU as Qa,aV as Za,e as Er,aW as Ja,aX as go,aY as el,aZ as tl,a_ as $r,a$ as nl,b0 as rl,b1 as ol,F as jt,n as al,p as ll}from"./index-P7H5iTop.js";import{L as ho,R as il,e as bo,E as kr,D as sl,P as cl}from"./Pagination-DPfaT0Az.js";import{b as dl}from"./proxy-OhLL-xTq.js";import{a as ul}from"./index-C73uzCkN.js";function On(e){return e!=null&&e===e.window}const fl=e=>{var t,r;if(typeof window>"u")return 0;let n=0;return On(e)?n=e.pageYOffset:e instanceof Document?n=e.documentElement.scrollTop:(e instanceof HTMLElement||e)&&(n=e.scrollTop),e&&!On(e)&&typeof n!="number"&&(n=(r=((t=e.ownerDocument)!==null&&t!==void 0?t:e).documentElement)===null||r===void 0?void 0:r.scrollTop),n};function vl(e,t,r,n){const o=r-t;return e/=n/2,e<1?o/2*e*e*e+t:o/2*((e-=2)*e*e+2)+t}function pl(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};const{getContainer:r=()=>window,callback:n,duration:o=450}=t,i=r(),d=fl(i),l=Date.now(),c=()=>{const v=Date.now()-l,u=vl(v>o?o:v,d,e,o);On(i)?i.scrollTo(window.pageXOffset,u):i instanceof Document||i.constructor.name==="HTMLDocument"?i.documentElement.scrollTop=u:i.scrollTop=u,v<o?Et(c):typeof n=="function"&&n()};Et(c)}const yo=a.createContext(null),ml=yo.Provider,Co=a.createContext(null),gl=Co.Provider;var hl=["prefixCls","className","style","checked","disabled","defaultChecked","type","title","onChange"],xo=a.forwardRef(function(e,t){var r=e.prefixCls,n=r===void 0?"rc-checkbox":r,o=e.className,i=e.style,d=e.checked,l=e.disabled,c=e.defaultChecked,s=c===void 0?!1:c,v=e.type,u=v===void 0?"checkbox":v,p=e.title,f=e.onChange,g=nt(e,hl),y=a.useRef(null),m=a.useRef(null),h=ln(s,{value:d}),x=xe(h,2),C=x[0],S=x[1];a.useImperativeHandle(t,function(){return{focus:function(R){var b;(b=y.current)===null||b===void 0||b.focus(R)},blur:function(){var R;(R=y.current)===null||R===void 0||R.blur()},input:y.current,nativeElement:m.current}});var E=G(n,o,B(B({},"".concat(n,"-checked"),C),"".concat(n,"-disabled"),l)),$=function(R){l||("checked"in e||S(R.target.checked),f==null||f({target:j(j({},e),{},{type:u,checked:R.target.checked}),stopPropagation:function(){R.stopPropagation()},preventDefault:function(){R.preventDefault()},nativeEvent:R.nativeEvent}))};return a.createElement("span",{className:E,title:p,style:i,ref:m},a.createElement("input",ve({},g,{className:"".concat(n,"-input"),ref:y,onChange:$,disabled:l,checked:!!C,type:u})),a.createElement("span",{className:"".concat(n,"-inner")}))});function So(e){const t=me.useRef(null),r=()=>{Et.cancel(t.current),t.current=null};return[()=>{r(),t.current=Et(()=>{t.current=null})},i=>{t.current&&(i.stopPropagation(),r()),e==null||e(i)}]}const bl=e=>{const{componentCls:t,antCls:r}=e,n=`${t}-group`;return{[n]:Object.assign(Object.assign({},ft(e)),{display:"inline-block",fontSize:0,[`&${n}-rtl`]:{direction:"rtl"},[`&${n}-block`]:{display:"flex"},[`${r}-badge ${r}-badge-count`]:{zIndex:1},[`> ${r}-badge:not(:first-child) > ${r}-button-wrapper`]:{borderInlineStart:"none"}})}},yl=e=>{const{componentCls:t,wrapperMarginInlineEnd:r,colorPrimary:n,radioSize:o,motionDurationSlow:i,motionDurationMid:d,motionEaseInOutCirc:l,colorBgContainer:c,colorBorder:s,lineWidth:v,colorBgContainerDisabled:u,colorTextDisabled:p,paddingXS:f,dotColorDisabled:g,lineType:y,radioColor:m,radioBgColor:h,calc:x}=e,C=`${t}-inner`,E=x(o).sub(x(4).mul(2)),$=x(1).mul(o).equal({unit:!0});return{[`${t}-wrapper`]:Object.assign(Object.assign({},ft(e)),{display:"inline-flex",alignItems:"baseline",marginInlineStart:0,marginInlineEnd:r,cursor:"pointer","&:last-child":{marginInlineEnd:0},[`&${t}-wrapper-rtl`]:{direction:"rtl"},"&-disabled":{cursor:"not-allowed",color:e.colorTextDisabled},"&::after":{display:"inline-block",width:0,overflow:"hidden",content:'"\\a0"'},"&-block":{flex:1,justifyContent:"center"},[`${t}-checked::after`]:{position:"absolute",insetBlockStart:0,insetInlineStart:0,width:"100%",height:"100%",border:`${W(v)} ${y} ${n}`,borderRadius:"50%",visibility:"hidden",opacity:0,content:'""'},[t]:Object.assign(Object.assign({},ft(e)),{position:"relative",display:"inline-block",outline:"none",cursor:"pointer",alignSelf:"center",borderRadius:"50%"}),[`${t}-wrapper:hover &,
        &:hover ${C}`]:{borderColor:n},[`${t}-input:focus-visible + ${C}`]:Object.assign({},cn(e)),[`${t}:hover::after, ${t}-wrapper:hover &::after`]:{visibility:"visible"},[`${t}-inner`]:{"&::after":{boxSizing:"border-box",position:"absolute",insetBlockStart:"50%",insetInlineStart:"50%",display:"block",width:$,height:$,marginBlockStart:x(1).mul(o).div(-2).equal({unit:!0}),marginInlineStart:x(1).mul(o).div(-2).equal({unit:!0}),backgroundColor:m,borderBlockStart:0,borderInlineStart:0,borderRadius:$,transform:"scale(0)",opacity:0,transition:`all ${i} ${l}`,content:'""'},boxSizing:"border-box",position:"relative",insetBlockStart:0,insetInlineStart:0,display:"block",width:$,height:$,backgroundColor:c,borderColor:s,borderStyle:"solid",borderWidth:v,borderRadius:"50%",transition:`all ${d}`},[`${t}-input`]:{position:"absolute",inset:0,zIndex:1,cursor:"pointer",opacity:0},[`${t}-checked`]:{[C]:{borderColor:n,backgroundColor:h,"&::after":{transform:`scale(${e.calc(e.dotSize).div(o).equal()})`,opacity:1,transition:`all ${i} ${l}`}}},[`${t}-disabled`]:{cursor:"not-allowed",[C]:{backgroundColor:u,borderColor:s,cursor:"not-allowed","&::after":{backgroundColor:g}},[`${t}-input`]:{cursor:"not-allowed"},[`${t}-disabled + span`]:{color:p,cursor:"not-allowed"},[`&${t}-checked`]:{[C]:{"&::after":{transform:`scale(${x(E).div(o).equal()})`}}}},[`span${t} + *`]:{paddingInlineStart:f,paddingInlineEnd:f}})}},Cl=e=>{const{buttonColor:t,controlHeight:r,componentCls:n,lineWidth:o,lineType:i,colorBorder:d,motionDurationSlow:l,motionDurationMid:c,buttonPaddingInline:s,fontSize:v,buttonBg:u,fontSizeLG:p,controlHeightLG:f,controlHeightSM:g,paddingXS:y,borderRadius:m,borderRadiusSM:h,borderRadiusLG:x,buttonCheckedBg:C,buttonSolidCheckedColor:S,colorTextDisabled:E,colorBgContainerDisabled:$,buttonCheckedBgDisabled:k,buttonCheckedColorDisabled:R,colorPrimary:b,colorPrimaryHover:T,colorPrimaryActive:D,buttonSolidCheckedBg:P,buttonSolidCheckedHoverBg:O,buttonSolidCheckedActiveBg:N,calc:w}=e;return{[`${n}-button-wrapper`]:{position:"relative",display:"inline-block",height:r,margin:0,paddingInline:s,paddingBlock:0,color:t,fontSize:v,lineHeight:W(w(r).sub(w(o).mul(2)).equal()),background:u,border:`${W(o)} ${i} ${d}`,borderBlockStartWidth:w(o).add(.02).equal(),borderInlineStartWidth:0,borderInlineEndWidth:o,cursor:"pointer",transition:[`color ${c}`,`background ${c}`,`box-shadow ${c}`].join(","),a:{color:t},[`> ${n}-button`]:{position:"absolute",insetBlockStart:0,insetInlineStart:0,zIndex:-1,width:"100%",height:"100%"},"&:not(:first-child)":{"&::before":{position:"absolute",insetBlockStart:w(o).mul(-1).equal(),insetInlineStart:w(o).mul(-1).equal(),display:"block",boxSizing:"content-box",width:1,height:"100%",paddingBlock:o,paddingInline:0,backgroundColor:d,transition:`background-color ${l}`,content:'""'}},"&:first-child":{borderInlineStart:`${W(o)} ${i} ${d}`,borderStartStartRadius:m,borderEndStartRadius:m},"&:last-child":{borderStartEndRadius:m,borderEndEndRadius:m},"&:first-child:last-child":{borderRadius:m},[`${n}-group-large &`]:{height:f,fontSize:p,lineHeight:W(w(f).sub(w(o).mul(2)).equal()),"&:first-child":{borderStartStartRadius:x,borderEndStartRadius:x},"&:last-child":{borderStartEndRadius:x,borderEndEndRadius:x}},[`${n}-group-small &`]:{height:g,paddingInline:w(y).sub(o).equal(),paddingBlock:0,lineHeight:W(w(g).sub(w(o).mul(2)).equal()),"&:first-child":{borderStartStartRadius:h,borderEndStartRadius:h},"&:last-child":{borderStartEndRadius:h,borderEndEndRadius:h}},"&:hover":{position:"relative",color:b},"&:has(:focus-visible)":Object.assign({},cn(e)),[`${n}-inner, input[type='checkbox'], input[type='radio']`]:{width:0,height:0,opacity:0,pointerEvents:"none"},[`&-checked:not(${n}-button-wrapper-disabled)`]:{zIndex:1,color:b,background:C,borderColor:b,"&::before":{backgroundColor:b},"&:first-child":{borderColor:b},"&:hover":{color:T,borderColor:T,"&::before":{backgroundColor:T}},"&:active":{color:D,borderColor:D,"&::before":{backgroundColor:D}}},[`${n}-group-solid &-checked:not(${n}-button-wrapper-disabled)`]:{color:S,background:P,borderColor:P,"&:hover":{color:S,background:O,borderColor:O},"&:active":{color:S,background:N,borderColor:N}},"&-disabled":{color:E,backgroundColor:$,borderColor:d,cursor:"not-allowed","&:first-child, &:hover":{color:E,backgroundColor:$,borderColor:d}},[`&-disabled${n}-button-wrapper-checked`]:{color:R,backgroundColor:k,borderColor:d,boxShadow:"none"},"&-block":{flex:1,textAlign:"center"}}}},xl=e=>{const{wireframe:t,padding:r,marginXS:n,lineWidth:o,fontSizeLG:i,colorText:d,colorBgContainer:l,colorTextDisabled:c,controlItemBgActiveDisabled:s,colorTextLightSolid:v,colorPrimary:u,colorPrimaryHover:p,colorPrimaryActive:f,colorWhite:g}=e,y=4,m=i,h=t?m-y*2:m-(y+o)*2;return{radioSize:m,dotSize:h,dotColorDisabled:c,buttonSolidCheckedColor:v,buttonSolidCheckedBg:u,buttonSolidCheckedHoverBg:p,buttonSolidCheckedActiveBg:f,buttonBg:l,buttonCheckedBg:l,buttonColor:d,buttonCheckedBgDisabled:s,buttonCheckedColorDisabled:c,buttonPaddingInline:r-o,wrapperMarginInlineEnd:n,radioColor:t?u:g,radioBgColor:t?l:u}},wo=Xt("Radio",e=>{const{controlOutline:t,controlOutlineWidth:r}=e,n=`0 0 0 ${W(r)} ${t}`,i=sn(e,{radioFocusShadow:n,radioButtonFocusShadow:n});return[bl(i),yl(i),Cl(i)]},xl,{unitless:{radioSize:!0,dotSize:!0}});var Sl=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};const wl=(e,t)=>{var r,n;const o=a.useContext(yo),i=a.useContext(Co),{getPrefixCls:d,direction:l,radio:c}=a.useContext(rt),s=a.useRef(null),v=ro(t,s),{isFormItemInput:u}=a.useContext(oo),p=I=>{var K,L;(K=e.onChange)===null||K===void 0||K.call(e,I),(L=o==null?void 0:o.onChange)===null||L===void 0||L.call(o,I)},{prefixCls:f,className:g,rootClassName:y,children:m,style:h,title:x}=e,C=Sl(e,["prefixCls","className","rootClassName","children","style","title"]),S=d("radio",f),E=((o==null?void 0:o.optionType)||i)==="button",$=E?`${S}-button`:S,k=Mt(S),[R,b,T]=wo(S,k),D=Object.assign({},C),P=a.useContext(ao);o&&(D.name=o.name,D.onChange=p,D.checked=e.value===o.value,D.disabled=(r=D.disabled)!==null&&r!==void 0?r:o.disabled),D.disabled=(n=D.disabled)!==null&&n!==void 0?n:P;const O=G(`${$}-wrapper`,{[`${$}-wrapper-checked`]:D.checked,[`${$}-wrapper-disabled`]:D.disabled,[`${$}-wrapper-rtl`]:l==="rtl",[`${$}-wrapper-in-form-item`]:u,[`${$}-wrapper-block`]:!!(o!=null&&o.block)},c==null?void 0:c.className,g,y,b,T,k),[N,w]=So(D.onClick);return R(a.createElement(lo,{component:"Radio",disabled:D.disabled},a.createElement("label",{className:O,style:Object.assign(Object.assign({},c==null?void 0:c.style),h),onMouseEnter:e.onMouseEnter,onMouseLeave:e.onMouseLeave,title:x,onClick:N},a.createElement(xo,Object.assign({},D,{className:G(D.className,{[io]:!E}),type:"radio",prefixCls:$,ref:v,onClick:w})),m!==void 0?a.createElement("span",{className:`${$}-label`},m):null)))},on=a.forwardRef(wl),El=a.forwardRef((e,t)=>{const{getPrefixCls:r,direction:n}=a.useContext(rt),o=ya(),{prefixCls:i,className:d,rootClassName:l,options:c,buttonStyle:s="outline",disabled:v,children:u,size:p,style:f,id:g,optionType:y,name:m=o,defaultValue:h,value:x,block:C=!1,onChange:S,onMouseEnter:E,onMouseLeave:$,onFocus:k,onBlur:R}=e,[b,T]=ln(h,{value:x}),D=a.useCallback(U=>{const te=b,ye=U.target.value;"value"in e||T(ye),ye!==te&&(S==null||S(U))},[b,T,S]),P=r("radio",i),O=`${P}-group`,N=Mt(P),[w,I,K]=wo(P,N);let L=u;c&&c.length>0&&(L=c.map(U=>typeof U=="string"||typeof U=="number"?a.createElement(on,{key:U.toString(),prefixCls:P,disabled:v,value:U,checked:b===U},U):a.createElement(on,{key:`radio-group-value-options-${U.value}`,prefixCls:P,disabled:U.disabled||v,value:U.value,checked:b===U.value,title:U.title,style:U.style,id:U.id,required:U.required},U.label)));const M=so(p),V=G(O,`${O}-${s}`,{[`${O}-${M}`]:M,[`${O}-rtl`]:n==="rtl",[`${O}-block`]:C},d,l,I,K,N),Q=a.useMemo(()=>({onChange:D,value:b,disabled:v,name:m,optionType:y,block:C}),[D,b,v,m,y,C]);return w(a.createElement("div",Object.assign({},Wt(e,{aria:!0,data:!0}),{className:V,style:f,onMouseEnter:E,onMouseLeave:$,onFocus:k,onBlur:R,id:g,ref:t}),a.createElement(ml,{value:Q},L)))}),$l=a.memo(El);var kl=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};const Nl=(e,t)=>{const{getPrefixCls:r}=a.useContext(rt),{prefixCls:n}=e,o=kl(e,["prefixCls"]),i=r("radio",n);return a.createElement(gl,{value:"button"},a.createElement(on,Object.assign({prefixCls:i},o,{type:"radio",ref:t})))},Rl=a.forwardRef(Nl),Ut=on;Ut.Button=Rl;Ut.Group=$l;Ut.__ANT_RADIO=!0;function Qe(e,t){return e[t]}var Ol=["children"];function Eo(e,t){return"".concat(e,"-").concat(t)}function Il(e){return e&&e.type&&e.type.isTreeNode}function Yt(e,t){return e??t}function Tt(e){var t=e||{},r=t.title,n=t._title,o=t.key,i=t.children,d=r||"title";return{title:d,_title:n||[d],key:o||"key",children:i||"children"}}function $o(e){function t(r){var n=co(r);return n.map(function(o){if(!Il(o))return ht(!o,"Tree/TreeNode can only accept TreeNode as children."),null;var i=o.key,d=o.props,l=d.children,c=nt(d,Ol),s=j({key:i},c),v=t(l);return v.length&&(s.children=v),s}).filter(function(o){return o})}return t(e)}function Sn(e,t,r){var n=Tt(r),o=n._title,i=n.key,d=n.children,l=new Set(t===!0?[]:t),c=[];function s(v){var u=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;return v.map(function(p,f){for(var g=Eo(u?u.pos:"0",f),y=Yt(p[i],g),m,h=0;h<o.length;h+=1){var x=o[h];if(p[x]!==void 0){m=p[x];break}}var C=Object.assign(dn(p,[].concat(ce(o),[i,d])),{title:m,key:y,parent:u,pos:g,children:null,data:p,isStart:[].concat(ce(u?u.isStart:[]),[f===0]),isEnd:[].concat(ce(u?u.isEnd:[]),[f===v.length-1])});return c.push(C),t===!0||l.has(y)?C.children=s(p[d]||[],C):C.children=[],C})}return s(e),c}function Pl(e,t,r){var n={};st(r)==="object"?n=r:n={externalGetKey:r},n=n||{};var o=n,i=o.childrenPropName,d=o.externalGetKey,l=o.fieldNames,c=Tt(l),s=c.key,v=c.children,u=i||v,p;d?typeof d=="string"?p=function(y){return y[d]}:typeof d=="function"&&(p=function(y){return d(y)}):p=function(y,m){return Yt(y[s],m)};function f(g,y,m,h){var x=g?g[u]:e,C=g?Eo(m.pos,y):"0",S=g?[].concat(ce(h),[g]):[];if(g){var E=p(g,C),$={node:g,index:y,pos:C,key:E,parentPos:m.node?m.pos:null,level:m.level+1,nodes:S};t($)}x&&x.forEach(function(k,R){f(k,R,{node:g,pos:C,level:m?m.level+1:-1},S)})}f(null)}function Wn(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r=t.initWrapper,n=t.processEntity,o=t.onProcessFinished,i=t.externalGetKey,d=t.childrenPropName,l=t.fieldNames,c=arguments.length>2?arguments[2]:void 0,s=i||c,v={},u={},p={posEntities:v,keyEntities:u};return r&&(p=r(p)||p),Pl(e,function(f){var g=f.node,y=f.index,m=f.pos,h=f.key,x=f.parentPos,C=f.level,S=f.nodes,E={node:g,nodes:S,index:y,key:h,pos:m,level:C},$=Yt(h,m);v[m]=E,u[$]=E,E.parent=v[x],E.parent&&(E.parent.children=E.parent.children||[],E.parent.children.push(E)),n&&n(E,p)},{externalGetKey:s,childrenPropName:d,fieldNames:l}),o&&o(p),p}function Ft(e,t){var r=t.expandedKeys,n=t.selectedKeys,o=t.loadedKeys,i=t.loadingKeys,d=t.checkedKeys,l=t.halfCheckedKeys,c=t.dragOverNodeKey,s=t.dropPosition,v=t.keyEntities,u=Qe(v,e),p={eventKey:e,expanded:r.indexOf(e)!==-1,selected:n.indexOf(e)!==-1,loaded:o.indexOf(e)!==-1,loading:i.indexOf(e)!==-1,checked:d.indexOf(e)!==-1,halfChecked:l.indexOf(e)!==-1,pos:String(u?u.pos:""),dragOver:c===e&&s===0,dragOverGapTop:c===e&&s===-1,dragOverGapBottom:c===e&&s===1};return p}function Me(e){var t=e.data,r=e.expanded,n=e.selected,o=e.checked,i=e.loaded,d=e.loading,l=e.halfChecked,c=e.dragOver,s=e.dragOverGapTop,v=e.dragOverGapBottom,u=e.pos,p=e.active,f=e.eventKey,g=j(j({},t),{},{expanded:r,selected:n,checked:o,loaded:i,loading:d,halfChecked:l,dragOver:c,dragOverGapTop:s,dragOverGapBottom:v,pos:u,active:p,key:f});return"props"in g||Object.defineProperty(g,"props",{get:function(){return ht(!1,"Second param return from event is node data instead of TreeNode instance. Please read value directly instead of reading from `props`."),e}}),g}function ko(e,t){var r=new Set;return e.forEach(function(n){t.has(n)||r.add(n)}),r}function Kl(e){var t=e||{},r=t.disabled,n=t.disableCheckbox,o=t.checkable;return!!(r||n)||o===!1}function Tl(e,t,r,n){for(var o=new Set(e),i=new Set,d=0;d<=r;d+=1){var l=t.get(d)||new Set;l.forEach(function(u){var p=u.key,f=u.node,g=u.children,y=g===void 0?[]:g;o.has(p)&&!n(f)&&y.filter(function(m){return!n(m.node)}).forEach(function(m){o.add(m.key)})})}for(var c=new Set,s=r;s>=0;s-=1){var v=t.get(s)||new Set;v.forEach(function(u){var p=u.parent,f=u.node;if(!(n(f)||!u.parent||c.has(u.parent.key))){if(n(u.parent.node)){c.add(p.key);return}var g=!0,y=!1;(p.children||[]).filter(function(m){return!n(m.node)}).forEach(function(m){var h=m.key,x=o.has(h);g&&!x&&(g=!1),!y&&(x||i.has(h))&&(y=!0)}),g&&o.add(p.key),y&&i.add(p.key),c.add(p.key)}})}return{checkedKeys:Array.from(o),halfCheckedKeys:Array.from(ko(i,o))}}function Dl(e,t,r,n,o){for(var i=new Set(e),d=new Set(t),l=0;l<=n;l+=1){var c=r.get(l)||new Set;c.forEach(function(p){var f=p.key,g=p.node,y=p.children,m=y===void 0?[]:y;!i.has(f)&&!d.has(f)&&!o(g)&&m.filter(function(h){return!o(h.node)}).forEach(function(h){i.delete(h.key)})})}d=new Set;for(var s=new Set,v=n;v>=0;v-=1){var u=r.get(v)||new Set;u.forEach(function(p){var f=p.parent,g=p.node;if(!(o(g)||!p.parent||s.has(p.parent.key))){if(o(p.parent.node)){s.add(f.key);return}var y=!0,m=!1;(f.children||[]).filter(function(h){return!o(h.node)}).forEach(function(h){var x=h.key,C=i.has(x);y&&!C&&(y=!1),!m&&(C||d.has(x))&&(m=!0)}),y||i.delete(f.key),m&&d.add(f.key),s.add(f.key)}})}return{checkedKeys:Array.from(i),halfCheckedKeys:Array.from(ko(d,i))}}function It(e,t,r,n){var o=[],i;n?i=n:i=Kl;var d=new Set(e.filter(function(v){var u=!!Qe(r,v);return u||o.push(v),u})),l=new Map,c=0;Object.keys(r).forEach(function(v){var u=r[v],p=u.level,f=l.get(p);f||(f=new Set,l.set(p,f)),f.add(u),c=Math.max(c,p)}),ht(!o.length,"Tree missing follow keys: ".concat(o.slice(0,100).map(function(v){return"'".concat(v,"'")}).join(", ")));var s;return t===!0?s=Tl(d,l,c,i):s=Dl(d,t.halfCheckedKeys,l,c,i),s}const Ml=e=>{const{checkboxCls:t}=e,r=`${t}-wrapper`;return[{[`${t}-group`]:Object.assign(Object.assign({},ft(e)),{display:"inline-flex",flexWrap:"wrap",columnGap:e.marginXS,[`> ${e.antCls}-row`]:{flex:1}}),[r]:Object.assign(Object.assign({},ft(e)),{display:"inline-flex",alignItems:"baseline",cursor:"pointer","&:after":{display:"inline-block",width:0,overflow:"hidden",content:"'\\a0'"},[`& + ${r}`]:{marginInlineStart:0},[`&${r}-in-form-item`]:{'input[type="checkbox"]':{width:14,height:14}}}),[t]:Object.assign(Object.assign({},ft(e)),{position:"relative",whiteSpace:"nowrap",lineHeight:1,cursor:"pointer",borderRadius:e.borderRadiusSM,alignSelf:"center",[`${t}-input`]:{position:"absolute",inset:0,zIndex:1,cursor:"pointer",opacity:0,margin:0,[`&:focus-visible + ${t}-inner`]:Object.assign({},cn(e))},[`${t}-inner`]:{boxSizing:"border-box",display:"block",width:e.checkboxSize,height:e.checkboxSize,direction:"ltr",backgroundColor:e.colorBgContainer,border:`${W(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusSM,borderCollapse:"separate",transition:`all ${e.motionDurationSlow}`,"&:after":{boxSizing:"border-box",position:"absolute",top:"50%",insetInlineStart:"25%",display:"table",width:e.calc(e.checkboxSize).div(14).mul(5).equal(),height:e.calc(e.checkboxSize).div(14).mul(8).equal(),border:`${W(e.lineWidthBold)} solid ${e.colorWhite}`,borderTop:0,borderInlineStart:0,transform:"rotate(45deg) scale(0) translate(-50%,-50%)",opacity:0,content:'""',transition:`all ${e.motionDurationFast} ${e.motionEaseInBack}, opacity ${e.motionDurationFast}`}},"& + span":{paddingInlineStart:e.paddingXS,paddingInlineEnd:e.paddingXS}})},{[`
        ${r}:not(${r}-disabled),
        ${t}:not(${t}-disabled)
      `]:{[`&:hover ${t}-inner`]:{borderColor:e.colorPrimary}},[`${r}:not(${r}-disabled)`]:{[`&:hover ${t}-checked:not(${t}-disabled) ${t}-inner`]:{backgroundColor:e.colorPrimaryHover,borderColor:"transparent"},[`&:hover ${t}-checked:not(${t}-disabled):after`]:{borderColor:e.colorPrimaryHover}}},{[`${t}-checked`]:{[`${t}-inner`]:{backgroundColor:e.colorPrimary,borderColor:e.colorPrimary,"&:after":{opacity:1,transform:"rotate(45deg) scale(1) translate(-50%,-50%)",transition:`all ${e.motionDurationMid} ${e.motionEaseOutBack} ${e.motionDurationFast}`}}},[`
        ${r}-checked:not(${r}-disabled),
        ${t}-checked:not(${t}-disabled)
      `]:{[`&:hover ${t}-inner`]:{backgroundColor:e.colorPrimaryHover,borderColor:"transparent"}}},{[t]:{"&-indeterminate":{[`${t}-inner`]:{backgroundColor:`${e.colorBgContainer} !important`,borderColor:`${e.colorBorder} !important`,"&:after":{top:"50%",insetInlineStart:"50%",width:e.calc(e.fontSizeLG).div(2).equal(),height:e.calc(e.fontSizeLG).div(2).equal(),backgroundColor:e.colorPrimary,border:0,transform:"translate(-50%, -50%) scale(1)",opacity:1,content:'""'}},[`&:hover ${t}-inner`]:{backgroundColor:`${e.colorBgContainer} !important`,borderColor:`${e.colorPrimary} !important`}}}},{[`${r}-disabled`]:{cursor:"not-allowed"},[`${t}-disabled`]:{[`&, ${t}-input`]:{cursor:"not-allowed",pointerEvents:"none"},[`${t}-inner`]:{background:e.colorBgContainerDisabled,borderColor:e.colorBorder,"&:after":{borderColor:e.colorTextDisabled}},"&:after":{display:"none"},"& + span":{color:e.colorTextDisabled},[`&${t}-indeterminate ${t}-inner::after`]:{background:e.colorTextDisabled}}}]};function No(e,t){const r=sn(t,{checkboxCls:`.${e}`,checkboxSize:t.controlInteractiveSize});return[Ml(r)]}const Ro=Xt("Checkbox",(e,t)=>{let{prefixCls:r}=t;return[No(r,e)]}),Oo=me.createContext(null);var Bl=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};const Ll=(e,t)=>{var r;const{prefixCls:n,className:o,rootClassName:i,children:d,indeterminate:l=!1,style:c,onMouseEnter:s,onMouseLeave:v,skipGroup:u=!1,disabled:p}=e,f=Bl(e,["prefixCls","className","rootClassName","children","indeterminate","style","onMouseEnter","onMouseLeave","skipGroup","disabled"]),{getPrefixCls:g,direction:y,checkbox:m}=a.useContext(rt),h=a.useContext(Oo),{isFormItemInput:x}=a.useContext(oo),C=a.useContext(ao),S=(r=(h==null?void 0:h.disabled)||p)!==null&&r!==void 0?r:C,E=a.useRef(f.value),$=a.useRef(null),k=ro(t,$);a.useEffect(()=>{h==null||h.registerValue(f.value)},[]),a.useEffect(()=>{if(!u)return f.value!==E.current&&(h==null||h.cancelValue(E.current),h==null||h.registerValue(f.value),E.current=f.value),()=>h==null?void 0:h.cancelValue(f.value)},[f.value]),a.useEffect(()=>{var L;!((L=$.current)===null||L===void 0)&&L.input&&($.current.input.indeterminate=l)},[l]);const R=g("checkbox",n),b=Mt(R),[T,D,P]=Ro(R,b),O=Object.assign({},f);h&&!u&&(O.onChange=function(){f.onChange&&f.onChange.apply(f,arguments),h.toggleOption&&h.toggleOption({label:d,value:f.value})},O.name=h.name,O.checked=h.value.includes(f.value));const N=G(`${R}-wrapper`,{[`${R}-rtl`]:y==="rtl",[`${R}-wrapper-checked`]:O.checked,[`${R}-wrapper-disabled`]:S,[`${R}-wrapper-in-form-item`]:x},m==null?void 0:m.className,o,i,P,b,D),w=G({[`${R}-indeterminate`]:l},io,D),[I,K]=So(O.onClick);return T(a.createElement(lo,{component:"Checkbox",disabled:S},a.createElement("label",{className:N,style:Object.assign(Object.assign({},m==null?void 0:m.style),c),onMouseEnter:s,onMouseLeave:v,onClick:I},a.createElement(xo,Object.assign({},O,{onClick:K,prefixCls:R,className:w,disabled:S,ref:k})),d!==void 0&&a.createElement("span",{className:`${R}-label`},d))))},Io=a.forwardRef(Ll);var _l=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};const Hl=a.forwardRef((e,t)=>{const{defaultValue:r,children:n,options:o=[],prefixCls:i,className:d,rootClassName:l,style:c,onChange:s}=e,v=_l(e,["defaultValue","children","options","prefixCls","className","rootClassName","style","onChange"]),{getPrefixCls:u,direction:p}=a.useContext(rt),[f,g]=a.useState(v.value||r||[]),[y,m]=a.useState([]);a.useEffect(()=>{"value"in v&&g(v.value||[])},[v.value]);const h=a.useMemo(()=>o.map(w=>typeof w=="string"||typeof w=="number"?{label:w,value:w}:w),[o]),x=w=>{m(I=>I.filter(K=>K!==w))},C=w=>{m(I=>[].concat(ce(I),[w]))},S=w=>{const I=f.indexOf(w.value),K=ce(f);I===-1?K.push(w.value):K.splice(I,1),"value"in v||g(K),s==null||s(K.filter(L=>y.includes(L)).sort((L,M)=>{const V=h.findIndex(U=>U.value===L),Q=h.findIndex(U=>U.value===M);return V-Q}))},E=u("checkbox",i),$=`${E}-group`,k=Mt(E),[R,b,T]=Ro(E,k),D=dn(v,["value","disabled"]),P=o.length?h.map(w=>a.createElement(Io,{prefixCls:E,key:w.value.toString(),disabled:"disabled"in w?w.disabled:v.disabled,value:w.value,checked:f.includes(w.value),onChange:w.onChange,className:`${$}-item`,style:w.style,title:w.title,id:w.id,required:w.required},w.label)):n,O={toggleOption:S,value:f,disabled:v.disabled,name:v.name,registerValue:C,cancelValue:x},N=G($,{[`${$}-rtl`]:p==="rtl"},d,l,T,k,b);return R(a.createElement("div",Object.assign({className:N,style:c},D,{ref:t}),a.createElement(Oo.Provider,{value:O},P)))}),Dt=Io;Dt.Group=Hl;Dt.__ANT_CHECKBOX=!0;function Nr(e,t,r,n){var o=hr.unstable_batchedUpdates?function(d){hr.unstable_batchedUpdates(r,d)}:r;return e!=null&&e.addEventListener&&e.addEventListener(t,o,n),{remove:function(){e!=null&&e.removeEventListener&&e.removeEventListener(t,o,n)}}}var zl=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};const jl=e=>{const{prefixCls:t,className:r,closeIcon:n,closable:o,type:i,title:d,children:l,footer:c}=e,s=zl(e,["prefixCls","className","closeIcon","closable","type","title","children","footer"]),{getPrefixCls:v}=a.useContext(rt),u=v(),p=t||v("modal"),f=Mt(u),[g,y,m]=xa(p,f),h=`${p}-confirm`;let x={};return i?x={closable:o??!1,title:"",footer:"",children:a.createElement(Sa,Object.assign({},e,{prefixCls:p,confirmPrefixCls:h,rootPrefixCls:u,content:l}))}:x={closable:o??!0,title:d,footer:c!==null&&a.createElement(wa,Object.assign({},e)),children:l},g(a.createElement(Ea,Object.assign({prefixCls:p,className:G(y,`${p}-pure-panel`,i&&h,i&&`${h}-${i}`,r,m,f)},s,{closeIcon:$a(p,n),closable:o},x)))},Fl=Ca(jl);function Po(e){return Gt(Ta(e))}const ct=ka;ct.useModal=Na;ct.info=function(t){return Gt(Ra(t))};ct.success=function(t){return Gt(Oa(t))};ct.error=function(t){return Gt(Ia(t))};ct.warning=Po;ct.warn=Po;ct.confirm=function(t){return Gt(Pa(t))};ct.destroyAll=function(){for(;br.length;){const t=br.pop();t&&t()}};ct.config=Ka;ct._InternalPanelDoNotUseOrYouWillBeFired=Fl;const Al=e=>{const{componentCls:t,iconCls:r,antCls:n,zIndexPopup:o,colorText:i,colorWarning:d,marginXXS:l,marginXS:c,fontSize:s,fontWeightStrong:v,colorTextHeading:u}=e;return{[t]:{zIndex:o,[`&${n}-popover`]:{fontSize:s},[`${t}-message`]:{marginBottom:c,display:"flex",flexWrap:"nowrap",alignItems:"start",[`> ${t}-message-icon ${r}`]:{color:d,fontSize:s,lineHeight:1,marginInlineEnd:c},[`${t}-title`]:{fontWeight:v,color:u,"&:only-child":{fontWeight:"normal"}},[`${t}-description`]:{marginTop:l,color:i}},[`${t}-buttons`]:{textAlign:"end",whiteSpace:"nowrap",button:{marginInlineStart:c}}}}},Wl=e=>{const{zIndexPopupBase:t}=e;return{zIndexPopup:t+60}},Ko=Xt("Popconfirm",e=>Al(e),Wl,{resetStyle:!1});var Vl=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};const To=e=>{const{prefixCls:t,okButtonProps:r,cancelButtonProps:n,title:o,description:i,cancelText:d,okText:l,okType:c="primary",icon:s=a.createElement(uo,null),showCancel:v=!0,close:u,onConfirm:p,onCancel:f,onPopupClick:g}=e,{getPrefixCls:y}=a.useContext(rt),[m]=Da("Popconfirm",fo.Popconfirm),h=yr(o),x=yr(i);return a.createElement("div",{className:`${t}-inner-content`,onClick:g},a.createElement("div",{className:`${t}-message`},s&&a.createElement("span",{className:`${t}-message-icon`},s),a.createElement("div",{className:`${t}-message-text`},h&&a.createElement("div",{className:`${t}-title`},h),x&&a.createElement("div",{className:`${t}-description`},x))),a.createElement("div",{className:`${t}-buttons`},v&&a.createElement(Rn,Object.assign({onClick:f,size:"small"},n),d||(m==null?void 0:m.cancelText)),a.createElement(Ma,{buttonProps:Object.assign(Object.assign({size:"small"},Ba(c)),r),actionFn:p,close:u,prefixCls:y("btn"),quitOnNullishReturnValue:!0,emitEvent:!0},l||(m==null?void 0:m.okText))))},ql=e=>{const{prefixCls:t,placement:r,className:n,style:o}=e,i=Vl(e,["prefixCls","placement","className","style"]),{getPrefixCls:d}=a.useContext(rt),l=d("popconfirm",t),[c]=Ko(l);return c(a.createElement(La,{placement:r,className:G(l,n),style:o,content:a.createElement(To,Object.assign({prefixCls:l},i))}))};var Xl=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};const Gl=a.forwardRef((e,t)=>{var r,n;const{prefixCls:o,placement:i="top",trigger:d="click",okType:l="primary",icon:c=a.createElement(uo,null),children:s,overlayClassName:v,onOpenChange:u,onVisibleChange:p,overlayStyle:f,styles:g,classNames:y}=e,m=Xl(e,["prefixCls","placement","trigger","okType","icon","children","overlayClassName","onOpenChange","onVisibleChange","overlayStyle","styles","classNames"]),{getPrefixCls:h,className:x,style:C,classNames:S,styles:E}=_a("popconfirm"),[$,k]=ln(!1,{value:(r=e.open)!==null&&r!==void 0?r:e.visible,defaultValue:(n=e.defaultOpen)!==null&&n!==void 0?n:e.defaultVisible}),R=(K,L)=>{k(K,!0),p==null||p(K),u==null||u(K,L)},b=K=>{R(!1,K)},T=K=>{var L;return(L=e.onConfirm)===null||L===void 0?void 0:L.call(void 0,K)},D=K=>{var L;R(!1,K),(L=e.onCancel)===null||L===void 0||L.call(void 0,K)},P=(K,L)=>{const{disabled:M=!1}=e;M||R(K,L)},O=h("popconfirm",o),N=G(O,x,v,S.root,y==null?void 0:y.root),w=G(S.body,y==null?void 0:y.body),[I]=Ko(O);return I(a.createElement(Ha,Object.assign({},dn(m,["title"]),{trigger:d,placement:i,onOpenChange:P,open:$,ref:t,classNames:{root:N,body:w},styles:{root:Object.assign(Object.assign(Object.assign(Object.assign({},E.root),C),f),g==null?void 0:g.root),body:Object.assign(Object.assign({},E.body),g==null?void 0:g.body)},content:a.createElement(To,Object.assign({okType:l,icon:c},e,{prefixCls:O,close:b,onConfirm:T,onCancel:D})),"data-popover-inject":!0}),s))}),Ul=Gl;Ul._InternalPanelDoNotUseOrYouWillBeFired=ql;var Yl={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"}}]},name:"caret-down",theme:"filled"},Ql=function(t,r){return a.createElement(ot,ve({},t,{ref:r,icon:Yl}))},Zl=a.forwardRef(Ql),Jl={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"}}]},name:"caret-down",theme:"outlined"},ei=function(t,r){return a.createElement(ot,ve({},t,{ref:r,icon:Jl}))},ti=a.forwardRef(ei),ni={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M858.9 689L530.5 308.2c-9.4-10.9-27.5-10.9-37 0L165.1 689c-12.2 14.2-1.2 35 18.5 35h656.8c19.7 0 30.7-20.8 18.5-35z"}}]},name:"caret-up",theme:"outlined"},ri=function(t,r){return a.createElement(ot,ve({},t,{ref:r,icon:ni}))},oi=a.forwardRef(ri),ai={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M360 184h-8c4.4 0 8-3.6 8-8v8h304v-8c0 4.4 3.6 8 8 8h-8v72h72v-80c0-35.3-28.7-64-64-64H352c-35.3 0-64 28.7-64 64v80h72v-72zm504 72H160c-17.7 0-32 14.3-32 32v32c0 4.4 3.6 8 8 8h60.4l24.7 523c1.6 34.1 29.8 61 63.9 61h454c34.2 0 62.3-26.8 63.9-61l24.7-523H888c4.4 0 8-3.6 8-8v-32c0-17.7-14.3-32-32-32zM731.3 840H292.7l-24.2-512h487l-24.2 512z"}}]},name:"delete",theme:"outlined"},li=function(t,r){return a.createElement(ot,ve({},t,{ref:r,icon:ai}))},dd=a.forwardRef(li),ii={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 288.6L639.4 73.4c-6-6-14.1-9.4-22.6-9.4H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V311.3c0-8.5-3.4-16.7-9.4-22.7zM790.2 326H602V137.8L790.2 326zm1.8 562H232V136h302v216a42 42 0 0042 42h216v494z"}}]},name:"file",theme:"outlined"},si=function(t,r){return a.createElement(ot,ve({},t,{ref:r,icon:ii}))},Do=a.forwardRef(si),ci={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M349 838c0 17.7 14.2 32 31.8 32h262.4c17.6 0 31.8-14.3 31.8-32V642H349v196zm531.1-684H143.9c-24.5 0-39.8 26.7-27.5 48l221.3 376h348.8l221.3-376c12.1-21.3-3.2-48-27.7-48z"}}]},name:"filter",theme:"filled"},di=function(t,r){return a.createElement(ot,ve({},t,{ref:r,icon:ci}))},ui=a.forwardRef(di),fi={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M928 444H820V330.4c0-17.7-14.3-32-32-32H473L355.7 186.2a8.15 8.15 0 00-5.5-2.2H96c-17.7 0-32 14.3-32 32v592c0 17.7 14.3 32 32 32h698c13 0 24.8-7.9 29.7-20l134-332c1.5-3.8 2.3-7.9 2.3-12 0-17.7-14.3-32-32-32zM136 256h188.5l119.6 114.4H748V444H238c-13 0-24.8 7.9-29.7 20L136 643.2V256zm635.3 512H159l103.3-256h612.4L771.3 768z"}}]},name:"folder-open",theme:"outlined"},vi=function(t,r){return a.createElement(ot,ve({},t,{ref:r,icon:fi}))},pi=a.forwardRef(vi),mi={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880 298.4H521L403.7 186.2a8.15 8.15 0 00-5.5-2.2H144c-17.7 0-32 14.3-32 32v592c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V330.4c0-17.7-14.3-32-32-32zM840 768H184V256h188.5l119.6 114.4H840V768z"}}]},name:"folder",theme:"outlined"},gi=function(t,r){return a.createElement(ot,ve({},t,{ref:r,icon:mi}))},hi=a.forwardRef(gi),bi={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M300 276.5a56 56 0 1056-97 56 56 0 00-56 97zm0 284a56 56 0 1056-97 56 56 0 00-56 97zM640 228a56 56 0 10112 0 56 56 0 00-112 0zm0 284a56 56 0 10112 0 56 56 0 00-112 0zM300 844.5a56 56 0 1056-97 56 56 0 00-56 97zM640 796a56 56 0 10112 0 56 56 0 00-112 0z"}}]},name:"holder",theme:"outlined"},yi=function(t,r){return a.createElement(ot,ve({},t,{ref:r,icon:bi}))},Ci=a.forwardRef(yi),xi={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M328 544h368c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H328c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8z"}},{tag:"path",attrs:{d:"M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zm-40 728H184V184h656v656z"}}]},name:"minus-square",theme:"outlined"},Si=function(t,r){return a.createElement(ot,ve({},t,{ref:r,icon:xi}))},wi=a.forwardRef(Si),Ei={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M328 544h152v152c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V544h152c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H544V328c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v152H328c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8z"}},{tag:"path",attrs:{d:"M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zm-40 728H184V184h656v656z"}}]},name:"plus-square",theme:"outlined"},$i=function(t,r){return a.createElement(ot,ve({},t,{ref:r,icon:Ei}))},ki=a.forwardRef($i),Ni={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M909.1 209.3l-56.4 44.1C775.8 155.1 656.2 92 521.9 92 290 92 102.3 279.5 102 511.5 101.7 743.7 289.8 932 521.9 932c181.3 0 335.8-115 394.6-276.1 1.5-4.2-.7-8.9-4.9-10.3l-56.7-19.5a8 8 0 00-10.1 4.8c-1.8 5-3.8 10-5.9 14.9-17.3 41-42.1 77.8-73.7 109.4A344.77 344.77 0 01655.9 829c-42.3 17.9-87.4 27-133.8 27-46.5 0-91.5-9.1-133.8-27A341.5 341.5 0 01279 755.2a342.16 342.16 0 01-73.7-109.4c-17.9-42.4-27-87.4-27-133.9s9.1-91.5 27-133.9c17.3-41 42.1-77.8 73.7-109.4 31.6-31.6 68.4-56.4 109.3-73.8 42.3-17.9 87.4-27 133.8-27 46.5 0 91.5 9.1 133.8 27a341.5 341.5 0 01109.3 73.8c9.9 9.9 19.2 20.4 27.8 31.4l-60.2 47a8 8 0 003 14.1l175.6 43c5 1.2 9.9-2.6 9.9-7.7l.8-180.9c-.1-6.6-7.8-10.3-13-6.2z"}}]},name:"reload",theme:"outlined"},Ri=function(t,r){return a.createElement(ot,ve({},t,{ref:r,icon:Ni}))},ud=a.forwardRef(Ri),ut={},Qt="rc-table-internal-hook";function Vn(e){var t=a.createContext(void 0),r=function(o){var i=o.value,d=o.children,l=a.useRef(i);l.current=i;var c=a.useState(function(){return{getValue:function(){return l.current},listeners:new Set}}),s=xe(c,1),v=s[0];return Kt(function(){za.unstable_batchedUpdates(function(){v.listeners.forEach(function(u){u(i)})})},[i]),a.createElement(t.Provider,{value:v},d)};return{Context:t,Provider:r,defaultValue:e}}function ze(e,t){var r=bt(typeof t=="function"?t:function(u){if(t===void 0)return u;if(!Array.isArray(t))return u[t];var p={};return t.forEach(function(f){p[f]=u[f]}),p}),n=a.useContext(e==null?void 0:e.Context),o=n||{},i=o.listeners,d=o.getValue,l=a.useRef();l.current=r(n?d():e==null?void 0:e.defaultValue);var c=a.useState({}),s=xe(c,2),v=s[1];return Kt(function(){if(!n)return;function u(p){var f=r(p);Vt(l.current,f,!0)||v({})}return i.add(u),function(){i.delete(u)}},[n]),l.current}function Oi(){var e=a.createContext(null);function t(){return a.useContext(e)}function r(o,i){var d=Cr(o),l=function(s,v){var u=d?{ref:v}:{},p=a.useRef(0),f=a.useRef(s),g=t();return g!==null?a.createElement(o,ve({},s,u)):((!i||i(f.current,s))&&(p.current+=1),f.current=s,a.createElement(e.Provider,{value:p.current},a.createElement(o,ve({},s,u))))};return d?a.forwardRef(l):l}function n(o,i){var d=Cr(o),l=function(s,v){var u=d?{ref:v}:{};return t(),a.createElement(o,ve({},s,u))};return d?a.memo(a.forwardRef(l),i):a.memo(l,i)}return{makeImmutable:r,responseImmutable:n,useImmutableMark:t}}var qn=Oi(),Mo=qn.makeImmutable,Bt=qn.responseImmutable,Ii=qn.useImmutableMark,Ge=Vn(),Bo=a.createContext({renderWithProps:!1}),Pi="RC_TABLE_KEY";function Ki(e){return e==null?[]:Array.isArray(e)?e:[e]}function un(e){var t=[],r={};return e.forEach(function(n){for(var o=n||{},i=o.key,d=o.dataIndex,l=i||Ki(d).join("-")||Pi;r[l];)l="".concat(l,"_next");r[l]=!0,t.push(l)}),t}function In(e){return e!=null}function Ti(e){return typeof e=="number"&&!Number.isNaN(e)}function Di(e){return e&&st(e)==="object"&&!Array.isArray(e)&&!a.isValidElement(e)}function Mi(e,t,r,n,o,i){var d=a.useContext(Bo),l=Ii(),c=vo(function(){if(In(n))return[n];var s=t==null||t===""?[]:Array.isArray(t)?t:[t],v=jn(e,s),u=v,p=void 0;if(o){var f=o(v,e,r);Di(f)?(u=f.children,p=f.props,d.renderWithProps=!0):u=f}return[u,p]},[l,e,n,t,o,r],function(s,v){if(i){var u=xe(s,2),p=u[1],f=xe(v,2),g=f[1];return i(g,p)}return d.renderWithProps?!0:!Vt(s,v,!0)});return c}function Bi(e,t,r,n){var o=e+t-1;return e<=n&&o>=r}function Li(e,t){return ze(Ge,function(r){var n=Bi(e,t||1,r.hoverStartRow,r.hoverEndRow);return[n,r.onHover]})}var _i=function(t){var r=t.ellipsis,n=t.rowType,o=t.children,i,d=r===!0?{showTitle:!0}:r;return d&&(d.showTitle||n==="header")&&(typeof o=="string"||typeof o=="number"?i=o.toString():a.isValidElement(o)&&typeof o.props.children=="string"&&(i=o.props.children)),i};function Hi(e){var t,r,n,o,i,d,l,c,s=e.component,v=e.children,u=e.ellipsis,p=e.scope,f=e.prefixCls,g=e.className,y=e.align,m=e.record,h=e.render,x=e.dataIndex,C=e.renderIndex,S=e.shouldCellUpdate,E=e.index,$=e.rowType,k=e.colSpan,R=e.rowSpan,b=e.fixLeft,T=e.fixRight,D=e.firstFixLeft,P=e.lastFixLeft,O=e.firstFixRight,N=e.lastFixRight,w=e.appendNode,I=e.additionalProps,K=I===void 0?{}:I,L=e.isSticky,M="".concat(f,"-cell"),V=ze(Ge,["supportSticky","allColumnsFixedLeft","rowHoverable"]),Q=V.supportSticky,U=V.allColumnsFixedLeft,te=V.rowHoverable,ye=Mi(m,x,C,v,h,S),Se=xe(ye,2),Ee=Se[0],Z=Se[1],J={},$e=typeof b=="number"&&Q,ge=typeof T=="number"&&Q;$e&&(J.position="sticky",J.left=b),ge&&(J.position="sticky",J.right=T);var q=(t=(r=(n=Z==null?void 0:Z.colSpan)!==null&&n!==void 0?n:K.colSpan)!==null&&r!==void 0?r:k)!==null&&t!==void 0?t:1,X=(o=(i=(d=Z==null?void 0:Z.rowSpan)!==null&&d!==void 0?d:K.rowSpan)!==null&&i!==void 0?i:R)!==null&&o!==void 0?o:1,z=Li(E,X),A=xe(z,2),ee=A[0],oe=A[1],re=bt(function(he){var de;m&&oe(E,E+X-1),K==null||(de=K.onMouseEnter)===null||de===void 0||de.call(K,he)}),Ne=bt(function(he){var de;m&&oe(-1,-1),K==null||(de=K.onMouseLeave)===null||de===void 0||de.call(K,he)});if(q===0||X===0)return null;var je=(l=K.title)!==null&&l!==void 0?l:_i({rowType:$,ellipsis:u,children:Ee}),Oe=G(M,g,(c={},B(B(B(B(B(B(B(B(B(B(c,"".concat(M,"-fix-left"),$e&&Q),"".concat(M,"-fix-left-first"),D&&Q),"".concat(M,"-fix-left-last"),P&&Q),"".concat(M,"-fix-left-all"),P&&U&&Q),"".concat(M,"-fix-right"),ge&&Q),"".concat(M,"-fix-right-first"),O&&Q),"".concat(M,"-fix-right-last"),N&&Q),"".concat(M,"-ellipsis"),u),"".concat(M,"-with-append"),w),"".concat(M,"-fix-sticky"),($e||ge)&&L&&Q),B(c,"".concat(M,"-row-hover"),!Z&&ee)),K.className,Z==null?void 0:Z.className),_={};y&&(_.textAlign=y);var F=j(j(j(j({},Z==null?void 0:Z.style),J),_),K.style),ne=Ee;return st(ne)==="object"&&!Array.isArray(ne)&&!a.isValidElement(ne)&&(ne=null),u&&(P||O)&&(ne=a.createElement("span",{className:"".concat(M,"-content")},ne)),a.createElement(s,ve({},Z,K,{className:Oe,style:F,title:je,scope:p,onMouseEnter:te?re:void 0,onMouseLeave:te?Ne:void 0,colSpan:q!==1?q:null,rowSpan:X!==1?X:null}),w,ne)}const Lt=a.memo(Hi);function Xn(e,t,r,n,o){var i=r[e]||{},d=r[t]||{},l,c;i.fixed==="left"?l=n.left[o==="rtl"?t:e]:d.fixed==="right"&&(c=n.right[o==="rtl"?e:t]);var s=!1,v=!1,u=!1,p=!1,f=r[t+1],g=r[e-1],y=f&&!f.fixed||g&&!g.fixed||r.every(function(S){return S.fixed==="left"});if(o==="rtl"){if(l!==void 0){var m=g&&g.fixed==="left";p=!m&&y}else if(c!==void 0){var h=f&&f.fixed==="right";u=!h&&y}}else if(l!==void 0){var x=f&&f.fixed==="left";s=!x&&y}else if(c!==void 0){var C=g&&g.fixed==="right";v=!C&&y}return{fixLeft:l,fixRight:c,lastFixLeft:s,firstFixRight:v,lastFixRight:u,firstFixLeft:p,isSticky:n.isSticky}}var Lo=a.createContext({});function zi(e){var t=e.className,r=e.index,n=e.children,o=e.colSpan,i=o===void 0?1:o,d=e.rowSpan,l=e.align,c=ze(Ge,["prefixCls","direction"]),s=c.prefixCls,v=c.direction,u=a.useContext(Lo),p=u.scrollColumnIndex,f=u.stickyOffsets,g=u.flattenColumns,y=r+i-1,m=y+1===p?i+1:i,h=Xn(r,r+m-1,g,f,v);return a.createElement(Lt,ve({className:t,index:r,component:"td",prefixCls:s,record:null,dataIndex:null,align:l,colSpan:m,rowSpan:d,render:function(){return n}},h))}var ji=["children"];function Fi(e){var t=e.children,r=nt(e,ji);return a.createElement("tr",r,t)}function fn(e){var t=e.children;return t}fn.Row=Fi;fn.Cell=zi;function Ai(e){var t=e.children,r=e.stickyOffsets,n=e.flattenColumns,o=ze(Ge,"prefixCls"),i=n.length-1,d=n[i],l=a.useMemo(function(){return{stickyOffsets:r,flattenColumns:n,scrollColumnIndex:d!=null&&d.scrollbar?i:null}},[d,n,i,r]);return a.createElement(Lo.Provider,{value:l},a.createElement("tfoot",{className:"".concat(o,"-summary")},t))}const nn=Bt(Ai);var _o=fn;function Wi(e){return null}function Vi(e){return null}function Ho(e,t,r,n,o,i,d){e.push({record:t,indent:r,index:d});var l=i(t),c=o==null?void 0:o.has(l);if(t&&Array.isArray(t[n])&&c)for(var s=0;s<t[n].length;s+=1)Ho(e,t[n][s],r+1,n,o,i,s)}function zo(e,t,r,n){var o=a.useMemo(function(){if(r!=null&&r.size){for(var i=[],d=0;d<(e==null?void 0:e.length);d+=1){var l=e[d];Ho(i,l,0,t,r,n,d)}return i}return e==null?void 0:e.map(function(c,s){return{record:c,indent:0,index:s}})},[e,t,r,n]);return o}function jo(e,t,r,n){var o=ze(Ge,["prefixCls","fixedInfoList","flattenColumns","expandableType","expandRowByClick","onTriggerExpand","rowClassName","expandedRowClassName","indentSize","expandIcon","expandedRowRender","expandIconColumnIndex","expandedKeys","childrenColumnName","rowExpandable","onRow"]),i=o.flattenColumns,d=o.expandableType,l=o.expandedKeys,c=o.childrenColumnName,s=o.onTriggerExpand,v=o.rowExpandable,u=o.onRow,p=o.expandRowByClick,f=o.rowClassName,g=d==="nest",y=d==="row"&&(!v||v(e)),m=y||g,h=l&&l.has(t),x=c&&e&&e[c],C=bt(s),S=u==null?void 0:u(e,r),E=S==null?void 0:S.onClick,$=function(T){p&&m&&s(e,T);for(var D=arguments.length,P=new Array(D>1?D-1:0),O=1;O<D;O++)P[O-1]=arguments[O];E==null||E.apply(void 0,[T].concat(P))},k;typeof f=="string"?k=f:typeof f=="function"&&(k=f(e,r,n));var R=un(i);return j(j({},o),{},{columnsKey:R,nestExpandable:g,expanded:h,hasNestChildren:x,record:e,onTriggerExpand:C,rowSupportExpand:y,expandable:m,rowProps:j(j({},S),{},{className:G(k,S==null?void 0:S.className),onClick:$})})}function Fo(e){var t=e.prefixCls,r=e.children,n=e.component,o=e.cellComponent,i=e.className,d=e.expanded,l=e.colSpan,c=e.isEmpty,s=ze(Ge,["scrollbarSize","fixHeader","fixColumn","componentWidth","horizonScroll"]),v=s.scrollbarSize,u=s.fixHeader,p=s.fixColumn,f=s.componentWidth,g=s.horizonScroll,y=r;return(c?g&&f:p)&&(y=a.createElement("div",{style:{width:f-(u&&!c?v:0),position:"sticky",left:0,overflow:"hidden"},className:"".concat(t,"-expanded-row-fixed")},y)),a.createElement(n,{className:i,style:{display:d?null:"none"}},a.createElement(Lt,{component:o,prefixCls:t,colSpan:l},y))}function qi(e){var t=e.prefixCls,r=e.record,n=e.onExpand,o=e.expanded,i=e.expandable,d="".concat(t,"-row-expand-icon");if(!i)return a.createElement("span",{className:G(d,"".concat(t,"-row-spaced"))});var l=function(s){n(r,s),s.stopPropagation()};return a.createElement("span",{className:G(d,B(B({},"".concat(t,"-row-expanded"),o),"".concat(t,"-row-collapsed"),!o)),onClick:l})}function Xi(e,t,r){var n=[];function o(i){(i||[]).forEach(function(d,l){n.push(t(d,l)),o(d[r])})}return o(e),n}function Ao(e,t,r,n){return typeof e=="string"?e:typeof e=="function"?e(t,r,n):""}function Wo(e,t,r,n,o){var i=e.record,d=e.prefixCls,l=e.columnsKey,c=e.fixedInfoList,s=e.expandIconColumnIndex,v=e.nestExpandable,u=e.indentSize,p=e.expandIcon,f=e.expanded,g=e.hasNestChildren,y=e.onTriggerExpand,m=l[r],h=c[r],x;r===(s||0)&&v&&(x=a.createElement(a.Fragment,null,a.createElement("span",{style:{paddingLeft:"".concat(u*n,"px")},className:"".concat(d,"-row-indent indent-level-").concat(n)}),p({prefixCls:d,expanded:f,expandable:g,record:i,onExpand:y})));var C;return t.onCell&&(C=t.onCell(i,o)),{key:m,fixedInfo:h,appendCellNode:x,additionalCellProps:C||{}}}function Gi(e){var t=e.className,r=e.style,n=e.record,o=e.index,i=e.renderIndex,d=e.rowKey,l=e.indent,c=l===void 0?0:l,s=e.rowComponent,v=e.cellComponent,u=e.scopeCellComponent,p=jo(n,d,o,c),f=p.prefixCls,g=p.flattenColumns,y=p.expandedRowClassName,m=p.expandedRowRender,h=p.rowProps,x=p.expanded,C=p.rowSupportExpand,S=a.useRef(!1);S.current||(S.current=x);var E=Ao(y,n,o,c),$=a.createElement(s,ve({},h,{"data-row-key":d,className:G(t,"".concat(f,"-row"),"".concat(f,"-row-level-").concat(c),h==null?void 0:h.className,B({},E,c>=1)),style:j(j({},r),h==null?void 0:h.style)}),g.map(function(b,T){var D=b.render,P=b.dataIndex,O=b.className,N=Wo(p,b,T,c,o),w=N.key,I=N.fixedInfo,K=N.appendCellNode,L=N.additionalCellProps;return a.createElement(Lt,ve({className:O,ellipsis:b.ellipsis,align:b.align,scope:b.rowScope,component:b.rowScope?u:v,prefixCls:f,key:w,record:n,index:o,renderIndex:i,dataIndex:P,render:D,shouldCellUpdate:b.shouldCellUpdate},I,{appendNode:K,additionalProps:L}))})),k;if(C&&(S.current||x)){var R=m(n,o,c+1,x);k=a.createElement(Fo,{expanded:x,className:G("".concat(f,"-expanded-row"),"".concat(f,"-expanded-row-level-").concat(c+1),E),prefixCls:f,component:s,cellComponent:v,colSpan:g.length,isEmpty:!1},R)}return a.createElement(a.Fragment,null,$,k)}const Ui=Bt(Gi);function Yi(e){var t=e.columnKey,r=e.onColumnResize,n=a.useRef();return a.useEffect(function(){n.current&&r(t,n.current.offsetWidth)},[]),a.createElement(Fn,{data:t},a.createElement("td",{ref:n,style:{padding:0,border:0,height:0}},a.createElement("div",{style:{height:0,overflow:"hidden"}}," ")))}function Qi(e){var t=e.prefixCls,r=e.columnsKey,n=e.onColumnResize;return a.createElement("tr",{"aria-hidden":"true",className:"".concat(t,"-measure-row"),style:{height:0,fontSize:0}},a.createElement(Fn.Collection,{onBatchResize:function(i){i.forEach(function(d){var l=d.data,c=d.size;n(l,c.offsetWidth)})}},r.map(function(o){return a.createElement(Yi,{key:o,columnKey:o,onColumnResize:n})})))}function Zi(e){var t=e.data,r=e.measureColumnWidth,n=ze(Ge,["prefixCls","getComponent","onColumnResize","flattenColumns","getRowKey","expandedKeys","childrenColumnName","emptyNode"]),o=n.prefixCls,i=n.getComponent,d=n.onColumnResize,l=n.flattenColumns,c=n.getRowKey,s=n.expandedKeys,v=n.childrenColumnName,u=n.emptyNode,p=zo(t,v,s,c),f=a.useRef({renderWithProps:!1}),g=i(["body","wrapper"],"tbody"),y=i(["body","row"],"tr"),m=i(["body","cell"],"td"),h=i(["body","cell"],"th"),x;t.length?x=p.map(function(S,E){var $=S.record,k=S.indent,R=S.index,b=c($,E);return a.createElement(Ui,{key:b,rowKey:b,record:$,index:E,renderIndex:R,rowComponent:y,cellComponent:m,scopeCellComponent:h,indent:k})}):x=a.createElement(Fo,{expanded:!0,className:"".concat(o,"-placeholder"),prefixCls:o,component:y,cellComponent:m,colSpan:l.length,isEmpty:!0},u);var C=un(l);return a.createElement(Bo.Provider,{value:f.current},a.createElement(g,{className:"".concat(o,"-tbody")},r&&a.createElement(Qi,{prefixCls:o,columnsKey:C,onColumnResize:d}),x))}const Ji=Bt(Zi);var es=["expandable"],At="RC_TABLE_INTERNAL_COL_DEFINE";function ts(e){var t=e.expandable,r=nt(e,es),n;return"expandable"in e?n=j(j({},r),t):n=r,n.showExpandColumn===!1&&(n.expandIconColumnIndex=-1),n}var ns=["columnType"];function Vo(e){for(var t=e.colWidths,r=e.columns,n=e.columCount,o=ze(Ge,["tableLayout"]),i=o.tableLayout,d=[],l=n||r.length,c=!1,s=l-1;s>=0;s-=1){var v=t[s],u=r&&r[s],p=void 0,f=void 0;if(u&&(p=u[At],i==="auto"&&(f=u.minWidth)),v||f||p||c){var g=p||{};g.columnType;var y=nt(g,ns);d.unshift(a.createElement("col",ve({key:s,style:{width:v,minWidth:f}},y))),c=!0}}return a.createElement("colgroup",null,d)}var rs=["className","noData","columns","flattenColumns","colWidths","columCount","stickyOffsets","direction","fixHeader","stickyTopOffset","stickyBottomOffset","stickyClassName","onScroll","maxContentScroll","children"];function os(e,t){return a.useMemo(function(){for(var r=[],n=0;n<t;n+=1){var o=e[n];if(o!==void 0)r[n]=o;else return null}return r},[e.join("_"),t])}var as=a.forwardRef(function(e,t){var r=e.className,n=e.noData,o=e.columns,i=e.flattenColumns,d=e.colWidths,l=e.columCount,c=e.stickyOffsets,s=e.direction,v=e.fixHeader,u=e.stickyTopOffset,p=e.stickyBottomOffset,f=e.stickyClassName,g=e.onScroll,y=e.maxContentScroll,m=e.children,h=nt(e,rs),x=ze(Ge,["prefixCls","scrollbarSize","isSticky","getComponent"]),C=x.prefixCls,S=x.scrollbarSize,E=x.isSticky,$=x.getComponent,k=$(["header","table"],"table"),R=E&&!v?0:S,b=a.useRef(null),T=a.useCallback(function(L){xr(t,L),xr(b,L)},[]);a.useEffect(function(){var L;function M(V){var Q=V,U=Q.currentTarget,te=Q.deltaX;te&&(g({currentTarget:U,scrollLeft:U.scrollLeft+te}),V.preventDefault())}return(L=b.current)===null||L===void 0||L.addEventListener("wheel",M,{passive:!1}),function(){var V;(V=b.current)===null||V===void 0||V.removeEventListener("wheel",M)}},[]);var D=a.useMemo(function(){return i.every(function(L){return L.width})},[i]),P=i[i.length-1],O={fixed:P?P.fixed:null,scrollbar:!0,onHeaderCell:function(){return{className:"".concat(C,"-cell-scrollbar")}}},N=a.useMemo(function(){return R?[].concat(ce(o),[O]):o},[R,o]),w=a.useMemo(function(){return R?[].concat(ce(i),[O]):i},[R,i]),I=a.useMemo(function(){var L=c.right,M=c.left;return j(j({},c),{},{left:s==="rtl"?[].concat(ce(M.map(function(V){return V+R})),[0]):M,right:s==="rtl"?L:[].concat(ce(L.map(function(V){return V+R})),[0]),isSticky:E})},[R,c,E]),K=os(d,l);return a.createElement("div",{style:j({overflow:"hidden"},E?{top:u,bottom:p}:{}),ref:T,className:G(r,B({},f,!!f))},a.createElement(k,{style:{tableLayout:"fixed",visibility:n||K?null:"hidden"}},(!n||!y||D)&&a.createElement(Vo,{colWidths:K?[].concat(ce(K),[R]):[],columCount:l+1,columns:w}),m(j(j({},h),{},{stickyOffsets:I,columns:N,flattenColumns:w}))))});const Rr=a.memo(as);var ls=function(t){var r=t.cells,n=t.stickyOffsets,o=t.flattenColumns,i=t.rowComponent,d=t.cellComponent,l=t.onHeaderRow,c=t.index,s=ze(Ge,["prefixCls","direction"]),v=s.prefixCls,u=s.direction,p;l&&(p=l(r.map(function(g){return g.column}),c));var f=un(r.map(function(g){return g.column}));return a.createElement(i,p,r.map(function(g,y){var m=g.column,h=Xn(g.colStart,g.colEnd,o,n,u),x;return m&&m.onHeaderCell&&(x=g.column.onHeaderCell(m)),a.createElement(Lt,ve({},g,{scope:m.title?g.colSpan>1?"colgroup":"col":null,ellipsis:m.ellipsis,align:m.align,component:d,prefixCls:v,key:f[y]},h,{additionalProps:x,rowType:"header"}))}))};function is(e){var t=[];function r(d,l){var c=arguments.length>2&&arguments[2]!==void 0?arguments[2]:0;t[c]=t[c]||[];var s=l,v=d.filter(Boolean).map(function(u){var p={key:u.key,className:u.className||"",children:u.title,column:u,colStart:s},f=1,g=u.children;return g&&g.length>0&&(f=r(g,s,c+1).reduce(function(y,m){return y+m},0),p.hasSubColumns=!0),"colSpan"in u&&(f=u.colSpan),"rowSpan"in u&&(p.rowSpan=u.rowSpan),p.colSpan=f,p.colEnd=p.colStart+f-1,t[c].push(p),s+=f,f});return v}r(e,0);for(var n=t.length,o=function(l){t[l].forEach(function(c){!("rowSpan"in c)&&!c.hasSubColumns&&(c.rowSpan=n-l)})},i=0;i<n;i+=1)o(i);return t}var ss=function(t){var r=t.stickyOffsets,n=t.columns,o=t.flattenColumns,i=t.onHeaderRow,d=ze(Ge,["prefixCls","getComponent"]),l=d.prefixCls,c=d.getComponent,s=a.useMemo(function(){return is(n)},[n]),v=c(["header","wrapper"],"thead"),u=c(["header","row"],"tr"),p=c(["header","cell"],"th");return a.createElement(v,{className:"".concat(l,"-thead")},s.map(function(f,g){var y=a.createElement(ls,{key:g,flattenColumns:o,cells:f,stickyOffsets:r,rowComponent:u,cellComponent:p,onHeaderRow:i,index:g});return y}))};const Or=Bt(ss);function Ir(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"";return typeof t=="number"?t:t.endsWith("%")?e*parseFloat(t)/100:null}function cs(e,t,r){return a.useMemo(function(){if(t&&t>0){var n=0,o=0;e.forEach(function(p){var f=Ir(t,p.width);f?n+=f:o+=1});var i=Math.max(t,r),d=Math.max(i-n,o),l=o,c=d/o,s=0,v=e.map(function(p){var f=j({},p),g=Ir(t,f.width);if(g)f.width=g;else{var y=Math.floor(c);f.width=l===1?d:y,d-=y,l-=1}return s+=f.width,f});if(s<i){var u=i/s;d=i,v.forEach(function(p,f){var g=Math.floor(p.width*u);p.width=f===v.length-1?d:g,d-=g})}return[v,Math.max(s,i)]}return[e,t]},[e,t,r])}var ds=["children"],us=["fixed"];function Gn(e){return co(e).filter(function(t){return a.isValidElement(t)}).map(function(t){var r=t.key,n=t.props,o=n.children,i=nt(n,ds),d=j({key:r},i);return o&&(d.children=Gn(o)),d})}function qo(e){return e.filter(function(t){return t&&st(t)==="object"&&!t.hidden}).map(function(t){var r=t.children;return r&&r.length>0?j(j({},t),{},{children:qo(r)}):t})}function Pn(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"key";return e.filter(function(r){return r&&st(r)==="object"}).reduce(function(r,n,o){var i=n.fixed,d=i===!0?"left":i,l="".concat(t,"-").concat(o),c=n.children;return c&&c.length>0?[].concat(ce(r),ce(Pn(c,l).map(function(s){return j({fixed:d},s)}))):[].concat(ce(r),[j(j({key:l},n),{},{fixed:d})])},[])}function fs(e){return e.map(function(t){var r=t.fixed,n=nt(t,us),o=r;return r==="left"?o="right":r==="right"&&(o="left"),j({fixed:o},n)})}function vs(e,t){var r=e.prefixCls,n=e.columns,o=e.children,i=e.expandable,d=e.expandedKeys,l=e.columnTitle,c=e.getRowKey,s=e.onTriggerExpand,v=e.expandIcon,u=e.rowExpandable,p=e.expandIconColumnIndex,f=e.direction,g=e.expandRowByClick,y=e.columnWidth,m=e.fixed,h=e.scrollWidth,x=e.clientWidth,C=a.useMemo(function(){var P=n||Gn(o)||[];return qo(P.slice())},[n,o]),S=a.useMemo(function(){if(i){var P=C.slice();if(!P.includes(ut)){var O=p||0;O>=0&&(O||m==="left"||!m)&&P.splice(O,0,ut),m==="right"&&P.splice(C.length,0,ut)}var N=P.indexOf(ut);P=P.filter(function(L,M){return L!==ut||M===N});var w=C[N],I;m?I=m:I=w?w.fixed:null;var K=B(B(B(B(B(B({},At,{className:"".concat(r,"-expand-icon-col"),columnType:"EXPAND_COLUMN"}),"title",l),"fixed",I),"className","".concat(r,"-row-expand-icon-cell")),"width",y),"render",function(M,V,Q){var U=c(V,Q),te=d.has(U),ye=u?u(V):!0,Se=v({prefixCls:r,expanded:te,expandable:ye,record:V,onExpand:s});return g?a.createElement("span",{onClick:function(Z){return Z.stopPropagation()}},Se):Se});return P.map(function(L){return L===ut?K:L})}return C.filter(function(L){return L!==ut})},[i,C,c,d,v,f]),E=a.useMemo(function(){var P=S;return t&&(P=t(P)),P.length||(P=[{render:function(){return null}}]),P},[t,S,f]),$=a.useMemo(function(){return f==="rtl"?fs(Pn(E)):Pn(E)},[E,f,h]),k=a.useMemo(function(){for(var P=-1,O=$.length-1;O>=0;O-=1){var N=$[O].fixed;if(N==="left"||N===!0){P=O;break}}if(P>=0)for(var w=0;w<=P;w+=1){var I=$[w].fixed;if(I!=="left"&&I!==!0)return!0}var K=$.findIndex(function(V){var Q=V.fixed;return Q==="right"});if(K>=0)for(var L=K;L<$.length;L+=1){var M=$[L].fixed;if(M!=="right")return!0}return!1},[$]),R=cs($,h,x),b=xe(R,2),T=b[0],D=b[1];return[E,T,D,k]}function ps(e,t,r){var n=ts(e),o=n.expandIcon,i=n.expandedRowKeys,d=n.defaultExpandedRowKeys,l=n.defaultExpandAllRows,c=n.expandedRowRender,s=n.onExpand,v=n.onExpandedRowsChange,u=n.childrenColumnName,p=o||qi,f=u||"children",g=a.useMemo(function(){return c?"row":e.expandable&&e.internalHooks===Qt&&e.expandable.__PARENT_RENDER_ICON__||t.some(function(E){return E&&st(E)==="object"&&E[f]})?"nest":!1},[!!c,t]),y=a.useState(function(){return d||(l?Xi(t,r,f):[])}),m=xe(y,2),h=m[0],x=m[1],C=a.useMemo(function(){return new Set(i||h||[])},[i,h]),S=a.useCallback(function(E){var $=r(E,t.indexOf(E)),k,R=C.has($);R?(C.delete($),k=ce(C)):k=[].concat(ce(C),[$]),x(k),s&&s(!R,E),v&&v(k)},[r,C,t,s,v]);return[n,g,C,p,f,S]}function ms(e,t,r){var n=e.map(function(o,i){return Xn(i,i,e,t,r)});return vo(function(){return n},[n],function(o,i){return!Vt(o,i)})}function Xo(e){var t=a.useRef(e),r=a.useState({}),n=xe(r,2),o=n[1],i=a.useRef(null),d=a.useRef([]);function l(c){d.current.push(c);var s=Promise.resolve();i.current=s,s.then(function(){if(i.current===s){var v=d.current,u=t.current;d.current=[],v.forEach(function(p){t.current=p(t.current)}),i.current=null,u!==t.current&&o({})}})}return a.useEffect(function(){return function(){i.current=null}},[]),[t.current,l]}function gs(e){var t=a.useRef(null),r=a.useRef();function n(){window.clearTimeout(r.current)}function o(d){t.current=d,n(),r.current=window.setTimeout(function(){t.current=null,r.current=void 0},100)}function i(){return t.current}return a.useEffect(function(){return n},[]),[o,i]}function hs(){var e=a.useState(-1),t=xe(e,2),r=t[0],n=t[1],o=a.useState(-1),i=xe(o,2),d=i[0],l=i[1],c=a.useCallback(function(s,v){n(s),l(v)},[]);return[r,d,c]}var Pr=ja()?window:null;function bs(e,t){var r=st(e)==="object"?e:{},n=r.offsetHeader,o=n===void 0?0:n,i=r.offsetSummary,d=i===void 0?0:i,l=r.offsetScroll,c=l===void 0?0:l,s=r.getContainer,v=s===void 0?function(){return Pr}:s,u=v()||Pr,p=!!e;return a.useMemo(function(){return{isSticky:p,stickyClassName:p?"".concat(t,"-sticky-holder"):"",offsetHeader:o,offsetSummary:d,offsetScroll:c,container:u}},[p,c,o,d,t,u])}function ys(e,t,r){var n=a.useMemo(function(){var o=t.length,i=function(s,v,u){for(var p=[],f=0,g=s;g!==v;g+=u)p.push(f),t[g].fixed&&(f+=e[g]||0);return p},d=i(0,o,1),l=i(o-1,-1,-1).reverse();return r==="rtl"?{left:l,right:d}:{left:d,right:l}},[e,t,r]);return n}function Kr(e){var t=e.className,r=e.children;return a.createElement("div",{className:t},r)}function Tr(e){var t=po(e),r=t.getBoundingClientRect(),n=document.documentElement;return{left:r.left+(window.pageXOffset||n.scrollLeft)-(n.clientLeft||document.body.clientLeft||0),top:r.top+(window.pageYOffset||n.scrollTop)-(n.clientTop||document.body.clientTop||0)}}var Cs=function(t,r){var n,o,i=t.scrollBodyRef,d=t.onScroll,l=t.offsetScroll,c=t.container,s=t.direction,v=ze(Ge,"prefixCls"),u=((n=i.current)===null||n===void 0?void 0:n.scrollWidth)||0,p=((o=i.current)===null||o===void 0?void 0:o.clientWidth)||0,f=u&&p*(p/u),g=a.useRef(),y=Xo({scrollLeft:0,isHiddenScrollBar:!0}),m=xe(y,2),h=m[0],x=m[1],C=a.useRef({delta:0,x:0}),S=a.useState(!1),E=xe(S,2),$=E[0],k=E[1],R=a.useRef(null);a.useEffect(function(){return function(){Et.cancel(R.current)}},[]);var b=function(){k(!1)},T=function(w){w.persist(),C.current.delta=w.pageX-h.scrollLeft,C.current.x=0,k(!0),w.preventDefault()},D=function(w){var I,K=w||((I=window)===null||I===void 0?void 0:I.event),L=K.buttons;if(!$||L===0){$&&k(!1);return}var M=C.current.x+w.pageX-C.current.x-C.current.delta,V=s==="rtl";M=Math.max(V?f-p:0,Math.min(V?0:p-f,M));var Q=!V||Math.abs(M)+Math.abs(f)<p;Q&&(d({scrollLeft:M/p*(u+2)}),C.current.x=w.pageX)},P=function(){Et.cancel(R.current),R.current=Et(function(){if(i.current){var w=Tr(i.current).top,I=w+i.current.offsetHeight,K=c===window?document.documentElement.scrollTop+window.innerHeight:Tr(c).top+c.clientHeight;I-Sr()<=K||w>=K-l?x(function(L){return j(j({},L),{},{isHiddenScrollBar:!0})}):x(function(L){return j(j({},L),{},{isHiddenScrollBar:!1})})}})},O=function(w){x(function(I){return j(j({},I),{},{scrollLeft:w/u*p||0})})};return a.useImperativeHandle(r,function(){return{setScrollLeft:O,checkScrollBarVisible:P}}),a.useEffect(function(){var N=Nr(document.body,"mouseup",b,!1),w=Nr(document.body,"mousemove",D,!1);return P(),function(){N.remove(),w.remove()}},[f,$]),a.useEffect(function(){if(i.current){for(var N=[],w=i.current;w;)N.push(w),w=w.parentElement;return N.forEach(function(I){return I.addEventListener("scroll",P,!1)}),window.addEventListener("resize",P,!1),window.addEventListener("scroll",P,!1),c.addEventListener("scroll",P,!1),function(){N.forEach(function(I){return I.removeEventListener("scroll",P)}),window.removeEventListener("resize",P),window.removeEventListener("scroll",P),c.removeEventListener("scroll",P)}}},[c]),a.useEffect(function(){h.isHiddenScrollBar||x(function(N){var w=i.current;return w?j(j({},N),{},{scrollLeft:w.scrollLeft/w.scrollWidth*w.clientWidth}):N})},[h.isHiddenScrollBar]),u<=p||!f||h.isHiddenScrollBar?null:a.createElement("div",{style:{height:Sr(),width:p,bottom:l},className:"".concat(v,"-sticky-scroll")},a.createElement("div",{onMouseDown:T,ref:g,className:G("".concat(v,"-sticky-scroll-bar"),B({},"".concat(v,"-sticky-scroll-bar-active"),$)),style:{width:"".concat(f,"px"),transform:"translate3d(".concat(h.scrollLeft,"px, 0, 0)")}}))};const xs=a.forwardRef(Cs);var Go="rc-table",Ss=[],ws={};function Es(){return"No Data"}function $s(e,t){var r=j({rowKey:"key",prefixCls:Go,emptyText:Es},e),n=r.prefixCls,o=r.className,i=r.rowClassName,d=r.style,l=r.data,c=r.rowKey,s=r.scroll,v=r.tableLayout,u=r.direction,p=r.title,f=r.footer,g=r.summary,y=r.caption,m=r.id,h=r.showHeader,x=r.components,C=r.emptyText,S=r.onRow,E=r.onHeaderRow,$=r.onScroll,k=r.internalHooks,R=r.transformColumns,b=r.internalRefs,T=r.tailor,D=r.getContainerWidth,P=r.sticky,O=r.rowHoverable,N=O===void 0?!0:O,w=l||Ss,I=!!w.length,K=k===Qt,L=a.useCallback(function(se,pe){return jn(x,se)||pe},[x]),M=a.useMemo(function(){return typeof c=="function"?c:function(se){var pe=se&&se[c];return pe}},[c]),V=L(["body"]),Q=hs(),U=xe(Q,3),te=U[0],ye=U[1],Se=U[2],Ee=ps(r,w,M),Z=xe(Ee,6),J=Z[0],$e=Z[1],ge=Z[2],q=Z[3],X=Z[4],z=Z[5],A=s==null?void 0:s.x,ee=a.useState(0),oe=xe(ee,2),re=oe[0],Ne=oe[1],je=vs(j(j(j({},r),J),{},{expandable:!!J.expandedRowRender,columnTitle:J.columnTitle,expandedKeys:ge,getRowKey:M,onTriggerExpand:z,expandIcon:q,expandIconColumnIndex:J.expandIconColumnIndex,direction:u,scrollWidth:K&&T&&typeof A=="number"?A:null,clientWidth:re}),K?R:null),Oe=xe(je,4),_=Oe[0],F=Oe[1],ne=Oe[2],he=Oe[3],de=ne??A,we=a.useMemo(function(){return{columns:_,flattenColumns:F}},[_,F]),Te=a.useRef(),Le=a.useRef(),be=a.useRef(),ue=a.useRef();a.useImperativeHandle(t,function(){return{nativeElement:Te.current,scrollTo:function(pe){var _e;if(be.current instanceof HTMLElement){var tt=pe.index,He=pe.top,Ot=pe.key;if(Ti(He)){var St;(St=be.current)===null||St===void 0||St.scrollTo({top:He})}else{var wt,zt=Ot??M(w[tt]);(wt=be.current.querySelector('[data-row-key="'.concat(zt,'"]')))===null||wt===void 0||wt.scrollIntoView()}}else(_e=be.current)!==null&&_e!==void 0&&_e.scrollTo&&be.current.scrollTo(pe)}}});var Y=a.useRef(),H=a.useState(!1),ke=xe(H,2),Ie=ke[0],ae=ke[1],Re=a.useState(!1),Ce=xe(Re,2),De=Ce[0],Ue=Ce[1],Ve=Xo(new Map),vt=xe(Ve,2),Pe=vt[0],kt=vt[1],Nt=un(F),qe=Nt.map(function(se){return Pe.get(se)}),at=a.useMemo(function(){return qe},[qe.join("_")]),Ze=ys(at,F,u),Xe=s&&In(s.y),Fe=s&&In(de)||!!J.fixed,Ye=Fe&&F.some(function(se){var pe=se.fixed;return pe}),xt=a.useRef(),pt=bs(P,n),lt=pt.isSticky,pn=pt.offsetHeader,mn=pt.offsetSummary,Zt=pt.offsetScroll,gn=pt.stickyClassName,le=pt.container,ie=a.useMemo(function(){return g==null?void 0:g(w)},[g,w]),Ke=(Xe||lt)&&a.isValidElement(ie)&&ie.type===fn&&ie.props.fixed,Be,Ae,Je;Xe&&(Ae={overflowY:I?"scroll":"auto",maxHeight:s.y}),Fe&&(Be={overflowX:"auto"},Xe||(Ae={overflowY:"hidden"}),Je={width:de===!0?"auto":de,minWidth:"100%"});var et=a.useCallback(function(se,pe){Fa(Te.current)&&kt(function(_e){if(_e.get(se)!==pe){var tt=new Map(_e);return tt.set(se,pe),tt}return _e})},[]),We=gs(),tr=xe(We,2),ca=tr[0],nr=tr[1];function Jt(se,pe){pe&&(typeof pe=="function"?pe(se):pe.scrollLeft!==se&&(pe.scrollLeft=se,pe.scrollLeft!==se&&setTimeout(function(){pe.scrollLeft=se},0)))}var Rt=bt(function(se){var pe=se.currentTarget,_e=se.scrollLeft,tt=u==="rtl",He=typeof _e=="number"?_e:pe.scrollLeft,Ot=pe||ws;if(!nr()||nr()===Ot){var St;ca(Ot),Jt(He,Le.current),Jt(He,be.current),Jt(He,Y.current),Jt(He,(St=xt.current)===null||St===void 0?void 0:St.setScrollLeft)}var wt=pe||Le.current;if(wt){var zt=K&&T&&typeof de=="number"?de:wt.scrollWidth,xn=wt.clientWidth;if(zt===xn){ae(!1),Ue(!1);return}tt?(ae(-He<zt-xn),Ue(-He>0)):(ae(He>0),Ue(He<zt-xn))}}),da=bt(function(se){Rt(se),$==null||$(se)}),rr=function(){if(Fe&&be.current){var pe;Rt({currentTarget:po(be.current),scrollLeft:(pe=be.current)===null||pe===void 0?void 0:pe.scrollLeft})}else ae(!1),Ue(!1)},ua=function(pe){var _e,tt=pe.width;(_e=xt.current)===null||_e===void 0||_e.checkScrollBarVisible();var He=Te.current?Te.current.offsetWidth:tt;K&&D&&Te.current&&(He=D(Te.current,He)||He),He!==re&&(rr(),Ne(He))},or=a.useRef(!1);a.useEffect(function(){or.current&&rr()},[Fe,l,_.length]),a.useEffect(function(){or.current=!0},[]);var fa=a.useState(0),ar=xe(fa,2),en=ar[0],lr=ar[1],va=a.useState(!0),ir=xe(va,2),sr=ir[0],pa=ir[1];a.useEffect(function(){(!T||!K)&&(be.current instanceof Element?lr(wr(be.current).width):lr(wr(ue.current).width)),pa(Aa("position","sticky"))},[]),a.useEffect(function(){K&&b&&(b.body.current=be.current)});var ma=a.useCallback(function(se){return a.createElement(a.Fragment,null,a.createElement(Or,se),Ke==="top"&&a.createElement(nn,se,ie))},[Ke,ie]),ga=a.useCallback(function(se){return a.createElement(nn,se,ie)},[ie]),cr=L(["table"],"table"),tn=a.useMemo(function(){return v||(Ye?de==="max-content"?"auto":"fixed":Xe||lt||F.some(function(se){var pe=se.ellipsis;return pe})?"fixed":"auto")},[Xe,Ye,F,v,lt]),hn,bn={colWidths:at,columCount:F.length,stickyOffsets:Ze,onHeaderRow:E,fixHeader:Xe,scroll:s},dr=a.useMemo(function(){return I?null:typeof C=="function"?C():C},[I,C]),ur=a.createElement(Ji,{data:w,measureColumnWidth:Xe||Fe||lt}),fr=a.createElement(Vo,{colWidths:F.map(function(se){var pe=se.width;return pe}),columns:F}),vr=y!=null?a.createElement("caption",{className:"".concat(n,"-caption")},y):void 0,ha=Wt(r,{data:!0}),pr=Wt(r,{aria:!0});if(Xe||lt){var yn;typeof V=="function"?(yn=V(w,{scrollbarSize:en,ref:be,onScroll:Rt}),bn.colWidths=F.map(function(se,pe){var _e=se.width,tt=pe===F.length-1?_e-en:_e;return typeof tt=="number"&&!Number.isNaN(tt)?tt:0})):yn=a.createElement("div",{style:j(j({},Be),Ae),onScroll:da,ref:be,className:G("".concat(n,"-body"))},a.createElement(cr,ve({style:j(j({},Je),{},{tableLayout:tn})},pr),vr,fr,ur,!Ke&&ie&&a.createElement(nn,{stickyOffsets:Ze,flattenColumns:F},ie)));var mr=j(j(j({noData:!w.length,maxContentScroll:Fe&&de==="max-content"},bn),we),{},{direction:u,stickyClassName:gn,onScroll:Rt});hn=a.createElement(a.Fragment,null,h!==!1&&a.createElement(Rr,ve({},mr,{stickyTopOffset:pn,className:"".concat(n,"-header"),ref:Le}),ma),yn,Ke&&Ke!=="top"&&a.createElement(Rr,ve({},mr,{stickyBottomOffset:mn,className:"".concat(n,"-summary"),ref:Y}),ga),lt&&be.current&&be.current instanceof Element&&a.createElement(xs,{ref:xt,offsetScroll:Zt,scrollBodyRef:be,onScroll:Rt,container:le,direction:u}))}else hn=a.createElement("div",{style:j(j({},Be),Ae),className:G("".concat(n,"-content")),onScroll:Rt,ref:be},a.createElement(cr,ve({style:j(j({},Je),{},{tableLayout:tn})},pr),vr,fr,h!==!1&&a.createElement(Or,ve({},bn,we)),ur,ie&&a.createElement(nn,{stickyOffsets:Ze,flattenColumns:F},ie)));var Cn=a.createElement("div",ve({className:G(n,o,B(B(B(B(B(B(B(B(B(B({},"".concat(n,"-rtl"),u==="rtl"),"".concat(n,"-ping-left"),Ie),"".concat(n,"-ping-right"),De),"".concat(n,"-layout-fixed"),v==="fixed"),"".concat(n,"-fixed-header"),Xe),"".concat(n,"-fixed-column"),Ye),"".concat(n,"-fixed-column-gapped"),Ye&&he),"".concat(n,"-scroll-horizontal"),Fe),"".concat(n,"-has-fix-left"),F[0]&&F[0].fixed),"".concat(n,"-has-fix-right"),F[F.length-1]&&F[F.length-1].fixed==="right")),style:d,id:m,ref:Te},ha),p&&a.createElement(Kr,{className:"".concat(n,"-title")},p(w)),a.createElement("div",{ref:ue,className:"".concat(n,"-container")},hn),f&&a.createElement(Kr,{className:"".concat(n,"-footer")},f(w)));Fe&&(Cn=a.createElement(Fn,{onResize:ua},Cn));var gr=ms(F,Ze,u),ba=a.useMemo(function(){return{scrollX:de,prefixCls:n,getComponent:L,scrollbarSize:en,direction:u,fixedInfoList:gr,isSticky:lt,supportSticky:sr,componentWidth:re,fixHeader:Xe,fixColumn:Ye,horizonScroll:Fe,tableLayout:tn,rowClassName:i,expandedRowClassName:J.expandedRowClassName,expandIcon:q,expandableType:$e,expandRowByClick:J.expandRowByClick,expandedRowRender:J.expandedRowRender,onTriggerExpand:z,expandIconColumnIndex:J.expandIconColumnIndex,indentSize:J.indentSize,allColumnsFixedLeft:F.every(function(se){return se.fixed==="left"}),emptyNode:dr,columns:_,flattenColumns:F,onColumnResize:et,hoverStartRow:te,hoverEndRow:ye,onHover:Se,rowExpandable:J.rowExpandable,onRow:S,getRowKey:M,expandedKeys:ge,childrenColumnName:X,rowHoverable:N}},[de,n,L,en,u,gr,lt,sr,re,Xe,Ye,Fe,tn,i,J.expandedRowClassName,q,$e,J.expandRowByClick,J.expandedRowRender,z,J.expandIconColumnIndex,J.indentSize,dr,_,F,et,te,ye,Se,J.rowExpandable,S,M,ge,X,N]);return a.createElement(Ge.Provider,{value:ba},Cn)}var ks=a.forwardRef($s);function Uo(e){return Mo(ks,e)}var _t=Uo();_t.EXPAND_COLUMN=ut;_t.INTERNAL_HOOKS=Qt;_t.Column=Wi;_t.ColumnGroup=Vi;_t.Summary=_o;var Un=Vn(null),Yo=Vn(null);function Ns(e,t,r){var n=t||1;return r[e+n]-(r[e]||0)}function Rs(e){var t=e.rowInfo,r=e.column,n=e.colIndex,o=e.indent,i=e.index,d=e.component,l=e.renderIndex,c=e.record,s=e.style,v=e.className,u=e.inverse,p=e.getHeight,f=r.render,g=r.dataIndex,y=r.className,m=r.width,h=ze(Yo,["columnsOffset"]),x=h.columnsOffset,C=Wo(t,r,n,o,i),S=C.key,E=C.fixedInfo,$=C.appendCellNode,k=C.additionalCellProps,R=k.style,b=k.colSpan,T=b===void 0?1:b,D=k.rowSpan,P=D===void 0?1:D,O=n-1,N=Ns(O,T,x),w=T>1?m-N:0,I=j(j(j({},R),s),{},{flex:"0 0 ".concat(N,"px"),width:"".concat(N,"px"),marginRight:w,pointerEvents:"auto"}),K=a.useMemo(function(){return u?P<=1:T===0||P===0||P>1},[P,T,u]);K?I.visibility="hidden":u&&(I.height=p==null?void 0:p(P));var L=K?function(){return null}:f,M={};return(P===0||T===0)&&(M.rowSpan=1,M.colSpan=1),a.createElement(Lt,ve({className:G(y,v),ellipsis:r.ellipsis,align:r.align,scope:r.rowScope,component:d,prefixCls:t.prefixCls,key:S,record:c,index:i,renderIndex:l,dataIndex:g,render:L,shouldCellUpdate:r.shouldCellUpdate},E,{appendNode:$,additionalProps:j(j({},k),{},{style:I},M)}))}var Os=["data","index","className","rowKey","style","extra","getHeight"],Is=a.forwardRef(function(e,t){var r=e.data,n=e.index,o=e.className,i=e.rowKey,d=e.style,l=e.extra,c=e.getHeight,s=nt(e,Os),v=r.record,u=r.indent,p=r.index,f=ze(Ge,["prefixCls","flattenColumns","fixColumn","componentWidth","scrollX"]),g=f.scrollX,y=f.flattenColumns,m=f.prefixCls,h=f.fixColumn,x=f.componentWidth,C=ze(Un,["getComponent"]),S=C.getComponent,E=jo(v,i,n,u),$=S(["body","row"],"div"),k=S(["body","cell"],"div"),R=E.rowSupportExpand,b=E.expanded,T=E.rowProps,D=E.expandedRowRender,P=E.expandedRowClassName,O;if(R&&b){var N=D(v,n,u+1,b),w=Ao(P,v,n,u),I={};h&&(I={style:B({},"--virtual-width","".concat(x,"px"))});var K="".concat(m,"-expanded-row-cell");O=a.createElement($,{className:G("".concat(m,"-expanded-row"),"".concat(m,"-expanded-row-level-").concat(u+1),w)},a.createElement(Lt,{component:k,prefixCls:m,className:G(K,B({},"".concat(K,"-fixed"),h)),additionalProps:I},N))}var L=j(j({},d),{},{width:g});l&&(L.position="absolute",L.pointerEvents="none");var M=a.createElement($,ve({},T,s,{"data-row-key":i,ref:R?null:t,className:G(o,"".concat(m,"-row"),T==null?void 0:T.className,B({},"".concat(m,"-row-extra"),l)),style:j(j({},L),T==null?void 0:T.style)}),y.map(function(V,Q){return a.createElement(Rs,{key:Q,component:k,rowInfo:E,column:V,colIndex:Q,indent:u,index:n,renderIndex:p,record:v,inverse:l,getHeight:c})}));return R?a.createElement("div",{ref:t},M,O):M}),Dr=Bt(Is),Ps=a.forwardRef(function(e,t){var r=e.data,n=e.onScroll,o=ze(Ge,["flattenColumns","onColumnResize","getRowKey","prefixCls","expandedKeys","childrenColumnName","scrollX","direction"]),i=o.flattenColumns,d=o.onColumnResize,l=o.getRowKey,c=o.expandedKeys,s=o.prefixCls,v=o.childrenColumnName,u=o.scrollX,p=o.direction,f=ze(Un),g=f.sticky,y=f.scrollY,m=f.listItemHeight,h=f.getComponent,x=f.onScroll,C=a.useRef(),S=zo(r,v,c,l),E=a.useMemo(function(){var O=0;return i.map(function(N){var w=N.width,I=N.key;return O+=w,[I,w,O]})},[i]),$=a.useMemo(function(){return E.map(function(O){return O[2]})},[E]);a.useEffect(function(){E.forEach(function(O){var N=xe(O,2),w=N[0],I=N[1];d(w,I)})},[E]),a.useImperativeHandle(t,function(){var O,N={scrollTo:function(I){var K;(K=C.current)===null||K===void 0||K.scrollTo(I)},nativeElement:(O=C.current)===null||O===void 0?void 0:O.nativeElement};return Object.defineProperty(N,"scrollLeft",{get:function(){var I;return((I=C.current)===null||I===void 0?void 0:I.getScrollInfo().x)||0},set:function(I){var K;(K=C.current)===null||K===void 0||K.scrollTo({left:I})}}),N});var k=function(N,w){var I,K=(I=S[w])===null||I===void 0?void 0:I.record,L=N.onCell;if(L){var M,V=L(K,w);return(M=V==null?void 0:V.rowSpan)!==null&&M!==void 0?M:1}return 1},R=function(N){var w=N.start,I=N.end,K=N.getSize,L=N.offsetY;if(I<0)return null;for(var M=i.filter(function(q){return k(q,w)===0}),V=w,Q=function(X){if(M=M.filter(function(z){return k(z,X)===0}),!M.length)return V=X,1},U=w;U>=0&&!Q(U);U-=1);for(var te=i.filter(function(q){return k(q,I)!==1}),ye=I,Se=function(X){if(te=te.filter(function(z){return k(z,X)!==1}),!te.length)return ye=Math.max(X-1,I),1},Ee=I;Ee<S.length&&!Se(Ee);Ee+=1);for(var Z=[],J=function(X){var z=S[X];if(!z)return 1;i.some(function(A){return k(A,X)>1})&&Z.push(X)},$e=V;$e<=ye;$e+=1)J($e);var ge=Z.map(function(q){var X=S[q],z=l(X.record,q),A=function(re){var Ne=q+re-1,je=l(S[Ne].record,Ne),Oe=K(z,je);return Oe.bottom-Oe.top},ee=K(z);return a.createElement(Dr,{key:q,data:X,rowKey:z,index:q,style:{top:-L+ee.top},extra:!0,getHeight:A})});return ge},b=a.useMemo(function(){return{columnsOffset:$}},[$]),T="".concat(s,"-tbody"),D=h(["body","wrapper"]),P={};return g&&(P.position="sticky",P.bottom=0,st(g)==="object"&&g.offsetScroll&&(P.bottom=g.offsetScroll)),a.createElement(Yo.Provider,{value:b},a.createElement(ho,{fullHeight:!1,ref:C,prefixCls:"".concat(T,"-virtual"),styles:{horizontalScrollBar:P},className:T,height:y,itemHeight:m||24,data:S,itemKey:function(N){return l(N.record)},component:D,scrollWidth:u,direction:p,onVirtualScroll:function(N){var w,I=N.x;n({currentTarget:(w=C.current)===null||w===void 0?void 0:w.nativeElement,scrollLeft:I})},onScroll:x,extraRender:R},function(O,N,w){var I=l(O.record,N);return a.createElement(Dr,{data:O,rowKey:I,index:N,style:w.style})}))}),Ks=Bt(Ps),Ts=function(t,r){var n=r.ref,o=r.onScroll;return a.createElement(Ks,{ref:n,data:t,onScroll:o})};function Ds(e,t){var r=e.data,n=e.columns,o=e.scroll,i=e.sticky,d=e.prefixCls,l=d===void 0?Go:d,c=e.className,s=e.listItemHeight,v=e.components,u=e.onScroll,p=o||{},f=p.x,g=p.y;typeof f!="number"&&(f=1),typeof g!="number"&&(g=500);var y=bt(function(x,C){return jn(v,x)||C}),m=bt(u),h=a.useMemo(function(){return{sticky:i,scrollY:g,listItemHeight:s,getComponent:y,onScroll:m}},[i,g,s,y,m]);return a.createElement(Un.Provider,{value:h},a.createElement(_t,ve({},e,{className:G(c,"".concat(l,"-virtual")),scroll:j(j({},o),{},{x:f}),components:j(j({},v),{},{body:r!=null&&r.length?Ts:void 0}),columns:n,internalHooks:Qt,tailor:!0,ref:t})))}var Ms=a.forwardRef(Ds);function Qo(e){return Mo(Ms,e)}Qo();const Bs=e=>null,Ls=e=>null;var Yn=a.createContext(null),_s=a.createContext({}),Hs=function(t){for(var r=t.prefixCls,n=t.level,o=t.isStart,i=t.isEnd,d="".concat(r,"-indent-unit"),l=[],c=0;c<n;c+=1)l.push(a.createElement("span",{key:c,className:G(d,B(B({},"".concat(d,"-start"),o[c]),"".concat(d,"-end"),i[c]))}));return a.createElement("span",{"aria-hidden":"true",className:"".concat(r,"-indent")},l)};const zs=a.memo(Hs);var js=["eventKey","className","style","dragOver","dragOverGapTop","dragOverGapBottom","isLeaf","isStart","isEnd","expanded","selected","checked","halfChecked","loading","domRef","active","data","onMouseMove","selectable"],Mr="open",Br="close",Fs="---",qt=function(t){var r,n,o,i=t.eventKey,d=t.className,l=t.style,c=t.dragOver,s=t.dragOverGapTop,v=t.dragOverGapBottom,u=t.isLeaf,p=t.isStart,f=t.isEnd,g=t.expanded,y=t.selected,m=t.checked,h=t.halfChecked,x=t.loading,C=t.domRef,S=t.active,E=t.data,$=t.onMouseMove,k=t.selectable,R=nt(t,js),b=me.useContext(Yn),T=me.useContext(_s),D=me.useRef(null),P=me.useState(!1),O=xe(P,2),N=O[0],w=O[1],I=!!(b.disabled||t.disabled||(r=T.nodeDisabled)!==null&&r!==void 0&&r.call(T,E)),K=me.useMemo(function(){return!b.checkable||t.checkable===!1?!1:b.checkable},[b.checkable,t.checkable]),L=function(H){I||b.onNodeSelect(H,Me(t))},M=function(H){I||!K||t.disableCheckbox||b.onNodeCheck(H,Me(t),!m)},V=me.useMemo(function(){return typeof k=="boolean"?k:b.selectable},[k,b.selectable]),Q=function(H){b.onNodeClick(H,Me(t)),V?L(H):M(H)},U=function(H){b.onNodeDoubleClick(H,Me(t))},te=function(H){b.onNodeMouseEnter(H,Me(t))},ye=function(H){b.onNodeMouseLeave(H,Me(t))},Se=function(H){b.onNodeContextMenu(H,Me(t))},Ee=me.useMemo(function(){return!!(b.draggable&&(!b.draggable.nodeDraggable||b.draggable.nodeDraggable(E)))},[b.draggable,E]),Z=function(H){H.stopPropagation(),w(!0),b.onNodeDragStart(H,t);try{H.dataTransfer.setData("text/plain","")}catch{}},J=function(H){H.preventDefault(),H.stopPropagation(),b.onNodeDragEnter(H,t)},$e=function(H){H.preventDefault(),H.stopPropagation(),b.onNodeDragOver(H,t)},ge=function(H){H.stopPropagation(),b.onNodeDragLeave(H,t)},q=function(H){H.stopPropagation(),w(!1),b.onNodeDragEnd(H,t)},X=function(H){H.preventDefault(),H.stopPropagation(),w(!1),b.onNodeDrop(H,t)},z=function(H){x||b.onNodeExpand(H,Me(t))},A=me.useMemo(function(){var Y=Qe(b.keyEntities,i)||{},H=Y.children;return!!(H||[]).length},[b.keyEntities,i]),ee=me.useMemo(function(){return u===!1?!1:u||!b.loadData&&!A||b.loadData&&t.loaded&&!A},[u,b.loadData,A,t.loaded]);me.useEffect(function(){x||typeof b.loadData=="function"&&g&&!ee&&!t.loaded&&b.onNodeLoad(Me(t))},[x,b.loadData,b.onNodeLoad,g,ee,t]);var oe=me.useMemo(function(){var Y;return(Y=b.draggable)!==null&&Y!==void 0&&Y.icon?me.createElement("span",{className:"".concat(b.prefixCls,"-draggable-icon")},b.draggable.icon):null},[b.draggable]),re=function(H){var ke=t.switcherIcon||b.switcherIcon;return typeof ke=="function"?ke(j(j({},t),{},{isLeaf:H})):ke},Ne=function(){if(ee){var H=re(!0);return H!==!1?me.createElement("span",{className:G("".concat(b.prefixCls,"-switcher"),"".concat(b.prefixCls,"-switcher-noop"))},H):null}var ke=re(!1);return ke!==!1?me.createElement("span",{onClick:z,className:G("".concat(b.prefixCls,"-switcher"),"".concat(b.prefixCls,"-switcher_").concat(g?Mr:Br))},ke):null},je=me.useMemo(function(){if(!K)return null;var Y=typeof K!="boolean"?K:null;return me.createElement("span",{className:G("".concat(b.prefixCls,"-checkbox"),B(B(B({},"".concat(b.prefixCls,"-checkbox-checked"),m),"".concat(b.prefixCls,"-checkbox-indeterminate"),!m&&h),"".concat(b.prefixCls,"-checkbox-disabled"),I||t.disableCheckbox)),onClick:M,role:"checkbox","aria-checked":h?"mixed":m,"aria-disabled":I||t.disableCheckbox,"aria-label":"Select ".concat(typeof t.title=="string"?t.title:"tree node")},Y)},[K,m,h,I,t.disableCheckbox,t.title]),Oe=me.useMemo(function(){return ee?null:g?Mr:Br},[ee,g]),_=me.useMemo(function(){return me.createElement("span",{className:G("".concat(b.prefixCls,"-iconEle"),"".concat(b.prefixCls,"-icon__").concat(Oe||"docu"),B({},"".concat(b.prefixCls,"-icon_loading"),x))})},[b.prefixCls,Oe,x]),F=me.useMemo(function(){var Y=!!b.draggable,H=!t.disabled&&Y&&b.dragOverNodeKey===i;return H?b.dropIndicatorRender({dropPosition:b.dropPosition,dropLevelOffset:b.dropLevelOffset,indent:b.indent,prefixCls:b.prefixCls,direction:b.direction}):null},[b.dropPosition,b.dropLevelOffset,b.indent,b.prefixCls,b.direction,b.draggable,b.dragOverNodeKey,b.dropIndicatorRender]),ne=me.useMemo(function(){var Y=t.title,H=Y===void 0?Fs:Y,ke="".concat(b.prefixCls,"-node-content-wrapper"),Ie;if(b.showIcon){var ae=t.icon||b.icon;Ie=ae?me.createElement("span",{className:G("".concat(b.prefixCls,"-iconEle"),"".concat(b.prefixCls,"-icon__customize"))},typeof ae=="function"?ae(t):ae):_}else b.loadData&&x&&(Ie=_);var Re;return typeof H=="function"?Re=H(E):b.titleRender?Re=b.titleRender(E):Re=H,me.createElement("span",{ref:D,title:typeof H=="string"?H:"",className:G(ke,"".concat(ke,"-").concat(Oe||"normal"),B({},"".concat(b.prefixCls,"-node-selected"),!I&&(y||N))),onMouseEnter:te,onMouseLeave:ye,onContextMenu:Se,onClick:Q,onDoubleClick:U},Ie,me.createElement("span",{className:"".concat(b.prefixCls,"-title")},Re),F)},[b.prefixCls,b.showIcon,t,b.icon,_,b.titleRender,E,Oe,te,ye,Se,Q,U]),he=Wt(R,{aria:!0,data:!0}),de=Qe(b.keyEntities,i)||{},we=de.level,Te=f[f.length-1],Le=!I&&Ee,be=b.draggingNodeKey===i,ue=k!==void 0?{"aria-selected":!!k}:void 0;return me.createElement("div",ve({ref:C,role:"treeitem","aria-expanded":u?void 0:g,className:G(d,"".concat(b.prefixCls,"-treenode"),(o={},B(B(B(B(B(B(B(B(B(B(o,"".concat(b.prefixCls,"-treenode-disabled"),I),"".concat(b.prefixCls,"-treenode-switcher-").concat(g?"open":"close"),!u),"".concat(b.prefixCls,"-treenode-checkbox-checked"),m),"".concat(b.prefixCls,"-treenode-checkbox-indeterminate"),h),"".concat(b.prefixCls,"-treenode-selected"),y),"".concat(b.prefixCls,"-treenode-loading"),x),"".concat(b.prefixCls,"-treenode-active"),S),"".concat(b.prefixCls,"-treenode-leaf-last"),Te),"".concat(b.prefixCls,"-treenode-draggable"),Ee),"dragging",be),B(B(B(B(B(B(B(o,"drop-target",b.dropTargetKey===i),"drop-container",b.dropContainerKey===i),"drag-over",!I&&c),"drag-over-gap-top",!I&&s),"drag-over-gap-bottom",!I&&v),"filter-node",(n=b.filterTreeNode)===null||n===void 0?void 0:n.call(b,Me(t))),"".concat(b.prefixCls,"-treenode-leaf"),ee))),style:l,draggable:Le,onDragStart:Le?Z:void 0,onDragEnter:Ee?J:void 0,onDragOver:Ee?$e:void 0,onDragLeave:Ee?ge:void 0,onDrop:Ee?X:void 0,onDragEnd:Ee?q:void 0,onMouseMove:$},ue,he),me.createElement(zs,{prefixCls:b.prefixCls,level:we,isStart:p,isEnd:f}),oe,Ne(),je,ne)};qt.isTreeNode=1;function it(e,t){if(!e)return[];var r=e.slice(),n=r.indexOf(t);return n>=0&&r.splice(n,1),r}function dt(e,t){var r=(e||[]).slice();return r.indexOf(t)===-1&&r.push(t),r}function Qn(e){return e.split("-")}function As(e,t){var r=[],n=Qe(t,e);function o(){var i=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];i.forEach(function(d){var l=d.key,c=d.children;r.push(l),o(c)})}return o(n.children),r}function Ws(e){if(e.parent){var t=Qn(e.pos);return Number(t[t.length-1])===e.parent.children.length-1}return!1}function Vs(e){var t=Qn(e.pos);return Number(t[t.length-1])===0}function Lr(e,t,r,n,o,i,d,l,c,s){var v,u=e.clientX,p=e.clientY,f=e.target.getBoundingClientRect(),g=f.top,y=f.height,m=(s==="rtl"?-1:1)*(((o==null?void 0:o.x)||0)-u),h=(m-12)/n,x=c.filter(function(I){var K;return(K=l[I])===null||K===void 0||(K=K.children)===null||K===void 0?void 0:K.length}),C=Qe(l,r.eventKey);if(p<g+y/2){var S=d.findIndex(function(I){return I.key===C.key}),E=S<=0?0:S-1,$=d[E].key;C=Qe(l,$)}var k=C.key,R=C,b=C.key,T=0,D=0;if(!x.includes(k))for(var P=0;P<h&&Ws(C);P+=1)C=C.parent,D+=1;var O=t.data,N=C.node,w=!0;return Vs(C)&&C.level===0&&p<g+y/2&&i({dragNode:O,dropNode:N,dropPosition:-1})&&C.key===r.eventKey?T=-1:(R.children||[]).length&&x.includes(b)?i({dragNode:O,dropNode:N,dropPosition:0})?T=0:w=!1:D===0?h>-1.5?i({dragNode:O,dropNode:N,dropPosition:1})?T=1:w=!1:i({dragNode:O,dropNode:N,dropPosition:0})?T=0:i({dragNode:O,dropNode:N,dropPosition:1})?T=1:w=!1:i({dragNode:O,dropNode:N,dropPosition:1})?T=1:w=!1,{dropPosition:T,dropLevelOffset:D,dropTargetKey:C.key,dropTargetPos:C.pos,dragOverNodeKey:b,dropContainerKey:T===0?null:((v=C.parent)===null||v===void 0?void 0:v.key)||null,dropAllowed:w}}function _r(e,t){if(e){var r=t.multiple;return r?e.slice():e.length?[e[0]]:e}}function wn(e){if(!e)return null;var t;if(Array.isArray(e))t={checkedKeys:e,halfCheckedKeys:void 0};else if(st(e)==="object")t={checkedKeys:e.checked||void 0,halfCheckedKeys:e.halfChecked||void 0};else return ht(!1,"`checkedKeys` is not an array or an object"),null;return t}function Kn(e,t){var r=new Set;function n(o){if(!r.has(o)){var i=Qe(t,o);if(i){r.add(o);var d=i.parent,l=i.node;l.disabled||d&&n(d.key)}}}return(e||[]).forEach(function(o){n(o)}),ce(r)}function qs(e){const[t,r]=a.useState(null);return[a.useCallback((i,d,l)=>{const c=t??i,s=Math.min(c||0,i),v=Math.max(c||0,i),u=d.slice(s,v+1).map(g=>e(g)),p=u.some(g=>!l.has(g)),f=[];return u.forEach(g=>{p?(l.has(g)||f.push(g),l.add(g)):(l.delete(g),f.push(g))}),r(p?v:null),f},[t]),i=>{r(i)}]}const mt={},Tn="SELECT_ALL",Dn="SELECT_INVERT",Mn="SELECT_NONE",Hr=[],Zo=(e,t)=>{let r=[];return(t||[]).forEach(n=>{r.push(n),n&&typeof n=="object"&&e in n&&(r=[].concat(ce(r),ce(Zo(e,n[e]))))}),r},Xs=(e,t)=>{const{preserveSelectedRowKeys:r,selectedRowKeys:n,defaultSelectedRowKeys:o,getCheckboxProps:i,onChange:d,onSelect:l,onSelectAll:c,onSelectInvert:s,onSelectNone:v,onSelectMultiple:u,columnWidth:p,type:f,selections:g,fixed:y,renderCell:m,hideSelectAll:h,checkStrictly:x=!0}=t||{},{prefixCls:C,data:S,pageData:E,getRecordByKey:$,getRowKey:k,expandType:R,childrenColumnName:b,locale:T,getPopupContainer:D}=e,P=An(),[O,N]=qs(q=>q),[w,I]=ln(n||o||Hr,{value:n}),K=a.useRef(new Map),L=a.useCallback(q=>{if(r){const X=new Map;q.forEach(z=>{let A=$(z);!A&&K.current.has(z)&&(A=K.current.get(z)),X.set(z,A)}),K.current=X}},[$,r]);a.useEffect(()=>{L(w)},[w]);const M=a.useMemo(()=>Zo(b,E),[b,E]),{keyEntities:V}=a.useMemo(()=>{if(x)return{keyEntities:null};let q=S;if(r){const X=new Set(M.map((A,ee)=>k(A,ee))),z=Array.from(K.current).reduce((A,ee)=>{let[oe,re]=ee;return X.has(oe)?A:A.concat(re)},[]);q=[].concat(ce(q),ce(z))}return Wn(q,{externalGetKey:k,childrenPropName:b})},[S,k,x,b,r,M]),Q=a.useMemo(()=>{const q=new Map;return M.forEach((X,z)=>{const A=k(X,z),ee=(i?i(X):null)||{};q.set(A,ee)}),q},[M,k,i]),U=a.useCallback(q=>{const X=k(q);let z;return Q.has(X)?z=Q.get(k(q)):z=i?i(q):void 0,!!(z!=null&&z.disabled)},[Q,k]),[te,ye]=a.useMemo(()=>{if(x)return[w||[],[]];const{checkedKeys:q,halfCheckedKeys:X}=It(w,!0,V,U);return[q||[],X]},[w,x,V,U]),Se=a.useMemo(()=>{const q=f==="radio"?te.slice(0,1):te;return new Set(q)},[te,f]),Ee=a.useMemo(()=>f==="radio"?new Set:new Set(ye),[ye,f]);a.useEffect(()=>{t||I(Hr)},[!!t]);const Z=a.useCallback((q,X)=>{let z,A;L(q),r?(z=q,A=q.map(ee=>K.current.get(ee))):(z=[],A=[],q.forEach(ee=>{const oe=$(ee);oe!==void 0&&(z.push(ee),A.push(oe))})),I(z),d==null||d(z,A,{type:X})},[I,$,d,r]),J=a.useCallback((q,X,z,A)=>{if(l){const ee=z.map(oe=>$(oe));l($(q),X,ee,A)}Z(z,"single")},[l,$,Z]),$e=a.useMemo(()=>!g||h?null:(g===!0?[Tn,Dn,Mn]:g).map(X=>X===Tn?{key:"all",text:T.selectionAll,onSelect(){Z(S.map((z,A)=>k(z,A)).filter(z=>{const A=Q.get(z);return!(A!=null&&A.disabled)||Se.has(z)}),"all")}}:X===Dn?{key:"invert",text:T.selectInvert,onSelect(){const z=new Set(Se);E.forEach((ee,oe)=>{const re=k(ee,oe),Ne=Q.get(re);Ne!=null&&Ne.disabled||(z.has(re)?z.delete(re):z.add(re))});const A=Array.from(z);s&&(P.deprecated(!1,"onSelectInvert","onChange"),s(A)),Z(A,"invert")}}:X===Mn?{key:"none",text:T.selectNone,onSelect(){v==null||v(),Z(Array.from(Se).filter(z=>{const A=Q.get(z);return A==null?void 0:A.disabled}),"none")}}:X).map(X=>Object.assign(Object.assign({},X),{onSelect:function(){for(var z,A,ee=arguments.length,oe=new Array(ee),re=0;re<ee;re++)oe[re]=arguments[re];(A=X.onSelect)===null||A===void 0||(z=A).call.apply(z,[X].concat(oe)),N(null)}})),[g,Se,E,k,s,Z]);return[a.useCallback(q=>{var X;if(!t)return q.filter(ue=>ue!==mt);let z=ce(q);const A=new Set(Se),ee=M.map(k).filter(ue=>!Q.get(ue).disabled),oe=ee.every(ue=>A.has(ue)),re=ee.some(ue=>A.has(ue)),Ne=()=>{const ue=[];oe?ee.forEach(H=>{A.delete(H),ue.push(H)}):ee.forEach(H=>{A.has(H)||(A.add(H),ue.push(H))});const Y=Array.from(A);c==null||c(!oe,Y.map(H=>$(H)),ue.map(H=>$(H))),Z(Y,"all"),N(null)};let je,Oe;if(f!=="radio"){let ue;if($e){const ae={getPopupContainer:D,items:$e.map((Re,Ce)=>{const{key:De,text:Ue,onSelect:Ve}=Re;return{key:De??Ce,onClick:()=>{Ve==null||Ve(ee)},label:Ue}})};ue=a.createElement("div",{className:`${C}-selection-extra`},a.createElement(mo,{menu:ae,getPopupContainer:D},a.createElement("span",null,a.createElement(il,null))))}const Y=M.map((ae,Re)=>{const Ce=k(ae,Re),De=Q.get(Ce)||{};return Object.assign({checked:A.has(Ce)},De)}).filter(ae=>{let{disabled:Re}=ae;return Re}),H=!!Y.length&&Y.length===M.length,ke=H&&Y.every(ae=>{let{checked:Re}=ae;return Re}),Ie=H&&Y.some(ae=>{let{checked:Re}=ae;return Re});Oe=a.createElement(Dt,{checked:H?ke:!!M.length&&oe,indeterminate:H?!ke&&Ie:!oe&&re,onChange:Ne,disabled:M.length===0||H,"aria-label":ue?"Custom selection":"Select all",skipGroup:!0}),je=!h&&a.createElement("div",{className:`${C}-selection`},Oe,ue)}let _;f==="radio"?_=(ue,Y,H)=>{const ke=k(Y,H),Ie=A.has(ke),ae=Q.get(ke);return{node:a.createElement(Ut,Object.assign({},ae,{checked:Ie,onClick:Re=>{var Ce;Re.stopPropagation(),(Ce=ae==null?void 0:ae.onClick)===null||Ce===void 0||Ce.call(ae,Re)},onChange:Re=>{var Ce;A.has(ke)||J(ke,!0,[ke],Re.nativeEvent),(Ce=ae==null?void 0:ae.onChange)===null||Ce===void 0||Ce.call(ae,Re)}})),checked:Ie}}:_=(ue,Y,H)=>{var ke;const Ie=k(Y,H),ae=A.has(Ie),Re=Ee.has(Ie),Ce=Q.get(Ie);let De;return R==="nest"?De=Re:De=(ke=Ce==null?void 0:Ce.indeterminate)!==null&&ke!==void 0?ke:Re,{node:a.createElement(Dt,Object.assign({},Ce,{indeterminate:De,checked:ae,skipGroup:!0,onClick:Ue=>{var Ve;Ue.stopPropagation(),(Ve=Ce==null?void 0:Ce.onClick)===null||Ve===void 0||Ve.call(Ce,Ue)},onChange:Ue=>{var Ve;const{nativeEvent:vt}=Ue,{shiftKey:Pe}=vt,kt=ee.findIndex(qe=>qe===Ie),Nt=te.some(qe=>ee.includes(qe));if(Pe&&x&&Nt){const qe=O(kt,ee,A),at=Array.from(A);u==null||u(!ae,at.map(Ze=>$(Ze)),qe.map(Ze=>$(Ze))),Z(at,"multiple")}else{const qe=te;if(x){const at=ae?it(qe,Ie):dt(qe,Ie);J(Ie,!ae,at,vt)}else{const at=It([].concat(ce(qe),[Ie]),!0,V,U),{checkedKeys:Ze,halfCheckedKeys:Xe}=at;let Fe=Ze;if(ae){const Ye=new Set(Ze);Ye.delete(Ie),Fe=It(Array.from(Ye),{halfCheckedKeys:Xe},V,U).checkedKeys}J(Ie,!ae,Fe,vt)}}N(ae?null:kt),(Ve=Ce==null?void 0:Ce.onChange)===null||Ve===void 0||Ve.call(Ce,Ue)}})),checked:ae}};const F=(ue,Y,H)=>{const{node:ke,checked:Ie}=_(ue,Y,H);return m?m(Ie,Y,H,ke):ke};if(!z.includes(mt))if(z.findIndex(ue=>{var Y;return((Y=ue[At])===null||Y===void 0?void 0:Y.columnType)==="EXPAND_COLUMN"})===0){const[ue,...Y]=z;z=[ue,mt].concat(ce(Y))}else z=[mt].concat(ce(z));const ne=z.indexOf(mt);z=z.filter((ue,Y)=>ue!==mt||Y===ne);const he=z[ne-1],de=z[ne+1];let we=y;we===void 0&&((de==null?void 0:de.fixed)!==void 0?we=de.fixed:(he==null?void 0:he.fixed)!==void 0&&(we=he.fixed)),we&&he&&((X=he[At])===null||X===void 0?void 0:X.columnType)==="EXPAND_COLUMN"&&he.fixed===void 0&&(he.fixed=we);const Te=G(`${C}-selection-col`,{[`${C}-selection-col-with-dropdown`]:g&&f==="checkbox"}),Le=()=>t!=null&&t.columnTitle?typeof t.columnTitle=="function"?t.columnTitle(Oe):t.columnTitle:je,be={fixed:we,width:p,className:`${C}-selection-column`,title:Le(),render:F,onCell:t.onCell,[At]:{className:Te}};return z.map(ue=>ue===mt?be:ue)},[k,M,t,te,Se,Ee,p,$e,R,Q,u,J,U]),Se]};function Gs(e,t){return e._antProxy=e._antProxy||{},Object.keys(t).forEach(r=>{if(!(r in e._antProxy)){const n=e[r];e._antProxy[r]=n,e[r]=t[r]}}),e}function Us(e,t){return a.useImperativeHandle(e,()=>{const r=t(),{nativeElement:n}=r;return typeof Proxy<"u"?new Proxy(n,{get(o,i){return r[i]?r[i]:Reflect.get(o,i)}}):Gs(n,r)})}function Ys(e){return t=>{const{prefixCls:r,onExpand:n,record:o,expanded:i,expandable:d}=t,l=`${r}-row-expand-icon`;return a.createElement("button",{type:"button",onClick:c=>{n(o,c),c.stopPropagation()},className:G(l,{[`${l}-spaced`]:!d,[`${l}-expanded`]:d&&i,[`${l}-collapsed`]:d&&!i}),"aria-label":i?e.collapse:e.expand,"aria-expanded":i})}}function Qs(e){return(r,n)=>{const o=r.querySelector(`.${e}-container`);let i=n;if(o){const d=getComputedStyle(o),l=parseInt(d.borderLeftWidth,10),c=parseInt(d.borderRightWidth,10);i=n-l-c}return i}}const yt=(e,t)=>"key"in e&&e.key!==void 0&&e.key!==null?e.key:e.dataIndex?Array.isArray(e.dataIndex)?e.dataIndex.join("."):e.dataIndex:t;function Ht(e,t){return t?`${t}-${e}`:`${e}`}const vn=(e,t)=>typeof e=="function"?e(t):e,Zs=(e,t)=>{const r=vn(e,t);return Object.prototype.toString.call(r)==="[object Object]"?"":r};function Js(e){const t=a.useRef(e),r=Wa();return[()=>t.current,n=>{t.current=n,r()}]}var ec=function(t){var r=t.dropPosition,n=t.dropLevelOffset,o=t.indent,i={pointerEvents:"none",position:"absolute",right:0,backgroundColor:"red",height:2};switch(r){case-1:i.top=0,i.left=-n*o;break;case 1:i.bottom=0,i.left=-n*o;break;case 0:i.bottom=0,i.left=o;break}return me.createElement("div",{style:i})};function Jo(e){if(e==null)throw new TypeError("Cannot destructure "+e)}function tc(e,t){var r=a.useState(!1),n=xe(r,2),o=n[0],i=n[1];Kt(function(){if(o)return e(),function(){t()}},[o]),Kt(function(){return i(!0),function(){i(!1)}},[])}var nc=["className","style","motion","motionNodes","motionType","onMotionStart","onMotionEnd","active","treeNodeRequiredProps"],rc=a.forwardRef(function(e,t){var r=e.className,n=e.style,o=e.motion,i=e.motionNodes,d=e.motionType,l=e.onMotionStart,c=e.onMotionEnd,s=e.active,v=e.treeNodeRequiredProps,u=nt(e,nc),p=a.useState(!0),f=xe(p,2),g=f[0],y=f[1],m=a.useContext(Yn),h=m.prefixCls,x=i&&d!=="hide";Kt(function(){i&&x!==g&&y(x)},[i]);var C=function(){i&&l()},S=a.useRef(!1),E=function(){i&&!S.current&&(S.current=!0,c())};tc(C,E);var $=function(R){x===R&&E()};return i?a.createElement(Va,ve({ref:t,visible:g},o,{motionAppear:d==="show",onVisibleChanged:$}),function(k,R){var b=k.className,T=k.style;return a.createElement("div",{ref:R,className:G("".concat(h,"-treenode-motion"),b),style:T},i.map(function(D){var P=Object.assign({},(Jo(D.data),D.data)),O=D.title,N=D.key,w=D.isStart,I=D.isEnd;delete P.children;var K=Ft(N,v);return a.createElement(qt,ve({},P,K,{title:O,active:s,data:D.data,key:N,isStart:w,isEnd:I}))}))}):a.createElement(qt,ve({domRef:t,className:r,style:n},u,{active:s}))});function oc(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[],r=e.length,n=t.length;if(Math.abs(r-n)!==1)return{add:!1,key:null};function o(i,d){var l=new Map;i.forEach(function(s){l.set(s,!0)});var c=d.filter(function(s){return!l.has(s)});return c.length===1?c[0]:null}return r<n?{add:!0,key:o(e,t)}:{add:!1,key:o(t,e)}}function zr(e,t,r){var n=e.findIndex(function(l){return l.key===r}),o=e[n+1],i=t.findIndex(function(l){return l.key===r});if(o){var d=t.findIndex(function(l){return l.key===o.key});return t.slice(i+1,d)}return t.slice(i+1)}var ac=["prefixCls","data","selectable","checkable","expandedKeys","selectedKeys","checkedKeys","loadedKeys","loadingKeys","halfCheckedKeys","keyEntities","disabled","dragging","dragOverNodeKey","dropPosition","motion","height","itemHeight","virtual","scrollWidth","focusable","activeItem","focused","tabIndex","onKeyDown","onFocus","onBlur","onActiveChange","onListChangeStart","onListChangeEnd"],jr={width:0,height:0,display:"flex",overflow:"hidden",opacity:0,border:0,padding:0,margin:0},lc=function(){},$t="RC_TREE_MOTION_".concat(Math.random()),Bn={key:$t},ea={key:$t,level:0,index:0,pos:"0",node:Bn,nodes:[Bn]},Fr={parent:null,children:[],pos:ea.pos,data:Bn,title:null,key:$t,isStart:[],isEnd:[]};function Ar(e,t,r,n){return t===!1||!r?e:e.slice(0,Math.ceil(r/n)+1)}function Wr(e){var t=e.key,r=e.pos;return Yt(t,r)}function ic(e){for(var t=String(e.data.key),r=e;r.parent;)r=r.parent,t="".concat(r.data.key," > ").concat(t);return t}var sc=a.forwardRef(function(e,t){var r=e.prefixCls,n=e.data;e.selectable,e.checkable;var o=e.expandedKeys,i=e.selectedKeys,d=e.checkedKeys,l=e.loadedKeys,c=e.loadingKeys,s=e.halfCheckedKeys,v=e.keyEntities,u=e.disabled,p=e.dragging,f=e.dragOverNodeKey,g=e.dropPosition,y=e.motion,m=e.height,h=e.itemHeight,x=e.virtual,C=e.scrollWidth,S=e.focusable,E=e.activeItem,$=e.focused,k=e.tabIndex,R=e.onKeyDown,b=e.onFocus,T=e.onBlur,D=e.onActiveChange,P=e.onListChangeStart,O=e.onListChangeEnd,N=nt(e,ac),w=a.useRef(null),I=a.useRef(null);a.useImperativeHandle(t,function(){return{scrollTo:function(F){w.current.scrollTo(F)},getIndentWidth:function(){return I.current.offsetWidth}}});var K=a.useState(o),L=xe(K,2),M=L[0],V=L[1],Q=a.useState(n),U=xe(Q,2),te=U[0],ye=U[1],Se=a.useState(n),Ee=xe(Se,2),Z=Ee[0],J=Ee[1],$e=a.useState([]),ge=xe($e,2),q=ge[0],X=ge[1],z=a.useState(null),A=xe(z,2),ee=A[0],oe=A[1],re=a.useRef(n);re.current=n;function Ne(){var _=re.current;ye(_),J(_),X([]),oe(null),O()}Kt(function(){V(o);var _=oc(M,o);if(_.key!==null)if(_.add){var F=te.findIndex(function(Le){var be=Le.key;return be===_.key}),ne=Ar(zr(te,n,_.key),x,m,h),he=te.slice();he.splice(F+1,0,Fr),J(he),X(ne),oe("show")}else{var de=n.findIndex(function(Le){var be=Le.key;return be===_.key}),we=Ar(zr(n,te,_.key),x,m,h),Te=n.slice();Te.splice(de+1,0,Fr),J(Te),X(we),oe("hide")}else te!==n&&(ye(n),J(n))},[o,n]),a.useEffect(function(){p||Ne()},[p]);var je=y?Z:n,Oe={expandedKeys:o,selectedKeys:i,loadedKeys:l,loadingKeys:c,checkedKeys:d,halfCheckedKeys:s,dragOverNodeKey:f,dropPosition:g,keyEntities:v};return a.createElement(a.Fragment,null,$&&E&&a.createElement("span",{style:jr,"aria-live":"assertive"},ic(E)),a.createElement("div",null,a.createElement("input",{style:jr,disabled:S===!1||u,tabIndex:S!==!1?k:null,onKeyDown:R,onFocus:b,onBlur:T,value:"",onChange:lc,"aria-label":"for screen reader"})),a.createElement("div",{className:"".concat(r,"-treenode"),"aria-hidden":!0,style:{position:"absolute",pointerEvents:"none",visibility:"hidden",height:0,overflow:"hidden",border:0,padding:0}},a.createElement("div",{className:"".concat(r,"-indent")},a.createElement("div",{ref:I,className:"".concat(r,"-indent-unit")}))),a.createElement(ho,ve({},N,{data:je,itemKey:Wr,height:m,fullHeight:!1,virtual:x,itemHeight:h,scrollWidth:C,prefixCls:"".concat(r,"-list"),ref:w,role:"tree",onVisibleChange:function(F){F.every(function(ne){return Wr(ne)!==$t})&&Ne()}}),function(_){var F=_.pos,ne=Object.assign({},(Jo(_.data),_.data)),he=_.title,de=_.key,we=_.isStart,Te=_.isEnd,Le=Yt(de,F);delete ne.key,delete ne.children;var be=Ft(Le,Oe);return a.createElement(rc,ve({},ne,be,{title:he,active:!!E&&de===E.key,pos:F,data:_.data,isStart:we,isEnd:Te,motion:y,motionNodes:de===$t?q:null,motionType:ee,onMotionStart:P,onMotionEnd:Ne,treeNodeRequiredProps:Oe,onMouseMove:function(){D(null)}}))}))}),cc=10,Zn=function(e){qa(r,e);var t=Xa(r);function r(){var n;Ga(this,r);for(var o=arguments.length,i=new Array(o),d=0;d<o;d++)i[d]=arguments[d];return n=t.call.apply(t,[this].concat(i)),B(fe(n),"destroyed",!1),B(fe(n),"delayedDragEnterLogic",void 0),B(fe(n),"loadingRetryTimes",{}),B(fe(n),"state",{keyEntities:{},indent:null,selectedKeys:[],checkedKeys:[],halfCheckedKeys:[],loadedKeys:[],loadingKeys:[],expandedKeys:[],draggingNodeKey:null,dragChildrenKeys:[],dropTargetKey:null,dropPosition:null,dropContainerKey:null,dropLevelOffset:null,dropTargetPos:null,dropAllowed:!0,dragOverNodeKey:null,treeData:[],flattenNodes:[],focused:!1,activeKey:null,listChanging:!1,prevProps:null,fieldNames:Tt()}),B(fe(n),"dragStartMousePosition",null),B(fe(n),"dragNodeProps",null),B(fe(n),"currentMouseOverDroppableNodeKey",null),B(fe(n),"listRef",a.createRef()),B(fe(n),"onNodeDragStart",function(l,c){var s=n.state,v=s.expandedKeys,u=s.keyEntities,p=n.props.onDragStart,f=c.eventKey;n.dragNodeProps=c,n.dragStartMousePosition={x:l.clientX,y:l.clientY};var g=it(v,f);n.setState({draggingNodeKey:f,dragChildrenKeys:As(f,u),indent:n.listRef.current.getIndentWidth()}),n.setExpandedKeys(g),window.addEventListener("dragend",n.onWindowDragEnd),p==null||p({event:l,node:Me(c)})}),B(fe(n),"onNodeDragEnter",function(l,c){var s=n.state,v=s.expandedKeys,u=s.keyEntities,p=s.dragChildrenKeys,f=s.flattenNodes,g=s.indent,y=n.props,m=y.onDragEnter,h=y.onExpand,x=y.allowDrop,C=y.direction,S=c.pos,E=c.eventKey;if(n.currentMouseOverDroppableNodeKey!==E&&(n.currentMouseOverDroppableNodeKey=E),!n.dragNodeProps){n.resetDragState();return}var $=Lr(l,n.dragNodeProps,c,g,n.dragStartMousePosition,x,f,u,v,C),k=$.dropPosition,R=$.dropLevelOffset,b=$.dropTargetKey,T=$.dropContainerKey,D=$.dropTargetPos,P=$.dropAllowed,O=$.dragOverNodeKey;if(p.includes(b)||!P){n.resetDragState();return}if(n.delayedDragEnterLogic||(n.delayedDragEnterLogic={}),Object.keys(n.delayedDragEnterLogic).forEach(function(N){clearTimeout(n.delayedDragEnterLogic[N])}),n.dragNodeProps.eventKey!==c.eventKey&&(l.persist(),n.delayedDragEnterLogic[S]=window.setTimeout(function(){if(n.state.draggingNodeKey!==null){var N=ce(v),w=Qe(u,c.eventKey);w&&(w.children||[]).length&&(N=dt(v,c.eventKey)),n.props.hasOwnProperty("expandedKeys")||n.setExpandedKeys(N),h==null||h(N,{node:Me(c),expanded:!0,nativeEvent:l.nativeEvent})}},800)),n.dragNodeProps.eventKey===b&&R===0){n.resetDragState();return}n.setState({dragOverNodeKey:O,dropPosition:k,dropLevelOffset:R,dropTargetKey:b,dropContainerKey:T,dropTargetPos:D,dropAllowed:P}),m==null||m({event:l,node:Me(c),expandedKeys:v})}),B(fe(n),"onNodeDragOver",function(l,c){var s=n.state,v=s.dragChildrenKeys,u=s.flattenNodes,p=s.keyEntities,f=s.expandedKeys,g=s.indent,y=n.props,m=y.onDragOver,h=y.allowDrop,x=y.direction;if(n.dragNodeProps){var C=Lr(l,n.dragNodeProps,c,g,n.dragStartMousePosition,h,u,p,f,x),S=C.dropPosition,E=C.dropLevelOffset,$=C.dropTargetKey,k=C.dropContainerKey,R=C.dropTargetPos,b=C.dropAllowed,T=C.dragOverNodeKey;v.includes($)||!b||(n.dragNodeProps.eventKey===$&&E===0?n.state.dropPosition===null&&n.state.dropLevelOffset===null&&n.state.dropTargetKey===null&&n.state.dropContainerKey===null&&n.state.dropTargetPos===null&&n.state.dropAllowed===!1&&n.state.dragOverNodeKey===null||n.resetDragState():S===n.state.dropPosition&&E===n.state.dropLevelOffset&&$===n.state.dropTargetKey&&k===n.state.dropContainerKey&&R===n.state.dropTargetPos&&b===n.state.dropAllowed&&T===n.state.dragOverNodeKey||n.setState({dropPosition:S,dropLevelOffset:E,dropTargetKey:$,dropContainerKey:k,dropTargetPos:R,dropAllowed:b,dragOverNodeKey:T}),m==null||m({event:l,node:Me(c)}))}}),B(fe(n),"onNodeDragLeave",function(l,c){n.currentMouseOverDroppableNodeKey===c.eventKey&&!l.currentTarget.contains(l.relatedTarget)&&(n.resetDragState(),n.currentMouseOverDroppableNodeKey=null);var s=n.props.onDragLeave;s==null||s({event:l,node:Me(c)})}),B(fe(n),"onWindowDragEnd",function(l){n.onNodeDragEnd(l,null,!0),window.removeEventListener("dragend",n.onWindowDragEnd)}),B(fe(n),"onNodeDragEnd",function(l,c){var s=n.props.onDragEnd;n.setState({dragOverNodeKey:null}),n.cleanDragState(),s==null||s({event:l,node:Me(c)}),n.dragNodeProps=null,window.removeEventListener("dragend",n.onWindowDragEnd)}),B(fe(n),"onNodeDrop",function(l,c){var s,v=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,u=n.state,p=u.dragChildrenKeys,f=u.dropPosition,g=u.dropTargetKey,y=u.dropTargetPos,m=u.dropAllowed;if(m){var h=n.props.onDrop;if(n.setState({dragOverNodeKey:null}),n.cleanDragState(),g!==null){var x=j(j({},Ft(g,n.getTreeNodeRequiredProps())),{},{active:((s=n.getActiveItem())===null||s===void 0?void 0:s.key)===g,data:Qe(n.state.keyEntities,g).node}),C=p.includes(g);ht(!C,"Can not drop to dragNode's children node. This is a bug of rc-tree. Please report an issue.");var S=Qn(y),E={event:l,node:Me(x),dragNode:n.dragNodeProps?Me(n.dragNodeProps):null,dragNodesKeys:[n.dragNodeProps.eventKey].concat(p),dropToGap:f!==0,dropPosition:f+Number(S[S.length-1])};v||h==null||h(E),n.dragNodeProps=null}}}),B(fe(n),"cleanDragState",function(){var l=n.state.draggingNodeKey;l!==null&&n.setState({draggingNodeKey:null,dropPosition:null,dropContainerKey:null,dropTargetKey:null,dropLevelOffset:null,dropAllowed:!0,dragOverNodeKey:null}),n.dragStartMousePosition=null,n.currentMouseOverDroppableNodeKey=null}),B(fe(n),"triggerExpandActionExpand",function(l,c){var s=n.state,v=s.expandedKeys,u=s.flattenNodes,p=c.expanded,f=c.key,g=c.isLeaf;if(!(g||l.shiftKey||l.metaKey||l.ctrlKey)){var y=u.filter(function(h){return h.key===f})[0],m=Me(j(j({},Ft(f,n.getTreeNodeRequiredProps())),{},{data:y.data}));n.setExpandedKeys(p?it(v,f):dt(v,f)),n.onNodeExpand(l,m)}}),B(fe(n),"onNodeClick",function(l,c){var s=n.props,v=s.onClick,u=s.expandAction;u==="click"&&n.triggerExpandActionExpand(l,c),v==null||v(l,c)}),B(fe(n),"onNodeDoubleClick",function(l,c){var s=n.props,v=s.onDoubleClick,u=s.expandAction;u==="doubleClick"&&n.triggerExpandActionExpand(l,c),v==null||v(l,c)}),B(fe(n),"onNodeSelect",function(l,c){var s=n.state.selectedKeys,v=n.state,u=v.keyEntities,p=v.fieldNames,f=n.props,g=f.onSelect,y=f.multiple,m=c.selected,h=c[p.key],x=!m;x?y?s=dt(s,h):s=[h]:s=it(s,h);var C=s.map(function(S){var E=Qe(u,S);return E?E.node:null}).filter(Boolean);n.setUncontrolledState({selectedKeys:s}),g==null||g(s,{event:"select",selected:x,node:c,selectedNodes:C,nativeEvent:l.nativeEvent})}),B(fe(n),"onNodeCheck",function(l,c,s){var v=n.state,u=v.keyEntities,p=v.checkedKeys,f=v.halfCheckedKeys,g=n.props,y=g.checkStrictly,m=g.onCheck,h=c.key,x,C={event:"check",node:c,checked:s,nativeEvent:l.nativeEvent};if(y){var S=s?dt(p,h):it(p,h),E=it(f,h);x={checked:S,halfChecked:E},C.checkedNodes=S.map(function(D){return Qe(u,D)}).filter(Boolean).map(function(D){return D.node}),n.setUncontrolledState({checkedKeys:S})}else{var $=It([].concat(ce(p),[h]),!0,u),k=$.checkedKeys,R=$.halfCheckedKeys;if(!s){var b=new Set(k);b.delete(h);var T=It(Array.from(b),{halfCheckedKeys:R},u);k=T.checkedKeys,R=T.halfCheckedKeys}x=k,C.checkedNodes=[],C.checkedNodesPositions=[],C.halfCheckedKeys=R,k.forEach(function(D){var P=Qe(u,D);if(P){var O=P.node,N=P.pos;C.checkedNodes.push(O),C.checkedNodesPositions.push({node:O,pos:N})}}),n.setUncontrolledState({checkedKeys:k},!1,{halfCheckedKeys:R})}m==null||m(x,C)}),B(fe(n),"onNodeLoad",function(l){var c,s=l.key,v=n.state.keyEntities,u=Qe(v,s);if(!(u!=null&&(c=u.children)!==null&&c!==void 0&&c.length)){var p=new Promise(function(f,g){n.setState(function(y){var m=y.loadedKeys,h=m===void 0?[]:m,x=y.loadingKeys,C=x===void 0?[]:x,S=n.props,E=S.loadData,$=S.onLoad;if(!E||h.includes(s)||C.includes(s))return null;var k=E(l);return k.then(function(){var R=n.state.loadedKeys,b=dt(R,s);$==null||$(b,{event:"load",node:l}),n.setUncontrolledState({loadedKeys:b}),n.setState(function(T){return{loadingKeys:it(T.loadingKeys,s)}}),f()}).catch(function(R){if(n.setState(function(T){return{loadingKeys:it(T.loadingKeys,s)}}),n.loadingRetryTimes[s]=(n.loadingRetryTimes[s]||0)+1,n.loadingRetryTimes[s]>=cc){var b=n.state.loadedKeys;ht(!1,"Retry for `loadData` many times but still failed. No more retry."),n.setUncontrolledState({loadedKeys:dt(b,s)}),f()}g(R)}),{loadingKeys:dt(C,s)}})});return p.catch(function(){}),p}}),B(fe(n),"onNodeMouseEnter",function(l,c){var s=n.props.onMouseEnter;s==null||s({event:l,node:c})}),B(fe(n),"onNodeMouseLeave",function(l,c){var s=n.props.onMouseLeave;s==null||s({event:l,node:c})}),B(fe(n),"onNodeContextMenu",function(l,c){var s=n.props.onRightClick;s&&(l.preventDefault(),s({event:l,node:c}))}),B(fe(n),"onFocus",function(){var l=n.props.onFocus;n.setState({focused:!0});for(var c=arguments.length,s=new Array(c),v=0;v<c;v++)s[v]=arguments[v];l==null||l.apply(void 0,s)}),B(fe(n),"onBlur",function(){var l=n.props.onBlur;n.setState({focused:!1}),n.onActiveChange(null);for(var c=arguments.length,s=new Array(c),v=0;v<c;v++)s[v]=arguments[v];l==null||l.apply(void 0,s)}),B(fe(n),"getTreeNodeRequiredProps",function(){var l=n.state,c=l.expandedKeys,s=l.selectedKeys,v=l.loadedKeys,u=l.loadingKeys,p=l.checkedKeys,f=l.halfCheckedKeys,g=l.dragOverNodeKey,y=l.dropPosition,m=l.keyEntities;return{expandedKeys:c||[],selectedKeys:s||[],loadedKeys:v||[],loadingKeys:u||[],checkedKeys:p||[],halfCheckedKeys:f||[],dragOverNodeKey:g,dropPosition:y,keyEntities:m}}),B(fe(n),"setExpandedKeys",function(l){var c=n.state,s=c.treeData,v=c.fieldNames,u=Sn(s,l,v);n.setUncontrolledState({expandedKeys:l,flattenNodes:u},!0)}),B(fe(n),"onNodeExpand",function(l,c){var s=n.state.expandedKeys,v=n.state,u=v.listChanging,p=v.fieldNames,f=n.props,g=f.onExpand,y=f.loadData,m=c.expanded,h=c[p.key];if(!u){var x=s.includes(h),C=!m;if(ht(m&&x||!m&&!x,"Expand state not sync with index check"),s=C?dt(s,h):it(s,h),n.setExpandedKeys(s),g==null||g(s,{node:c,expanded:C,nativeEvent:l.nativeEvent}),C&&y){var S=n.onNodeLoad(c);S&&S.then(function(){var E=Sn(n.state.treeData,s,p);n.setUncontrolledState({flattenNodes:E})}).catch(function(){var E=n.state.expandedKeys,$=it(E,h);n.setExpandedKeys($)})}}}),B(fe(n),"onListChangeStart",function(){n.setUncontrolledState({listChanging:!0})}),B(fe(n),"onListChangeEnd",function(){setTimeout(function(){n.setUncontrolledState({listChanging:!1})})}),B(fe(n),"onActiveChange",function(l){var c=n.state.activeKey,s=n.props,v=s.onActiveChange,u=s.itemScrollOffset,p=u===void 0?0:u;c!==l&&(n.setState({activeKey:l}),l!==null&&n.scrollTo({key:l,offset:p}),v==null||v(l))}),B(fe(n),"getActiveItem",function(){var l=n.state,c=l.activeKey,s=l.flattenNodes;return c===null?null:s.find(function(v){var u=v.key;return u===c})||null}),B(fe(n),"offsetActiveKey",function(l){var c=n.state,s=c.flattenNodes,v=c.activeKey,u=s.findIndex(function(g){var y=g.key;return y===v});u===-1&&l<0&&(u=s.length),u=(u+l+s.length)%s.length;var p=s[u];if(p){var f=p.key;n.onActiveChange(f)}else n.onActiveChange(null)}),B(fe(n),"onKeyDown",function(l){var c=n.state,s=c.activeKey,v=c.expandedKeys,u=c.checkedKeys,p=c.fieldNames,f=n.props,g=f.onKeyDown,y=f.checkable,m=f.selectable;switch(l.which){case gt.UP:{n.offsetActiveKey(-1),l.preventDefault();break}case gt.DOWN:{n.offsetActiveKey(1),l.preventDefault();break}}var h=n.getActiveItem();if(h&&h.data){var x=n.getTreeNodeRequiredProps(),C=h.data.isLeaf===!1||!!(h.data[p.children]||[]).length,S=Me(j(j({},Ft(s,x)),{},{data:h.data,active:!0}));switch(l.which){case gt.LEFT:{C&&v.includes(s)?n.onNodeExpand({},S):h.parent&&n.onActiveChange(h.parent.key),l.preventDefault();break}case gt.RIGHT:{C&&!v.includes(s)?n.onNodeExpand({},S):h.children&&h.children.length&&n.onActiveChange(h.children[0].key),l.preventDefault();break}case gt.ENTER:case gt.SPACE:{y&&!S.disabled&&S.checkable!==!1&&!S.disableCheckbox?n.onNodeCheck({},S,!u.includes(s)):!y&&m&&!S.disabled&&S.selectable!==!1&&n.onNodeSelect({},S);break}}}g==null||g(l)}),B(fe(n),"setUncontrolledState",function(l){var c=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:null;if(!n.destroyed){var v=!1,u=!0,p={};Object.keys(l).forEach(function(f){if(n.props.hasOwnProperty(f)){u=!1;return}v=!0,p[f]=l[f]}),v&&(!c||u)&&n.setState(j(j({},p),s))}}),B(fe(n),"scrollTo",function(l){n.listRef.current.scrollTo(l)}),n}return Ua(r,[{key:"componentDidMount",value:function(){this.destroyed=!1,this.onUpdated()}},{key:"componentDidUpdate",value:function(){this.onUpdated()}},{key:"onUpdated",value:function(){var o=this.props,i=o.activeKey,d=o.itemScrollOffset,l=d===void 0?0:d;i!==void 0&&i!==this.state.activeKey&&(this.setState({activeKey:i}),i!==null&&this.scrollTo({key:i,offset:l}))}},{key:"componentWillUnmount",value:function(){window.removeEventListener("dragend",this.onWindowDragEnd),this.destroyed=!0}},{key:"resetDragState",value:function(){this.setState({dragOverNodeKey:null,dropPosition:null,dropLevelOffset:null,dropTargetKey:null,dropContainerKey:null,dropTargetPos:null,dropAllowed:!1})}},{key:"render",value:function(){var o=this.state,i=o.focused,d=o.flattenNodes,l=o.keyEntities,c=o.draggingNodeKey,s=o.activeKey,v=o.dropLevelOffset,u=o.dropContainerKey,p=o.dropTargetKey,f=o.dropPosition,g=o.dragOverNodeKey,y=o.indent,m=this.props,h=m.prefixCls,x=m.className,C=m.style,S=m.showLine,E=m.focusable,$=m.tabIndex,k=$===void 0?0:$,R=m.selectable,b=m.showIcon,T=m.icon,D=m.switcherIcon,P=m.draggable,O=m.checkable,N=m.checkStrictly,w=m.disabled,I=m.motion,K=m.loadData,L=m.filterTreeNode,M=m.height,V=m.itemHeight,Q=m.scrollWidth,U=m.virtual,te=m.titleRender,ye=m.dropIndicatorRender,Se=m.onContextMenu,Ee=m.onScroll,Z=m.direction,J=m.rootClassName,$e=m.rootStyle,ge=Wt(this.props,{aria:!0,data:!0}),q;P&&(st(P)==="object"?q=P:typeof P=="function"?q={nodeDraggable:P}:q={});var X={prefixCls:h,selectable:R,showIcon:b,icon:T,switcherIcon:D,draggable:q,draggingNodeKey:c,checkable:O,checkStrictly:N,disabled:w,keyEntities:l,dropLevelOffset:v,dropContainerKey:u,dropTargetKey:p,dropPosition:f,dragOverNodeKey:g,indent:y,direction:Z,dropIndicatorRender:ye,loadData:K,filterTreeNode:L,titleRender:te,onNodeClick:this.onNodeClick,onNodeDoubleClick:this.onNodeDoubleClick,onNodeExpand:this.onNodeExpand,onNodeSelect:this.onNodeSelect,onNodeCheck:this.onNodeCheck,onNodeLoad:this.onNodeLoad,onNodeMouseEnter:this.onNodeMouseEnter,onNodeMouseLeave:this.onNodeMouseLeave,onNodeContextMenu:this.onNodeContextMenu,onNodeDragStart:this.onNodeDragStart,onNodeDragEnter:this.onNodeDragEnter,onNodeDragOver:this.onNodeDragOver,onNodeDragLeave:this.onNodeDragLeave,onNodeDragEnd:this.onNodeDragEnd,onNodeDrop:this.onNodeDrop};return a.createElement(Yn.Provider,{value:X},a.createElement("div",{className:G(h,x,J,B(B(B({},"".concat(h,"-show-line"),S),"".concat(h,"-focused"),i),"".concat(h,"-active-focused"),s!==null)),style:$e},a.createElement(sc,ve({ref:this.listRef,prefixCls:h,style:C,data:d,disabled:w,selectable:R,checkable:!!O,motion:I,dragging:c!==null,height:M,itemHeight:V,virtual:U,focusable:E,focused:i,tabIndex:k,activeItem:this.getActiveItem(),onFocus:this.onFocus,onBlur:this.onBlur,onKeyDown:this.onKeyDown,onActiveChange:this.onActiveChange,onListChangeStart:this.onListChangeStart,onListChangeEnd:this.onListChangeEnd,onContextMenu:Se,onScroll:Ee,scrollWidth:Q},this.getTreeNodeRequiredProps(),ge))))}}],[{key:"getDerivedStateFromProps",value:function(o,i){var d=i.prevProps,l={prevProps:o};function c(k){return!d&&o.hasOwnProperty(k)||d&&d[k]!==o[k]}var s,v=i.fieldNames;if(c("fieldNames")&&(v=Tt(o.fieldNames),l.fieldNames=v),c("treeData")?s=o.treeData:c("children")&&(ht(!1,"`children` of Tree is deprecated. Please use `treeData` instead."),s=$o(o.children)),s){l.treeData=s;var u=Wn(s,{fieldNames:v});l.keyEntities=j(B({},$t,ea),u.keyEntities)}var p=l.keyEntities||i.keyEntities;if(c("expandedKeys")||d&&c("autoExpandParent"))l.expandedKeys=o.autoExpandParent||!d&&o.defaultExpandParent?Kn(o.expandedKeys,p):o.expandedKeys;else if(!d&&o.defaultExpandAll){var f=j({},p);delete f[$t];var g=[];Object.keys(f).forEach(function(k){var R=f[k];R.children&&R.children.length&&g.push(R.key)}),l.expandedKeys=g}else!d&&o.defaultExpandedKeys&&(l.expandedKeys=o.autoExpandParent||o.defaultExpandParent?Kn(o.defaultExpandedKeys,p):o.defaultExpandedKeys);if(l.expandedKeys||delete l.expandedKeys,s||l.expandedKeys){var y=Sn(s||i.treeData,l.expandedKeys||i.expandedKeys,v);l.flattenNodes=y}if(o.selectable&&(c("selectedKeys")?l.selectedKeys=_r(o.selectedKeys,o):!d&&o.defaultSelectedKeys&&(l.selectedKeys=_r(o.defaultSelectedKeys,o))),o.checkable){var m;if(c("checkedKeys")?m=wn(o.checkedKeys)||{}:!d&&o.defaultCheckedKeys?m=wn(o.defaultCheckedKeys)||{}:s&&(m=wn(o.checkedKeys)||{checkedKeys:i.checkedKeys,halfCheckedKeys:i.halfCheckedKeys}),m){var h=m,x=h.checkedKeys,C=x===void 0?[]:x,S=h.halfCheckedKeys,E=S===void 0?[]:S;if(!o.checkStrictly){var $=It(C,!0,p);C=$.checkedKeys,E=$.halfCheckedKeys}l.checkedKeys=C,l.halfCheckedKeys=E}}return c("loadedKeys")&&(l.loadedKeys=o.loadedKeys),l}}]),r}(a.Component);B(Zn,"defaultProps",{prefixCls:"rc-tree",showLine:!1,showIcon:!0,selectable:!0,multiple:!1,checkable:!1,disabled:!1,checkStrictly:!1,draggable:!1,defaultExpandParent:!0,autoExpandParent:!1,defaultExpandAll:!1,defaultExpandedKeys:[],defaultCheckedKeys:[],defaultSelectedKeys:[],dropIndicatorRender:ec,allowDrop:function(){return!0},expandAction:!1});B(Zn,"TreeNode",qt);const dc=e=>{let{treeCls:t,treeNodeCls:r,directoryNodeSelectedBg:n,directoryNodeSelectedColor:o,motionDurationMid:i,borderRadius:d,controlItemBgHover:l}=e;return{[`${t}${t}-directory ${r}`]:{[`${t}-node-content-wrapper`]:{position:"static",[`> *:not(${t}-drop-indicator)`]:{position:"relative"},"&:hover":{background:"transparent"},"&:before":{position:"absolute",inset:0,transition:`background-color ${i}`,content:'""',borderRadius:d},"&:hover:before":{background:l}},[`${t}-switcher, ${t}-checkbox, ${t}-draggable-icon`]:{zIndex:1},"&-selected":{[`${t}-switcher, ${t}-draggable-icon`]:{color:o},[`${t}-node-content-wrapper`]:{color:o,background:"transparent","&:before, &:hover:before":{background:n}}}}}},uc=new Qa("ant-tree-node-fx-do-not-use",{"0%":{opacity:0},"100%":{opacity:1}}),fc=(e,t)=>({[`.${e}-switcher-icon`]:{display:"inline-block",fontSize:10,verticalAlign:"baseline",svg:{transition:`transform ${t.motionDurationSlow}`}}}),vc=(e,t)=>({[`.${e}-drop-indicator`]:{position:"absolute",zIndex:1,height:2,backgroundColor:t.colorPrimary,borderRadius:1,pointerEvents:"none","&:after":{position:"absolute",top:-3,insetInlineStart:-6,width:8,height:8,backgroundColor:"transparent",border:`${W(t.lineWidthBold)} solid ${t.colorPrimary}`,borderRadius:"50%",content:'""'}}}),pc=(e,t)=>{const{treeCls:r,treeNodeCls:n,treeNodePadding:o,titleHeight:i,indentSize:d,nodeSelectedBg:l,nodeHoverBg:c,colorTextQuaternary:s,controlItemBgActiveDisabled:v}=t;return{[r]:Object.assign(Object.assign({},ft(t)),{background:t.colorBgContainer,borderRadius:t.borderRadius,transition:`background-color ${t.motionDurationSlow}`,"&-rtl":{direction:"rtl"},[`&${r}-rtl ${r}-switcher_close ${r}-switcher-icon svg`]:{transform:"rotate(90deg)"},[`&-focused:not(:hover):not(${r}-active-focused)`]:Object.assign({},cn(t)),[`${r}-list-holder-inner`]:{alignItems:"flex-start"},[`&${r}-block-node`]:{[`${r}-list-holder-inner`]:{alignItems:"stretch",[`${r}-node-content-wrapper`]:{flex:"auto"},[`${n}.dragging:after`]:{position:"absolute",inset:0,border:`1px solid ${t.colorPrimary}`,opacity:0,animationName:uc,animationDuration:t.motionDurationSlow,animationPlayState:"running",animationFillMode:"forwards",content:'""',pointerEvents:"none",borderRadius:t.borderRadius}}},[n]:{display:"flex",alignItems:"flex-start",marginBottom:o,lineHeight:W(i),position:"relative","&:before":{content:'""',position:"absolute",zIndex:1,insetInlineStart:0,width:"100%",top:"100%",height:o},[`&-disabled ${r}-node-content-wrapper`]:{color:t.colorTextDisabled,cursor:"not-allowed","&:hover":{background:"transparent"}},[`${r}-checkbox-disabled + ${r}-node-selected,&${n}-disabled${n}-selected ${r}-node-content-wrapper`]:{backgroundColor:v},[`${r}-checkbox-disabled`]:{pointerEvents:"unset"},[`&:not(${n}-disabled)`]:{[`${r}-node-content-wrapper`]:{"&:hover":{color:t.nodeHoverColor}}},[`&-active ${r}-node-content-wrapper`]:{background:t.controlItemBgHover},[`&:not(${n}-disabled).filter-node ${r}-title`]:{color:t.colorPrimary,fontWeight:500},"&-draggable":{cursor:"grab",[`${r}-draggable-icon`]:{flexShrink:0,width:i,textAlign:"center",visibility:"visible",color:s},[`&${n}-disabled ${r}-draggable-icon`]:{visibility:"hidden"}}},[`${r}-indent`]:{alignSelf:"stretch",whiteSpace:"nowrap",userSelect:"none","&-unit":{display:"inline-block",width:d}},[`${r}-draggable-icon`]:{visibility:"hidden"},[`${r}-switcher, ${r}-checkbox`]:{marginInlineEnd:t.calc(t.calc(i).sub(t.controlInteractiveSize)).div(2).equal()},[`${r}-switcher`]:Object.assign(Object.assign({},fc(e,t)),{position:"relative",flex:"none",alignSelf:"stretch",width:i,textAlign:"center",cursor:"pointer",userSelect:"none",transition:`all ${t.motionDurationSlow}`,"&-noop":{cursor:"unset"},"&:before":{pointerEvents:"none",content:'""',width:i,height:i,position:"absolute",left:{_skip_check_:!0,value:0},top:0,borderRadius:t.borderRadius,transition:`all ${t.motionDurationSlow}`},[`&:not(${r}-switcher-noop):hover:before`]:{backgroundColor:t.colorBgTextHover},[`&_close ${r}-switcher-icon svg`]:{transform:"rotate(-90deg)"},"&-loading-icon":{color:t.colorPrimary},"&-leaf-line":{position:"relative",zIndex:1,display:"inline-block",width:"100%",height:"100%","&:before":{position:"absolute",top:0,insetInlineEnd:t.calc(i).div(2).equal(),bottom:t.calc(o).mul(-1).equal(),marginInlineStart:-1,borderInlineEnd:`1px solid ${t.colorBorder}`,content:'""'},"&:after":{position:"absolute",width:t.calc(t.calc(i).div(2).equal()).mul(.8).equal(),height:t.calc(i).div(2).equal(),borderBottom:`1px solid ${t.colorBorder}`,content:'""'}}}),[`${r}-node-content-wrapper`]:Object.assign(Object.assign({position:"relative",minHeight:i,paddingBlock:0,paddingInline:t.paddingXS,background:"transparent",borderRadius:t.borderRadius,cursor:"pointer",transition:`all ${t.motionDurationMid}, border 0s, line-height 0s, box-shadow 0s`},vc(e,t)),{"&:hover":{backgroundColor:c},[`&${r}-node-selected`]:{color:t.nodeSelectedColor,backgroundColor:l},[`${r}-iconEle`]:{display:"inline-block",width:i,height:i,textAlign:"center",verticalAlign:"top","&:empty":{display:"none"}}}),[`${r}-unselectable ${r}-node-content-wrapper:hover`]:{backgroundColor:"transparent"},[`${n}.drop-container > [draggable]`]:{boxShadow:`0 0 0 2px ${t.colorPrimary}`},"&-show-line":{[`${r}-indent-unit`]:{position:"relative",height:"100%","&:before":{position:"absolute",top:0,insetInlineEnd:t.calc(i).div(2).equal(),bottom:t.calc(o).mul(-1).equal(),borderInlineEnd:`1px solid ${t.colorBorder}`,content:'""'},"&-end:before":{display:"none"}},[`${r}-switcher`]:{background:"transparent","&-line-icon":{verticalAlign:"-0.15em"}}},[`${n}-leaf-last ${r}-switcher-leaf-line:before`]:{top:"auto !important",bottom:"auto !important",height:`${W(t.calc(i).div(2).equal())} !important`}})}},mc=(e,t)=>{const r=`.${e}`,n=`${r}-treenode`,o=t.calc(t.paddingXS).div(2).equal(),i=sn(t,{treeCls:r,treeNodeCls:n,treeNodePadding:o});return[pc(e,i),dc(i)]},gc=e=>{const{controlHeightSM:t,controlItemBgHover:r,controlItemBgActive:n}=e,o=t;return{titleHeight:o,indentSize:o,nodeHoverBg:r,nodeHoverColor:e.colorText,nodeSelectedBg:n,nodeSelectedColor:e.colorText}},hc=e=>{const{colorTextLightSolid:t,colorPrimary:r}=e;return Object.assign(Object.assign({},gc(e)),{directoryNodeSelectedColor:t,directoryNodeSelectedBg:r})},bc=Xt("Tree",(e,t)=>{let{prefixCls:r}=t;return[{[e.componentCls]:No(`${r}-checkbox`,e)},mc(r,e),Ya(e)]},hc),Vr=4;function yc(e){const{dropPosition:t,dropLevelOffset:r,prefixCls:n,indent:o,direction:i="ltr"}=e,d=i==="ltr"?"left":"right",l=i==="ltr"?"right":"left",c={[d]:-r*o+Vr,[l]:0};switch(t){case-1:c.top=-3;break;case 1:c.bottom=-3;break;default:c.bottom=-3,c[d]=o+Vr;break}return me.createElement("div",{style:c,className:`${n}-drop-indicator`})}const Cc=e=>{const{prefixCls:t,switcherIcon:r,treeNodeProps:n,showLine:o,switcherLoadingIcon:i}=e,{isLeaf:d,expanded:l,loading:c}=n;if(c)return a.isValidElement(i)?i:a.createElement(Za,{className:`${t}-switcher-loading-icon`});let s;if(o&&typeof o=="object"&&(s=o.showLeafIcon),d){if(!o)return null;if(typeof s!="boolean"&&s){const p=typeof s=="function"?s(n):s,f=`${t}-switcher-line-custom-icon`;return a.isValidElement(p)?Er(p,{className:G(p.props.className||"",f)}):p}return s?a.createElement(Do,{className:`${t}-switcher-line-icon`}):a.createElement("span",{className:`${t}-switcher-leaf-line`})}const v=`${t}-switcher-icon`,u=typeof r=="function"?r(n):r;return a.isValidElement(u)?Er(u,{className:G(u.props.className||"",v)}):u!==void 0?u:o?l?a.createElement(wi,{className:`${t}-switcher-line-icon`}):a.createElement(ki,{className:`${t}-switcher-line-icon`}):a.createElement(Zl,{className:v})},ta=me.forwardRef((e,t)=>{var r;const{getPrefixCls:n,direction:o,virtual:i,tree:d}=me.useContext(rt),{prefixCls:l,className:c,showIcon:s=!1,showLine:v,switcherIcon:u,switcherLoadingIcon:p,blockNode:f=!1,children:g,checkable:y=!1,selectable:m=!0,draggable:h,motion:x,style:C}=e,S=n("tree",l),E=n(),$=x??Object.assign(Object.assign({},Ja(E)),{motionAppear:!1}),k=Object.assign(Object.assign({},e),{checkable:y,selectable:m,showIcon:s,motion:$,blockNode:f,showLine:!!v,dropIndicatorRender:yc}),[R,b,T]=bc(S),[,D]=go(),P=D.paddingXS/2+(((r=D.Tree)===null||r===void 0?void 0:r.titleHeight)||D.controlHeightSM),O=me.useMemo(()=>{if(!h)return!1;let w={};switch(typeof h){case"function":w.nodeDraggable=h;break;case"object":w=Object.assign({},h);break}return w.icon!==!1&&(w.icon=w.icon||me.createElement(Ci,null)),w},[h]),N=w=>me.createElement(Cc,{prefixCls:S,switcherIcon:u,switcherLoadingIcon:p,treeNodeProps:w,showLine:v});return R(me.createElement(Zn,Object.assign({itemHeight:P,ref:t,virtual:i},k,{style:Object.assign(Object.assign({},d==null?void 0:d.style),C),prefixCls:S,className:G({[`${S}-icon-hide`]:!s,[`${S}-block-node`]:f,[`${S}-unselectable`]:!m,[`${S}-rtl`]:o==="rtl"},d==null?void 0:d.className,c,b,T),direction:o,checkable:y&&me.createElement("span",{className:`${S}-checkbox-inner`}),selectable:m,switcherIcon:N,draggable:O}),g))}),qr=0,En=1,Xr=2;function Jn(e,t,r){const{key:n,children:o}=r;function i(d){const l=d[n],c=d[o];t(l,d)!==!1&&Jn(c||[],t,r)}e.forEach(i)}function xc(e){let{treeData:t,expandedKeys:r,startKey:n,endKey:o,fieldNames:i}=e;const d=[];let l=qr;if(n&&n===o)return[n];if(!n||!o)return[];function c(s){return s===n||s===o}return Jn(t,s=>{if(l===Xr)return!1;if(c(s)){if(d.push(s),l===qr)l=En;else if(l===En)return l=Xr,!1}else l===En&&d.push(s);return r.includes(s)},Tt(i)),d}function $n(e,t,r){const n=ce(t),o=[];return Jn(e,(i,d)=>{const l=n.indexOf(i);return l!==-1&&(o.push(d),n.splice(l,1)),!!n.length},Tt(r)),o}var Gr=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};function Sc(e){const{isLeaf:t,expanded:r}=e;return t?a.createElement(Do,null):r?a.createElement(pi,null):a.createElement(hi,null)}function Ur(e){let{treeData:t,children:r}=e;return t||$o(r)}const wc=(e,t)=>{var{defaultExpandAll:r,defaultExpandParent:n,defaultExpandedKeys:o}=e,i=Gr(e,["defaultExpandAll","defaultExpandParent","defaultExpandedKeys"]);const d=a.useRef(null),l=a.useRef(null),c=()=>{const{keyEntities:R}=Wn(Ur(i));let b;return r?b=Object.keys(R):n?b=Kn(i.expandedKeys||o||[],R):b=i.expandedKeys||o||[],b},[s,v]=a.useState(i.selectedKeys||i.defaultSelectedKeys||[]),[u,p]=a.useState(()=>c());a.useEffect(()=>{"selectedKeys"in i&&v(i.selectedKeys)},[i.selectedKeys]),a.useEffect(()=>{"expandedKeys"in i&&p(i.expandedKeys)},[i.expandedKeys]);const f=(R,b)=>{var T;return"expandedKeys"in i||p(R),(T=i.onExpand)===null||T===void 0?void 0:T.call(i,R,b)},g=(R,b)=>{var T;const{multiple:D,fieldNames:P}=i,{node:O,nativeEvent:N}=b,{key:w=""}=O,I=Ur(i),K=Object.assign(Object.assign({},b),{selected:!0}),L=(N==null?void 0:N.ctrlKey)||(N==null?void 0:N.metaKey),M=N==null?void 0:N.shiftKey;let V;D&&L?(V=R,d.current=w,l.current=V,K.selectedNodes=$n(I,V,P)):D&&M?(V=Array.from(new Set([].concat(ce(l.current||[]),ce(xc({treeData:I,expandedKeys:u,startKey:w,endKey:d.current,fieldNames:P}))))),K.selectedNodes=$n(I,V,P)):(V=[w],d.current=w,l.current=V,K.selectedNodes=$n(I,V,P)),(T=i.onSelect)===null||T===void 0||T.call(i,V,K),"selectedKeys"in i||v(V)},{getPrefixCls:y,direction:m}=a.useContext(rt),{prefixCls:h,className:x,showIcon:C=!0,expandAction:S="click"}=i,E=Gr(i,["prefixCls","className","showIcon","expandAction"]),$=y("tree",h),k=G(`${$}-directory`,{[`${$}-directory-rtl`]:m==="rtl"},x);return a.createElement(ta,Object.assign({icon:Sc,ref:t,blockNode:!0},E,{showIcon:C,expandAction:S,prefixCls:$,className:k,expandedKeys:u,selectedKeys:s,onSelect:g,onExpand:f}))},Ec=a.forwardRef(wc),er=ta;er.DirectoryTree=Ec;er.TreeNode=qt;const Yr=e=>{const{value:t,filterSearch:r,tablePrefixCls:n,locale:o,onChange:i}=e;return r?a.createElement("div",{className:`${n}-filter-dropdown-search`},a.createElement(ul,{prefix:a.createElement(dl,null),placeholder:o.filterSearchPlaceholder,onChange:i,value:t,htmlSize:1,className:`${n}-filter-dropdown-search-input`})):null},$c=e=>{const{keyCode:t}=e;t===gt.ENTER&&e.stopPropagation()},kc=a.forwardRef((e,t)=>a.createElement("div",{className:e.className,onClick:r=>r.stopPropagation(),onKeyDown:$c,ref:t},e.children));function Pt(e){let t=[];return(e||[]).forEach(r=>{let{value:n,children:o}=r;t.push(n),o&&(t=[].concat(ce(t),ce(Pt(o))))}),t}function Nc(e){return e.some(t=>{let{children:r}=t;return r})}function na(e,t){return typeof t=="string"||typeof t=="number"?t==null?void 0:t.toString().toLowerCase().includes(e.trim().toLowerCase()):!1}function ra(e){let{filters:t,prefixCls:r,filteredKeys:n,filterMultiple:o,searchValue:i,filterSearch:d}=e;return t.map((l,c)=>{const s=String(l.value);if(l.children)return{key:s||c,label:l.text,popupClassName:`${r}-dropdown-submenu`,children:ra({filters:l.children,prefixCls:r,filteredKeys:n,filterMultiple:o,searchValue:i,filterSearch:d})};const v=o?Dt:Ut,u={key:l.value!==void 0?s:c,label:a.createElement(a.Fragment,null,a.createElement(v,{checked:n.includes(s)}),a.createElement("span",null,l.text))};return i.trim()?typeof d=="function"?d(i,l)?u:null:na(i,l.text)?u:null:u})}function kn(e){return e||[]}const Rc=e=>{var t,r,n,o;const{tablePrefixCls:i,prefixCls:d,column:l,dropdownPrefixCls:c,columnKey:s,filterOnClose:v,filterMultiple:u,filterMode:p="menu",filterSearch:f=!1,filterState:g,triggerFilter:y,locale:m,children:h,getPopupContainer:x,rootClassName:C}=e,{filterResetToDefaultFilteredValue:S,defaultFilteredValue:E,filterDropdownProps:$={},filterDropdownOpen:k,filterDropdownVisible:R,onFilterDropdownVisibleChange:b,onFilterDropdownOpenChange:T}=l,[D,P]=a.useState(!1),O=!!(g&&(!((t=g.filteredKeys)===null||t===void 0)&&t.length||g.forceFiltered)),N=_=>{var F;P(_),(F=$.onOpenChange)===null||F===void 0||F.call($,_),T==null||T(_),b==null||b(_)},w=(o=(n=(r=$.open)!==null&&r!==void 0?r:k)!==null&&n!==void 0?n:R)!==null&&o!==void 0?o:D,I=g==null?void 0:g.filteredKeys,[K,L]=Js(kn(I)),M=_=>{let{selectedKeys:F}=_;L(F)},V=(_,F)=>{let{node:ne,checked:he}=F;M(u?{selectedKeys:_}:{selectedKeys:he&&ne.key?[ne.key]:[]})};a.useEffect(()=>{D&&M({selectedKeys:kn(I)})},[I]);const[Q,U]=a.useState([]),te=_=>{U(_)},[ye,Se]=a.useState(""),Ee=_=>{const{value:F}=_.target;Se(F)};a.useEffect(()=>{D||Se("")},[D]);const Z=_=>{const F=_!=null&&_.length?_:null;if(F===null&&(!g||!g.filteredKeys)||Vt(F,g==null?void 0:g.filteredKeys,!0))return null;y({column:l,key:s,filteredKeys:F})},J=()=>{N(!1),Z(K())},$e=function(){let{confirm:_,closeDropdown:F}=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{confirm:!1,closeDropdown:!1};_&&Z([]),F&&N(!1),Se(""),L(S?(E||[]).map(ne=>String(ne)):[])},ge=function(){let{closeDropdown:_}=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{closeDropdown:!0};_&&N(!1),Z(K())},q=(_,F)=>{F.source==="trigger"&&(_&&I!==void 0&&L(kn(I)),N(_),!_&&!l.filterDropdown&&v&&J())},X=G({[`${c}-menu-without-submenu`]:!Nc(l.filters||[])}),z=_=>{if(_.target.checked){const F=Pt(l==null?void 0:l.filters).map(ne=>String(ne));L(F)}else L([])},A=_=>{let{filters:F}=_;return(F||[]).map((ne,he)=>{const de=String(ne.value),we={title:ne.text,key:ne.value!==void 0?de:String(he)};return ne.children&&(we.children=A({filters:ne.children})),we})},ee=_=>{var F;return Object.assign(Object.assign({},_),{text:_.title,value:_.key,children:((F=_.children)===null||F===void 0?void 0:F.map(ne=>ee(ne)))||[]})};let oe;const{direction:re,renderEmpty:Ne}=a.useContext(rt);if(typeof l.filterDropdown=="function")oe=l.filterDropdown({prefixCls:`${c}-custom`,setSelectedKeys:_=>M({selectedKeys:_}),selectedKeys:K(),confirm:ge,clearFilters:$e,filters:l.filters,visible:w,close:()=>{N(!1)}});else if(l.filterDropdown)oe=l.filterDropdown;else{const _=K()||[],F=()=>{var he,de;const we=(he=Ne==null?void 0:Ne("Table.filter"))!==null&&he!==void 0?he:a.createElement(kr,{image:kr.PRESENTED_IMAGE_SIMPLE,description:m.filterEmptyText,styles:{image:{height:24}},style:{margin:0,padding:"16px 0"}});if((l.filters||[]).length===0)return we;if(p==="tree")return a.createElement(a.Fragment,null,a.createElement(Yr,{filterSearch:f,value:ye,onChange:Ee,tablePrefixCls:i,locale:m}),a.createElement("div",{className:`${i}-filter-dropdown-tree`},u?a.createElement(Dt,{checked:_.length===Pt(l.filters).length,indeterminate:_.length>0&&_.length<Pt(l.filters).length,className:`${i}-filter-dropdown-checkall`,onChange:z},(de=m==null?void 0:m.filterCheckall)!==null&&de!==void 0?de:m==null?void 0:m.filterCheckAll):null,a.createElement(er,{checkable:!0,selectable:!1,blockNode:!0,multiple:u,checkStrictly:!u,className:`${c}-menu`,onCheck:V,checkedKeys:_,selectedKeys:_,showIcon:!1,treeData:A({filters:l.filters}),autoExpandParent:!0,defaultExpandAll:!0,filterTreeNode:ye.trim()?be=>typeof f=="function"?f(ye,ee(be)):na(ye,be.title):void 0})));const Te=ra({filters:l.filters||[],filterSearch:f,prefixCls:d,filteredKeys:K(),filterMultiple:u,searchValue:ye}),Le=Te.every(be=>be===null);return a.createElement(a.Fragment,null,a.createElement(Yr,{filterSearch:f,value:ye,onChange:Ee,tablePrefixCls:i,locale:m}),Le?we:a.createElement(el,{selectable:!0,multiple:u,prefixCls:`${c}-menu`,className:X,onSelect:M,onDeselect:M,selectedKeys:_,getPopupContainer:x,openKeys:Q,onOpenChange:te,items:Te}))},ne=()=>S?Vt((E||[]).map(he=>String(he)),_,!0):_.length===0;oe=a.createElement(a.Fragment,null,F(),a.createElement("div",{className:`${d}-dropdown-btns`},a.createElement(Rn,{type:"link",size:"small",disabled:ne(),onClick:()=>$e()},m.filterReset),a.createElement(Rn,{type:"primary",size:"small",onClick:J},m.filterConfirm)))}l.filterDropdown&&(oe=a.createElement(tl,{selectable:void 0},oe)),oe=a.createElement(kc,{className:`${d}-dropdown`},oe);const Oe=bo({trigger:["click"],placement:re==="rtl"?"bottomLeft":"bottomRight",children:(()=>{let _;return typeof l.filterIcon=="function"?_=l.filterIcon(O):l.filterIcon?_=l.filterIcon:_=a.createElement(ui,null),a.createElement("span",{role:"button",tabIndex:-1,className:G(`${d}-trigger`,{active:O}),onClick:F=>{F.stopPropagation()}},_)})(),getPopupContainer:x},Object.assign(Object.assign({},$),{rootClassName:G(C,$.rootClassName),open:w,onOpenChange:q,dropdownRender:()=>typeof($==null?void 0:$.dropdownRender)=="function"?$.dropdownRender(oe):oe}));return a.createElement("div",{className:`${d}-column`},a.createElement("span",{className:`${i}-column-title`},h),a.createElement(mo,Object.assign({},Oe)))},Ln=(e,t,r)=>{let n=[];return(e||[]).forEach((o,i)=>{var d;const l=Ht(i,r);if(o.filters||"filterDropdown"in o||"onFilter"in o)if("filteredValue"in o){let c=o.filteredValue;"filterDropdown"in o||(c=(d=c==null?void 0:c.map(String))!==null&&d!==void 0?d:c),n.push({column:o,key:yt(o,l),filteredKeys:c,forceFiltered:o.filtered})}else n.push({column:o,key:yt(o,l),filteredKeys:t&&o.defaultFilteredValue?o.defaultFilteredValue:void 0,forceFiltered:o.filtered});"children"in o&&(n=[].concat(ce(n),ce(Ln(o.children,t,l))))}),n};function oa(e,t,r,n,o,i,d,l,c){return r.map((s,v)=>{const u=Ht(v,l),{filterOnClose:p=!0,filterMultiple:f=!0,filterMode:g,filterSearch:y}=s;let m=s;if(m.filters||m.filterDropdown){const h=yt(m,u),x=n.find(C=>{let{key:S}=C;return h===S});m=Object.assign(Object.assign({},m),{title:C=>a.createElement(Rc,{tablePrefixCls:e,prefixCls:`${e}-filter`,dropdownPrefixCls:t,column:m,columnKey:h,filterState:x,filterOnClose:p,filterMultiple:f,filterMode:g,filterSearch:y,triggerFilter:i,locale:o,getPopupContainer:d,rootClassName:c},vn(s.title,C))})}return"children"in m&&(m=Object.assign(Object.assign({},m),{children:oa(e,t,m.children,n,o,i,d,u,c)})),m})}const Qr=e=>{const t={};return e.forEach(r=>{let{key:n,filteredKeys:o,column:i}=r;const d=n,{filters:l,filterDropdown:c}=i;if(c)t[d]=o||null;else if(Array.isArray(o)){const s=Pt(l);t[d]=s.filter(v=>o.includes(String(v)))}else t[d]=null}),t},_n=(e,t,r)=>t.reduce((o,i)=>{const{column:{onFilter:d,filters:l},filteredKeys:c}=i;return d&&c&&c.length?o.map(s=>Object.assign({},s)).filter(s=>c.some(v=>{const u=Pt(l),p=u.findIndex(g=>String(g)===String(v)),f=p!==-1?u[p]:v;return s[r]&&(s[r]=_n(s[r],t,r)),d(f,s)})):o},e),aa=e=>e.flatMap(t=>"children"in t?[t].concat(ce(aa(t.children||[]))):[t]),Oc=e=>{const{prefixCls:t,dropdownPrefixCls:r,mergedColumns:n,onFilterChange:o,getPopupContainer:i,locale:d,rootClassName:l}=e;An();const c=a.useMemo(()=>aa(n||[]),[n]),[s,v]=a.useState(()=>Ln(c,!0)),u=a.useMemo(()=>{const y=Ln(c,!1);if(y.length===0)return y;let m=!0;if(y.forEach(h=>{let{filteredKeys:x}=h;x!==void 0&&(m=!1)}),m){const h=(c||[]).map((x,C)=>yt(x,Ht(C)));return s.filter(x=>{let{key:C}=x;return h.includes(C)}).map(x=>{const C=c[h.findIndex(S=>S===x.key)];return Object.assign(Object.assign({},x),{column:Object.assign(Object.assign({},x.column),C),forceFiltered:C.filtered})})}return y},[c,s]),p=a.useMemo(()=>Qr(u),[u]),f=y=>{const m=u.filter(h=>{let{key:x}=h;return x!==y.key});m.push(y),v(m),o(Qr(m),m)};return[y=>oa(t,r,y,u,d,f,i,void 0,l),u,p]},Ic=(e,t,r)=>{const n=a.useRef({});function o(i){var d;if(!n.current||n.current.data!==e||n.current.childrenColumnName!==t||n.current.getRowKey!==r){let c=function(s){s.forEach((v,u)=>{const p=r(v,u);l.set(p,v),v&&typeof v=="object"&&t in v&&c(v[t]||[])})};const l=new Map;c(e),n.current={data:e,childrenColumnName:t,kvMap:l,getRowKey:r}}return(d=n.current.kvMap)===null||d===void 0?void 0:d.get(i)}return[o]};var Pc=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};const la=10;function Kc(e,t){const r={current:e.current,pageSize:e.pageSize};return Object.keys(t&&typeof t=="object"?t:{}).forEach(o=>{const i=e[o];typeof i!="function"&&(r[o]=i)}),r}function Tc(e,t,r){const n=r&&typeof r=="object"?r:{},{total:o=0}=n,i=Pc(n,["total"]),[d,l]=a.useState(()=>({current:"defaultCurrent"in i?i.defaultCurrent:1,pageSize:"defaultPageSize"in i?i.defaultPageSize:la})),c=bo(d,i,{total:o>0?o:e}),s=Math.ceil((o||e)/c.pageSize);c.current>s&&(c.current=s||1);const v=(p,f)=>{l({current:p??1,pageSize:f||c.pageSize})},u=(p,f)=>{var g;r&&((g=r.onChange)===null||g===void 0||g.call(r,p,f)),v(p,f),t(p,f||(c==null?void 0:c.pageSize))};return r===!1?[{},()=>{}]:[Object.assign(Object.assign({},c),{onChange:u}),v]}const rn="ascend",Nn="descend",an=e=>typeof e.sorter=="object"&&typeof e.sorter.multiple=="number"?e.sorter.multiple:!1,Zr=e=>typeof e=="function"?e:e&&typeof e=="object"&&e.compare?e.compare:!1,Dc=(e,t)=>t?e[e.indexOf(t)+1]:e[0],Hn=(e,t,r)=>{let n=[];const o=(i,d)=>{n.push({column:i,key:yt(i,d),multiplePriority:an(i),sortOrder:i.sortOrder})};return(e||[]).forEach((i,d)=>{const l=Ht(d,r);i.children?("sortOrder"in i&&o(i,l),n=[].concat(ce(n),ce(Hn(i.children,t,l)))):i.sorter&&("sortOrder"in i?o(i,l):t&&i.defaultSortOrder&&n.push({column:i,key:yt(i,l),multiplePriority:an(i),sortOrder:i.defaultSortOrder}))}),n},ia=(e,t,r,n,o,i,d,l)=>(t||[]).map((s,v)=>{const u=Ht(v,l);let p=s;if(p.sorter){const f=p.sortDirections||o,g=p.showSorterTooltip===void 0?d:p.showSorterTooltip,y=yt(p,u),m=r.find(b=>{let{key:T}=b;return T===y}),h=m?m.sortOrder:null,x=Dc(f,h);let C;if(s.sortIcon)C=s.sortIcon({sortOrder:h});else{const b=f.includes(rn)&&a.createElement(oi,{className:G(`${e}-column-sorter-up`,{active:h===rn})}),T=f.includes(Nn)&&a.createElement(ti,{className:G(`${e}-column-sorter-down`,{active:h===Nn})});C=a.createElement("span",{className:G(`${e}-column-sorter`,{[`${e}-column-sorter-full`]:!!(b&&T)})},a.createElement("span",{className:`${e}-column-sorter-inner`,"aria-hidden":"true"},b,T))}const{cancelSort:S,triggerAsc:E,triggerDesc:$}=i||{};let k=S;x===Nn?k=$:x===rn&&(k=E);const R=typeof g=="object"?Object.assign({title:k},g):{title:k};p=Object.assign(Object.assign({},p),{className:G(p.className,{[`${e}-column-sort`]:h}),title:b=>{const T=`${e}-column-sorters`,D=a.createElement("span",{className:`${e}-column-title`},vn(s.title,b)),P=a.createElement("div",{className:T},D,C);return g?typeof g!="boolean"&&(g==null?void 0:g.target)==="sorter-icon"?a.createElement("div",{className:`${T} ${e}-column-sorters-tooltip-target-sorter`},D,a.createElement($r,Object.assign({},R),C)):a.createElement($r,Object.assign({},R),P):P},onHeaderCell:b=>{var T;const D=((T=s.onHeaderCell)===null||T===void 0?void 0:T.call(s,b))||{},P=D.onClick,O=D.onKeyDown;D.onClick=I=>{n({column:s,key:y,sortOrder:x,multiplePriority:an(s)}),P==null||P(I)},D.onKeyDown=I=>{I.keyCode===gt.ENTER&&(n({column:s,key:y,sortOrder:x,multiplePriority:an(s)}),O==null||O(I))};const N=Zs(s.title,{}),w=N==null?void 0:N.toString();return h&&(D["aria-sort"]=h==="ascend"?"ascending":"descending"),D["aria-label"]=w||"",D.className=G(D.className,`${e}-column-has-sorters`),D.tabIndex=0,s.ellipsis&&(D.title=(N??"").toString()),D}})}return"children"in p&&(p=Object.assign(Object.assign({},p),{children:ia(e,p.children,r,n,o,i,d,u)})),p}),Jr=e=>{const{column:t,sortOrder:r}=e;return{column:t,order:r,field:t.dataIndex,columnKey:t.key}},eo=e=>{const t=e.filter(r=>{let{sortOrder:n}=r;return n}).map(Jr);if(t.length===0&&e.length){const r=e.length-1;return Object.assign(Object.assign({},Jr(e[r])),{column:void 0,order:void 0,field:void 0,columnKey:void 0})}return t.length<=1?t[0]||{}:t},zn=(e,t,r)=>{const n=t.slice().sort((d,l)=>l.multiplePriority-d.multiplePriority),o=e.slice(),i=n.filter(d=>{let{column:{sorter:l},sortOrder:c}=d;return Zr(l)&&c});return i.length?o.sort((d,l)=>{for(let c=0;c<i.length;c+=1){const s=i[c],{column:{sorter:v},sortOrder:u}=s,p=Zr(v);if(p&&u){const f=p(d,l,u);if(f!==0)return u===rn?f:-f}}return 0}).map(d=>{const l=d[r];return l?Object.assign(Object.assign({},d),{[r]:zn(l,t,r)}):d}):o},Mc=e=>{const{prefixCls:t,mergedColumns:r,sortDirections:n,tableLocale:o,showSorterTooltip:i,onSorterChange:d}=e,[l,c]=a.useState(Hn(r,!0)),s=(y,m)=>{const h=[];return y.forEach((x,C)=>{const S=Ht(C,m);if(h.push(yt(x,S)),Array.isArray(x.children)){const E=s(x.children,S);h.push.apply(h,ce(E))}}),h},v=a.useMemo(()=>{let y=!0;const m=Hn(r,!1);if(!m.length){const S=s(r);return l.filter(E=>{let{key:$}=E;return S.includes($)})}const h=[];function x(S){y?h.push(S):h.push(Object.assign(Object.assign({},S),{sortOrder:null}))}let C=null;return m.forEach(S=>{C===null?(x(S),S.sortOrder&&(S.multiplePriority===!1?y=!1:C=!0)):(C&&S.multiplePriority!==!1||(y=!1),x(S))}),h},[r,l]),u=a.useMemo(()=>{var y,m;const h=v.map(x=>{let{column:C,sortOrder:S}=x;return{column:C,order:S}});return{sortColumns:h,sortColumn:(y=h[0])===null||y===void 0?void 0:y.column,sortOrder:(m=h[0])===null||m===void 0?void 0:m.order}},[v]),p=y=>{let m;y.multiplePriority===!1||!v.length||v[0].multiplePriority===!1?m=[y]:m=[].concat(ce(v.filter(h=>{let{key:x}=h;return x!==y.key})),[y]),c(m),d(eo(m),m)};return[y=>ia(t,y,v,p,n,o,i),v,u,()=>eo(v)]},sa=(e,t)=>e.map(n=>{const o=Object.assign({},n);return o.title=vn(n.title,t),"children"in o&&(o.children=sa(o.children,t)),o}),Bc=e=>[a.useCallback(r=>sa(r,e),[e])],Lc=Uo((e,t)=>{const{_renderTimes:r}=e,{_renderTimes:n}=t;return r!==n}),_c=Qo((e,t)=>{const{_renderTimes:r}=e,{_renderTimes:n}=t;return r!==n}),Hc=e=>{const{componentCls:t,lineWidth:r,lineType:n,tableBorderColor:o,tableHeaderBg:i,tablePaddingVertical:d,tablePaddingHorizontal:l,calc:c}=e,s=`${W(r)} ${n} ${o}`,v=(u,p,f)=>({[`&${t}-${u}`]:{[`> ${t}-container`]:{[`> ${t}-content, > ${t}-body`]:{"\n            > table > tbody > tr > th,\n            > table > tbody > tr > td\n          ":{[`> ${t}-expanded-row-fixed`]:{margin:`${W(c(p).mul(-1).equal())}
              ${W(c(c(f).add(r)).mul(-1).equal())}`}}}}}});return{[`${t}-wrapper`]:{[`${t}${t}-bordered`]:Object.assign(Object.assign(Object.assign({[`> ${t}-title`]:{border:s,borderBottom:0},[`> ${t}-container`]:{borderInlineStart:s,borderTop:s,[`
            > ${t}-content,
            > ${t}-header,
            > ${t}-body,
            > ${t}-summary
          `]:{"> table":{"\n                > thead > tr > th,\n                > thead > tr > td,\n                > tbody > tr > th,\n                > tbody > tr > td,\n                > tfoot > tr > th,\n                > tfoot > tr > td\n              ":{borderInlineEnd:s},"> thead":{"> tr:not(:last-child) > th":{borderBottom:s},"> tr > th::before":{backgroundColor:"transparent !important"}},"\n                > thead > tr,\n                > tbody > tr,\n                > tfoot > tr\n              ":{[`> ${t}-cell-fix-right-first::after`]:{borderInlineEnd:s}},"\n                > tbody > tr > th,\n                > tbody > tr > td\n              ":{[`> ${t}-expanded-row-fixed`]:{margin:`${W(c(d).mul(-1).equal())} ${W(c(c(l).add(r)).mul(-1).equal())}`,"&::after":{position:"absolute",top:0,insetInlineEnd:r,bottom:0,borderInlineEnd:s,content:'""'}}}}}},[`&${t}-scroll-horizontal`]:{[`> ${t}-container > ${t}-body`]:{"> table > tbody":{[`
                > tr${t}-expanded-row,
                > tr${t}-placeholder
              `]:{"> th, > td":{borderInlineEnd:0}}}}}},v("middle",e.tablePaddingVerticalMiddle,e.tablePaddingHorizontalMiddle)),v("small",e.tablePaddingVerticalSmall,e.tablePaddingHorizontalSmall)),{[`> ${t}-footer`]:{border:s,borderTop:0}}),[`${t}-cell`]:{[`${t}-container:first-child`]:{borderTop:0},"&-scrollbar:not([rowspan])":{boxShadow:`0 ${W(r)} 0 ${W(r)} ${i}`}},[`${t}-bordered ${t}-cell-scrollbar`]:{borderInlineEnd:s}}}},zc=e=>{const{componentCls:t}=e;return{[`${t}-wrapper`]:{[`${t}-cell-ellipsis`]:Object.assign(Object.assign({},nl),{wordBreak:"keep-all",[`
          &${t}-cell-fix-left-last,
          &${t}-cell-fix-right-first
        `]:{overflow:"visible",[`${t}-cell-content`]:{display:"block",overflow:"hidden",textOverflow:"ellipsis"}},[`${t}-column-title`]:{overflow:"hidden",textOverflow:"ellipsis",wordBreak:"keep-all"}})}}},jc=e=>{const{componentCls:t}=e;return{[`${t}-wrapper`]:{[`${t}-tbody > tr${t}-placeholder`]:{textAlign:"center",color:e.colorTextDisabled,"\n          &:hover > th,\n          &:hover > td,\n        ":{background:e.colorBgContainer}}}}},Fc=e=>{const{componentCls:t,antCls:r,motionDurationSlow:n,lineWidth:o,paddingXS:i,lineType:d,tableBorderColor:l,tableExpandIconBg:c,tableExpandColumnWidth:s,borderRadius:v,tablePaddingVertical:u,tablePaddingHorizontal:p,tableExpandedRowBg:f,paddingXXS:g,expandIconMarginTop:y,expandIconSize:m,expandIconHalfInner:h,expandIconScale:x,calc:C}=e,S=`${W(o)} ${d} ${l}`,E=C(g).sub(o).equal();return{[`${t}-wrapper`]:{[`${t}-expand-icon-col`]:{width:s},[`${t}-row-expand-icon-cell`]:{textAlign:"center",[`${t}-row-expand-icon`]:{display:"inline-flex",float:"none",verticalAlign:"sub"}},[`${t}-row-indent`]:{height:1,float:"left"},[`${t}-row-expand-icon`]:Object.assign(Object.assign({},rl(e)),{position:"relative",float:"left",width:m,height:m,color:"inherit",lineHeight:W(m),background:c,border:S,borderRadius:v,transform:`scale(${x})`,"&:focus, &:hover, &:active":{borderColor:"currentcolor"},"&::before, &::after":{position:"absolute",background:"currentcolor",transition:`transform ${n} ease-out`,content:'""'},"&::before":{top:h,insetInlineEnd:E,insetInlineStart:E,height:o},"&::after":{top:E,bottom:E,insetInlineStart:h,width:o,transform:"rotate(90deg)"},"&-collapsed::before":{transform:"rotate(-180deg)"},"&-collapsed::after":{transform:"rotate(0deg)"},"&-spaced":{"&::before, &::after":{display:"none",content:"none"},background:"transparent",border:0,visibility:"hidden"}}),[`${t}-row-indent + ${t}-row-expand-icon`]:{marginTop:y,marginInlineEnd:i},[`tr${t}-expanded-row`]:{"&, &:hover":{"> th, > td":{background:f}},[`${r}-descriptions-view`]:{display:"flex",table:{flex:"auto",width:"100%"}}},[`${t}-expanded-row-fixed`]:{position:"relative",margin:`${W(C(u).mul(-1).equal())} ${W(C(p).mul(-1).equal())}`,padding:`${W(u)} ${W(p)}`}}}},Ac=e=>{const{componentCls:t,antCls:r,iconCls:n,tableFilterDropdownWidth:o,tableFilterDropdownSearchWidth:i,paddingXXS:d,paddingXS:l,colorText:c,lineWidth:s,lineType:v,tableBorderColor:u,headerIconColor:p,fontSizeSM:f,tablePaddingHorizontal:g,borderRadius:y,motionDurationSlow:m,colorTextDescription:h,colorPrimary:x,tableHeaderFilterActiveBg:C,colorTextDisabled:S,tableFilterDropdownBg:E,tableFilterDropdownHeight:$,controlItemBgHover:k,controlItemBgActive:R,boxShadowSecondary:b,filterDropdownMenuBg:T,calc:D}=e,P=`${r}-dropdown`,O=`${t}-filter-dropdown`,N=`${r}-tree`,w=`${W(s)} ${v} ${u}`;return[{[`${t}-wrapper`]:{[`${t}-filter-column`]:{display:"flex",justifyContent:"space-between"},[`${t}-filter-trigger`]:{position:"relative",display:"flex",alignItems:"center",marginBlock:D(d).mul(-1).equal(),marginInline:`${W(d)} ${W(D(g).div(2).mul(-1).equal())}`,padding:`0 ${W(d)}`,color:p,fontSize:f,borderRadius:y,cursor:"pointer",transition:`all ${m}`,"&:hover":{color:h,background:C},"&.active":{color:x}}}},{[`${r}-dropdown`]:{[O]:Object.assign(Object.assign({},ft(e)),{minWidth:o,backgroundColor:E,borderRadius:y,boxShadow:b,overflow:"hidden",[`${P}-menu`]:{maxHeight:$,overflowX:"hidden",border:0,boxShadow:"none",borderRadius:"unset",backgroundColor:T,"&:empty::after":{display:"block",padding:`${W(l)} 0`,color:S,fontSize:f,textAlign:"center",content:'"Not Found"'}},[`${O}-tree`]:{paddingBlock:`${W(l)} 0`,paddingInline:l,[N]:{padding:0},[`${N}-treenode ${N}-node-content-wrapper:hover`]:{backgroundColor:k},[`${N}-treenode-checkbox-checked ${N}-node-content-wrapper`]:{"&, &:hover":{backgroundColor:R}}},[`${O}-search`]:{padding:l,borderBottom:w,"&-input":{input:{minWidth:i},[n]:{color:S}}},[`${O}-checkall`]:{width:"100%",marginBottom:d,marginInlineStart:d},[`${O}-btns`]:{display:"flex",justifyContent:"space-between",padding:`${W(D(l).sub(s).equal())} ${W(l)}`,overflow:"hidden",borderTop:w}})}},{[`${r}-dropdown ${O}, ${O}-submenu`]:{[`${r}-checkbox-wrapper + span`]:{paddingInlineStart:l,color:c},"> ul":{maxHeight:"calc(100vh - 130px)",overflowX:"hidden",overflowY:"auto"}}}]},Wc=e=>{const{componentCls:t,lineWidth:r,colorSplit:n,motionDurationSlow:o,zIndexTableFixed:i,tableBg:d,zIndexTableSticky:l,calc:c}=e,s=n;return{[`${t}-wrapper`]:{[`
        ${t}-cell-fix-left,
        ${t}-cell-fix-right
      `]:{position:"sticky !important",zIndex:i,background:d},[`
        ${t}-cell-fix-left-first::after,
        ${t}-cell-fix-left-last::after
      `]:{position:"absolute",top:0,right:{_skip_check_:!0,value:0},bottom:c(r).mul(-1).equal(),width:30,transform:"translateX(100%)",transition:`box-shadow ${o}`,content:'""',pointerEvents:"none"},[`${t}-cell-fix-left-all::after`]:{display:"none"},[`
        ${t}-cell-fix-right-first::after,
        ${t}-cell-fix-right-last::after
      `]:{position:"absolute",top:0,bottom:c(r).mul(-1).equal(),left:{_skip_check_:!0,value:0},width:30,transform:"translateX(-100%)",transition:`box-shadow ${o}`,content:'""',pointerEvents:"none"},[`${t}-container`]:{position:"relative","&::before, &::after":{position:"absolute",top:0,bottom:0,zIndex:c(l).add(1).equal({unit:!1}),width:30,transition:`box-shadow ${o}`,content:'""',pointerEvents:"none"},"&::before":{insetInlineStart:0},"&::after":{insetInlineEnd:0}},[`${t}-ping-left`]:{[`&:not(${t}-has-fix-left) ${t}-container::before`]:{boxShadow:`inset 10px 0 8px -8px ${s}`},[`
          ${t}-cell-fix-left-first::after,
          ${t}-cell-fix-left-last::after
        `]:{boxShadow:`inset 10px 0 8px -8px ${s}`},[`${t}-cell-fix-left-last::before`]:{backgroundColor:"transparent !important"}},[`${t}-ping-right`]:{[`&:not(${t}-has-fix-right) ${t}-container::after`]:{boxShadow:`inset -10px 0 8px -8px ${s}`},[`
          ${t}-cell-fix-right-first::after,
          ${t}-cell-fix-right-last::after
        `]:{boxShadow:`inset -10px 0 8px -8px ${s}`}},[`${t}-fixed-column-gapped`]:{[`
        ${t}-cell-fix-left-first::after,
        ${t}-cell-fix-left-last::after,
        ${t}-cell-fix-right-first::after,
        ${t}-cell-fix-right-last::after
      `]:{boxShadow:"none"}}}}},Vc=e=>{const{componentCls:t,antCls:r,margin:n}=e;return{[`${t}-wrapper`]:{[`${t}-pagination${r}-pagination`]:{margin:`${W(n)} 0`},[`${t}-pagination`]:{display:"flex",flexWrap:"wrap",rowGap:e.paddingXS,"> *":{flex:"none"},"&-left":{justifyContent:"flex-start"},"&-center":{justifyContent:"center"},"&-right":{justifyContent:"flex-end"}}}}},qc=e=>{const{componentCls:t,tableRadius:r}=e;return{[`${t}-wrapper`]:{[t]:{[`${t}-title, ${t}-header`]:{borderRadius:`${W(r)} ${W(r)} 0 0`},[`${t}-title + ${t}-container`]:{borderStartStartRadius:0,borderStartEndRadius:0,[`${t}-header, table`]:{borderRadius:0},"table > thead > tr:first-child":{"th:first-child, th:last-child, td:first-child, td:last-child":{borderRadius:0}}},"&-container":{borderStartStartRadius:r,borderStartEndRadius:r,"table > thead > tr:first-child":{"> *:first-child":{borderStartStartRadius:r},"> *:last-child":{borderStartEndRadius:r}}},"&-footer":{borderRadius:`0 0 ${W(r)} ${W(r)}`}}}}},Xc=e=>{const{componentCls:t}=e;return{[`${t}-wrapper-rtl`]:{direction:"rtl",table:{direction:"rtl"},[`${t}-pagination-left`]:{justifyContent:"flex-end"},[`${t}-pagination-right`]:{justifyContent:"flex-start"},[`${t}-row-expand-icon`]:{float:"right","&::after":{transform:"rotate(-90deg)"},"&-collapsed::before":{transform:"rotate(180deg)"},"&-collapsed::after":{transform:"rotate(0deg)"}},[`${t}-container`]:{"&::before":{insetInlineStart:"unset",insetInlineEnd:0},"&::after":{insetInlineStart:0,insetInlineEnd:"unset"},[`${t}-row-indent`]:{float:"right"}}}}},Gc=e=>{const{componentCls:t,antCls:r,iconCls:n,fontSizeIcon:o,padding:i,paddingXS:d,headerIconColor:l,headerIconHoverColor:c,tableSelectionColumnWidth:s,tableSelectedRowBg:v,tableSelectedRowHoverBg:u,tableRowHoverBg:p,tablePaddingHorizontal:f,calc:g}=e;return{[`${t}-wrapper`]:{[`${t}-selection-col`]:{width:s,[`&${t}-selection-col-with-dropdown`]:{width:g(s).add(o).add(g(i).div(4)).equal()}},[`${t}-bordered ${t}-selection-col`]:{width:g(s).add(g(d).mul(2)).equal(),[`&${t}-selection-col-with-dropdown`]:{width:g(s).add(o).add(g(i).div(4)).add(g(d).mul(2)).equal()}},[`
        table tr th${t}-selection-column,
        table tr td${t}-selection-column,
        ${t}-selection-column
      `]:{paddingInlineEnd:e.paddingXS,paddingInlineStart:e.paddingXS,textAlign:"center",[`${r}-radio-wrapper`]:{marginInlineEnd:0}},[`table tr th${t}-selection-column${t}-cell-fix-left`]:{zIndex:g(e.zIndexTableFixed).add(1).equal({unit:!1})},[`table tr th${t}-selection-column::after`]:{backgroundColor:"transparent !important"},[`${t}-selection`]:{position:"relative",display:"inline-flex",flexDirection:"column"},[`${t}-selection-extra`]:{position:"absolute",top:0,zIndex:1,cursor:"pointer",transition:`all ${e.motionDurationSlow}`,marginInlineStart:"100%",paddingInlineStart:W(g(f).div(4).equal()),[n]:{color:l,fontSize:o,verticalAlign:"baseline","&:hover":{color:c}}},[`${t}-tbody`]:{[`${t}-row`]:{[`&${t}-row-selected`]:{[`> ${t}-cell`]:{background:v,"&-row-hover":{background:u}}},[`> ${t}-cell-row-hover`]:{background:p}}}}}},Uc=e=>{const{componentCls:t,tableExpandColumnWidth:r,calc:n}=e,o=(i,d,l,c)=>({[`${t}${t}-${i}`]:{fontSize:c,[`
        ${t}-title,
        ${t}-footer,
        ${t}-cell,
        ${t}-thead > tr > th,
        ${t}-tbody > tr > th,
        ${t}-tbody > tr > td,
        tfoot > tr > th,
        tfoot > tr > td
      `]:{padding:`${W(d)} ${W(l)}`},[`${t}-filter-trigger`]:{marginInlineEnd:W(n(l).div(2).mul(-1).equal())},[`${t}-expanded-row-fixed`]:{margin:`${W(n(d).mul(-1).equal())} ${W(n(l).mul(-1).equal())}`},[`${t}-tbody`]:{[`${t}-wrapper:only-child ${t}`]:{marginBlock:W(n(d).mul(-1).equal()),marginInline:`${W(n(r).sub(l).equal())} ${W(n(l).mul(-1).equal())}`}},[`${t}-selection-extra`]:{paddingInlineStart:W(n(l).div(4).equal())}}});return{[`${t}-wrapper`]:Object.assign(Object.assign({},o("middle",e.tablePaddingVerticalMiddle,e.tablePaddingHorizontalMiddle,e.tableFontSizeMiddle)),o("small",e.tablePaddingVerticalSmall,e.tablePaddingHorizontalSmall,e.tableFontSizeSmall))}},Yc=e=>{const{componentCls:t,marginXXS:r,fontSizeIcon:n,headerIconColor:o,headerIconHoverColor:i}=e;return{[`${t}-wrapper`]:{[`${t}-thead th${t}-column-has-sorters`]:{outline:"none",cursor:"pointer",transition:`all ${e.motionDurationSlow}, left 0s`,"&:hover":{background:e.tableHeaderSortHoverBg,"&::before":{backgroundColor:"transparent !important"}},"&:focus-visible":{color:e.colorPrimary},[`
          &${t}-cell-fix-left:hover,
          &${t}-cell-fix-right:hover
        `]:{background:e.tableFixedHeaderSortActiveBg}},[`${t}-thead th${t}-column-sort`]:{background:e.tableHeaderSortBg,"&::before":{backgroundColor:"transparent !important"}},[`td${t}-column-sort`]:{background:e.tableBodySortBg},[`${t}-column-title`]:{position:"relative",zIndex:1,flex:1,minWidth:0},[`${t}-column-sorters`]:{display:"flex",flex:"auto",alignItems:"center",justifyContent:"space-between","&::after":{position:"absolute",inset:0,width:"100%",height:"100%",content:'""'}},[`${t}-column-sorters-tooltip-target-sorter`]:{"&::after":{content:"none"}},[`${t}-column-sorter`]:{marginInlineStart:r,color:o,fontSize:0,transition:`color ${e.motionDurationSlow}`,"&-inner":{display:"inline-flex",flexDirection:"column",alignItems:"center"},"&-up, &-down":{fontSize:n,"&.active":{color:e.colorPrimary}},[`${t}-column-sorter-up + ${t}-column-sorter-down`]:{marginTop:"-0.3em"}},[`${t}-column-sorters:hover ${t}-column-sorter`]:{color:i}}}},Qc=e=>{const{componentCls:t,opacityLoading:r,tableScrollThumbBg:n,tableScrollThumbBgHover:o,tableScrollThumbSize:i,tableScrollBg:d,zIndexTableSticky:l,stickyScrollBarBorderRadius:c,lineWidth:s,lineType:v,tableBorderColor:u}=e,p=`${W(s)} ${v} ${u}`;return{[`${t}-wrapper`]:{[`${t}-sticky`]:{"&-holder":{position:"sticky",zIndex:l,background:e.colorBgContainer},"&-scroll":{position:"sticky",bottom:0,height:`${W(i)} !important`,zIndex:l,display:"flex",alignItems:"center",background:d,borderTop:p,opacity:r,"&:hover":{transformOrigin:"center bottom"},"&-bar":{height:i,backgroundColor:n,borderRadius:c,transition:`all ${e.motionDurationSlow}, transform none`,position:"absolute",bottom:0,"&:hover, &-active":{backgroundColor:o}}}}}}},to=e=>{const{componentCls:t,lineWidth:r,tableBorderColor:n,calc:o}=e,i=`${W(r)} ${e.lineType} ${n}`;return{[`${t}-wrapper`]:{[`${t}-summary`]:{position:"relative",zIndex:e.zIndexTableFixed,background:e.tableBg,"> tr":{"> th, > td":{borderBottom:i}}},[`div${t}-summary`]:{boxShadow:`0 ${W(o(r).mul(-1).equal())} 0 ${n}`}}}},Zc=e=>{const{componentCls:t,motionDurationMid:r,lineWidth:n,lineType:o,tableBorderColor:i,calc:d}=e,l=`${W(n)} ${o} ${i}`,c=`${t}-expanded-row-cell`;return{[`${t}-wrapper`]:{[`${t}-tbody-virtual`]:{[`${t}-tbody-virtual-holder-inner`]:{[`
            & > ${t}-row, 
            & > div:not(${t}-row) > ${t}-row
          `]:{display:"flex",boxSizing:"border-box",width:"100%"}},[`${t}-cell`]:{borderBottom:l,transition:`background ${r}`},[`${t}-expanded-row`]:{[`${c}${c}-fixed`]:{position:"sticky",insetInlineStart:0,overflow:"hidden",width:`calc(var(--virtual-width) - ${W(n)})`,borderInlineEnd:"none"}}},[`${t}-bordered`]:{[`${t}-tbody-virtual`]:{"&:after":{content:'""',insetInline:0,bottom:0,borderBottom:l,position:"absolute"},[`${t}-cell`]:{borderInlineEnd:l,[`&${t}-cell-fix-right-first:before`]:{content:'""',position:"absolute",insetBlock:0,insetInlineStart:d(n).mul(-1).equal(),borderInlineStart:l}}},[`&${t}-virtual`]:{[`${t}-placeholder ${t}-cell`]:{borderInlineEnd:l,borderBottom:l}}}}}},Jc=e=>{const{componentCls:t,fontWeightStrong:r,tablePaddingVertical:n,tablePaddingHorizontal:o,tableExpandColumnWidth:i,lineWidth:d,lineType:l,tableBorderColor:c,tableFontSize:s,tableBg:v,tableRadius:u,tableHeaderTextColor:p,motionDurationMid:f,tableHeaderBg:g,tableHeaderCellSplitColor:y,tableFooterTextColor:m,tableFooterBg:h,calc:x}=e,C=`${W(d)} ${l} ${c}`;return{[`${t}-wrapper`]:Object.assign(Object.assign({clear:"both",maxWidth:"100%"},ol()),{[t]:Object.assign(Object.assign({},ft(e)),{fontSize:s,background:v,borderRadius:`${W(u)} ${W(u)} 0 0`,scrollbarColor:`${e.tableScrollThumbBg} ${e.tableScrollBg}`}),table:{width:"100%",textAlign:"start",borderRadius:`${W(u)} ${W(u)} 0 0`,borderCollapse:"separate",borderSpacing:0},[`
          ${t}-cell,
          ${t}-thead > tr > th,
          ${t}-tbody > tr > th,
          ${t}-tbody > tr > td,
          tfoot > tr > th,
          tfoot > tr > td
        `]:{position:"relative",padding:`${W(n)} ${W(o)}`,overflowWrap:"break-word"},[`${t}-title`]:{padding:`${W(n)} ${W(o)}`},[`${t}-thead`]:{"\n          > tr > th,\n          > tr > td\n        ":{position:"relative",color:p,fontWeight:r,textAlign:"start",background:g,borderBottom:C,transition:`background ${f} ease`,"&[colspan]:not([colspan='1'])":{textAlign:"center"},[`&:not(:last-child):not(${t}-selection-column):not(${t}-row-expand-icon-cell):not([colspan])::before`]:{position:"absolute",top:"50%",insetInlineEnd:0,width:1,height:"1.6em",backgroundColor:y,transform:"translateY(-50%)",transition:`background-color ${f}`,content:'""'}},"> tr:not(:last-child) > th[colspan]":{borderBottom:0}},[`${t}-tbody`]:{"> tr":{"> th, > td":{transition:`background ${f}, border-color ${f}`,borderBottom:C,[`
              > ${t}-wrapper:only-child,
              > ${t}-expanded-row-fixed > ${t}-wrapper:only-child
            `]:{[t]:{marginBlock:W(x(n).mul(-1).equal()),marginInline:`${W(x(i).sub(o).equal())}
                ${W(x(o).mul(-1).equal())}`,[`${t}-tbody > tr:last-child > td`]:{borderBottomWidth:0,"&:first-child, &:last-child":{borderRadius:0}}}}},"> th":{position:"relative",color:p,fontWeight:r,textAlign:"start",background:g,borderBottom:C,transition:`background ${f} ease`}}},[`${t}-footer`]:{padding:`${W(n)} ${W(o)}`,color:m,background:h}})}},ed=e=>{const{colorFillAlter:t,colorBgContainer:r,colorTextHeading:n,colorFillSecondary:o,colorFillContent:i,controlItemBgActive:d,controlItemBgActiveHover:l,padding:c,paddingSM:s,paddingXS:v,colorBorderSecondary:u,borderRadiusLG:p,controlHeight:f,colorTextPlaceholder:g,fontSize:y,fontSizeSM:m,lineHeight:h,lineWidth:x,colorIcon:C,colorIconHover:S,opacityLoading:E,controlInteractiveSize:$}=e,k=new jt(o).onBackground(r).toHexString(),R=new jt(i).onBackground(r).toHexString(),b=new jt(t).onBackground(r).toHexString(),T=new jt(C),D=new jt(S),P=$/2-x,O=P*2+x*3;return{headerBg:b,headerColor:n,headerSortActiveBg:k,headerSortHoverBg:R,bodySortBg:b,rowHoverBg:b,rowSelectedBg:d,rowSelectedHoverBg:l,rowExpandedBg:t,cellPaddingBlock:c,cellPaddingInline:c,cellPaddingBlockMD:s,cellPaddingInlineMD:v,cellPaddingBlockSM:v,cellPaddingInlineSM:v,borderColor:u,headerBorderRadius:p,footerBg:b,footerColor:n,cellFontSize:y,cellFontSizeMD:y,cellFontSizeSM:y,headerSplitColor:u,fixedHeaderSortActiveBg:k,headerFilterHoverBg:i,filterDropdownMenuBg:r,filterDropdownBg:r,expandIconBg:r,selectionColumnWidth:f,stickyScrollBarBg:g,stickyScrollBarBorderRadius:100,expandIconMarginTop:(y*h-x*3)/2-Math.ceil((m*1.4-x*3)/2),headerIconColor:T.clone().setA(T.a*E).toRgbString(),headerIconHoverColor:D.clone().setA(D.a*E).toRgbString(),expandIconHalfInner:P,expandIconSize:O,expandIconScale:$/O}},no=2,td=Xt("Table",e=>{const{colorTextHeading:t,colorSplit:r,colorBgContainer:n,controlInteractiveSize:o,headerBg:i,headerColor:d,headerSortActiveBg:l,headerSortHoverBg:c,bodySortBg:s,rowHoverBg:v,rowSelectedBg:u,rowSelectedHoverBg:p,rowExpandedBg:f,cellPaddingBlock:g,cellPaddingInline:y,cellPaddingBlockMD:m,cellPaddingInlineMD:h,cellPaddingBlockSM:x,cellPaddingInlineSM:C,borderColor:S,footerBg:E,footerColor:$,headerBorderRadius:k,cellFontSize:R,cellFontSizeMD:b,cellFontSizeSM:T,headerSplitColor:D,fixedHeaderSortActiveBg:P,headerFilterHoverBg:O,filterDropdownBg:N,expandIconBg:w,selectionColumnWidth:I,stickyScrollBarBg:K,calc:L}=e,M=sn(e,{tableFontSize:R,tableBg:n,tableRadius:k,tablePaddingVertical:g,tablePaddingHorizontal:y,tablePaddingVerticalMiddle:m,tablePaddingHorizontalMiddle:h,tablePaddingVerticalSmall:x,tablePaddingHorizontalSmall:C,tableBorderColor:S,tableHeaderTextColor:d,tableHeaderBg:i,tableFooterTextColor:$,tableFooterBg:E,tableHeaderCellSplitColor:D,tableHeaderSortBg:l,tableHeaderSortHoverBg:c,tableBodySortBg:s,tableFixedHeaderSortActiveBg:P,tableHeaderFilterActiveBg:O,tableFilterDropdownBg:N,tableRowHoverBg:v,tableSelectedRowBg:u,tableSelectedRowHoverBg:p,zIndexTableFixed:no,zIndexTableSticky:L(no).add(1).equal({unit:!1}),tableFontSizeMiddle:b,tableFontSizeSmall:T,tableSelectionColumnWidth:I,tableExpandIconBg:w,tableExpandColumnWidth:L(o).add(L(e.padding).mul(2)).equal(),tableExpandedRowBg:f,tableFilterDropdownWidth:120,tableFilterDropdownHeight:264,tableFilterDropdownSearchWidth:140,tableScrollThumbSize:8,tableScrollThumbBg:K,tableScrollThumbBgHover:t,tableScrollBg:r});return[Jc(M),Vc(M),to(M),Yc(M),Ac(M),Hc(M),qc(M),Fc(M),to(M),jc(M),Gc(M),Wc(M),Qc(M),zc(M),Uc(M),Xc(M),Zc(M)]},ed,{unitless:{expandIconScale:!0}}),nd=[],rd=(e,t)=>{var r,n;const{prefixCls:o,className:i,rootClassName:d,style:l,size:c,bordered:s,dropdownPrefixCls:v,dataSource:u,pagination:p,rowSelection:f,rowKey:g="key",rowClassName:y,columns:m,children:h,childrenColumnName:x,onChange:C,getPopupContainer:S,loading:E,expandIcon:$,expandable:k,expandedRowRender:R,expandIconColumnIndex:b,indentSize:T,scroll:D,sortDirections:P,locale:O,showSorterTooltip:N={target:"full-header"},virtual:w}=e;An();const I=a.useMemo(()=>m||Gn(h),[m,h]),K=a.useMemo(()=>I.some(le=>le.responsive),[I]),L=al(K),M=a.useMemo(()=>{const le=new Set(Object.keys(L).filter(ie=>L[ie]));return I.filter(ie=>!ie.responsive||ie.responsive.some(Ke=>le.has(Ke)))},[I,L]),V=dn(e,["className","style","columns"]),{locale:Q=fo,direction:U,table:te,renderEmpty:ye,getPrefixCls:Se,getPopupContainer:Ee}=a.useContext(rt),Z=so(c),J=Object.assign(Object.assign({},Q.Table),O),$e=u||nd,ge=Se("table",o),q=Se("dropdown",v),[,X]=go(),z=Mt(ge),[A,ee,oe]=td(ge,z),re=Object.assign(Object.assign({childrenColumnName:x,expandIconColumnIndex:b},k),{expandIcon:(r=k==null?void 0:k.expandIcon)!==null&&r!==void 0?r:(n=te==null?void 0:te.expandable)===null||n===void 0?void 0:n.expandIcon}),{childrenColumnName:Ne="children"}=re,je=a.useMemo(()=>$e.some(le=>le==null?void 0:le[Ne])?"nest":R||k!=null&&k.expandedRowRender?"row":null,[$e]),Oe={body:a.useRef(null)},_=Qs(ge),F=a.useRef(null),ne=a.useRef(null);Us(t,()=>Object.assign(Object.assign({},ne.current),{nativeElement:F.current}));const he=a.useMemo(()=>typeof g=="function"?g:le=>le==null?void 0:le[g],[g]),[de]=Ic($e,Ne,he),we={},Te=function(le,ie){let Ke=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;var Be,Ae,Je,et;const We=Object.assign(Object.assign({},we),le);Ke&&((Be=we.resetPagination)===null||Be===void 0||Be.call(we),!((Ae=We.pagination)===null||Ae===void 0)&&Ae.current&&(We.pagination.current=1),p&&((Je=p.onChange)===null||Je===void 0||Je.call(p,1,(et=We.pagination)===null||et===void 0?void 0:et.pageSize))),D&&D.scrollToFirstRowOnChange!==!1&&Oe.body.current&&pl(0,{getContainer:()=>Oe.body.current}),C==null||C(We.pagination,We.filters,We.sorter,{currentDataSource:_n(zn($e,We.sorterStates,Ne),We.filterStates,Ne),action:ie})},Le=(le,ie)=>{Te({sorter:le,sorterStates:ie},"sort",!1)},[be,ue,Y,H]=Mc({prefixCls:ge,mergedColumns:M,onSorterChange:Le,sortDirections:P||["ascend","descend"],tableLocale:J,showSorterTooltip:N}),ke=a.useMemo(()=>zn($e,ue,Ne),[$e,ue]);we.sorter=H(),we.sorterStates=ue;const Ie=(le,ie)=>{Te({filters:le,filterStates:ie},"filter",!0)},[ae,Re,Ce]=Oc({prefixCls:ge,locale:J,dropdownPrefixCls:q,mergedColumns:M,onFilterChange:Ie,getPopupContainer:S||Ee,rootClassName:G(d,z)}),De=_n(ke,Re,Ne);we.filters=Ce,we.filterStates=Re;const Ue=a.useMemo(()=>{const le={};return Object.keys(Ce).forEach(ie=>{Ce[ie]!==null&&(le[ie]=Ce[ie])}),Object.assign(Object.assign({},Y),{filters:le})},[Y,Ce]),[Ve]=Bc(Ue),vt=(le,ie)=>{Te({pagination:Object.assign(Object.assign({},we.pagination),{current:le,pageSize:ie})},"paginate")},[Pe,kt]=Tc(De.length,vt,p);we.pagination=p===!1?{}:Kc(Pe,p),we.resetPagination=kt;const Nt=a.useMemo(()=>{if(p===!1||!Pe.pageSize)return De;const{current:le=1,total:ie,pageSize:Ke=la}=Pe;return De.length<ie?De.length>Ke?De.slice((le-1)*Ke,le*Ke):De:De.slice((le-1)*Ke,le*Ke)},[!!p,De,Pe==null?void 0:Pe.current,Pe==null?void 0:Pe.pageSize,Pe==null?void 0:Pe.total]),[qe,at]=Xs({prefixCls:ge,data:De,pageData:Nt,getRowKey:he,getRecordByKey:de,expandType:je,childrenColumnName:Ne,locale:J,getPopupContainer:S||Ee},f),Ze=(le,ie,Ke)=>{let Be;return typeof y=="function"?Be=G(y(le,ie,Ke)):Be=G(y),G({[`${ge}-row-selected`]:at.has(he(le,ie))},Be)};re.__PARENT_RENDER_ICON__=re.expandIcon,re.expandIcon=re.expandIcon||$||Ys(J),je==="nest"&&re.expandIconColumnIndex===void 0?re.expandIconColumnIndex=f?1:0:re.expandIconColumnIndex>0&&f&&(re.expandIconColumnIndex-=1),typeof re.indentSize!="number"&&(re.indentSize=typeof T=="number"?T:15);const Xe=a.useCallback(le=>Ve(qe(ae(be(le)))),[be,ae,qe]);let Fe,Ye;if(p!==!1&&(Pe!=null&&Pe.total)){let le;Pe.size?le=Pe.size:le=Z==="small"||Z==="middle"?"small":void 0;const ie=Ae=>a.createElement(cl,Object.assign({},Pe,{className:G(`${ge}-pagination ${ge}-pagination-${Ae}`,Pe.className),size:le})),Ke=U==="rtl"?"left":"right",{position:Be}=Pe;if(Be!==null&&Array.isArray(Be)){const Ae=Be.find(We=>We.includes("top")),Je=Be.find(We=>We.includes("bottom")),et=Be.every(We=>`${We}`=="none");!Ae&&!Je&&!et&&(Ye=ie(Ke)),Ae&&(Fe=ie(Ae.toLowerCase().replace("top",""))),Je&&(Ye=ie(Je.toLowerCase().replace("bottom","")))}else Ye=ie(Ke)}let xt;typeof E=="boolean"?xt={spinning:E}:typeof E=="object"&&(xt=Object.assign({spinning:!0},E));const pt=G(oe,z,`${ge}-wrapper`,te==null?void 0:te.className,{[`${ge}-wrapper-rtl`]:U==="rtl"},i,d,ee),lt=Object.assign(Object.assign({},te==null?void 0:te.style),l),pn=typeof(O==null?void 0:O.emptyText)<"u"?O.emptyText:(ye==null?void 0:ye("Table"))||a.createElement(sl,{componentName:"Table"}),mn=w?_c:Lc,Zt={},gn=a.useMemo(()=>{const{fontSize:le,lineHeight:ie,lineWidth:Ke,padding:Be,paddingXS:Ae,paddingSM:Je}=X,et=Math.floor(le*ie);switch(Z){case"middle":return Je*2+et+Ke;case"small":return Ae*2+et+Ke;default:return Be*2+et+Ke}},[X,Z]);return w&&(Zt.listItemHeight=gn),A(a.createElement("div",{ref:F,className:pt,style:lt},a.createElement(ll,Object.assign({spinning:!1},xt),Fe,a.createElement(mn,Object.assign({},Zt,V,{ref:ne,columns:M,direction:U,expandable:re,prefixCls:ge,className:G({[`${ge}-middle`]:Z==="middle",[`${ge}-small`]:Z==="small",[`${ge}-bordered`]:s,[`${ge}-empty`]:$e.length===0},oe,z,ee),data:Nt,rowKey:he,rowClassName:Ze,emptyText:pn,internalHooks:Qt,internalRefs:Oe,transformColumns:Xe,getContainerWidth:_})),Ye)))},od=a.forwardRef(rd),ad=(e,t)=>{const r=a.useRef(0);return r.current+=1,a.createElement(od,Object.assign({},e,{ref:t,_renderTimes:r.current}))},Ct=a.forwardRef(ad);Ct.SELECTION_COLUMN=mt;Ct.EXPAND_COLUMN=ut;Ct.SELECTION_ALL=Tn;Ct.SELECTION_INVERT=Dn;Ct.SELECTION_NONE=Mn;Ct.Column=Bs;Ct.ColumnGroup=Ls;Ct.Summary=_o;export{Ct as F,ct as M,Ul as P,ud as R,dd as a};
