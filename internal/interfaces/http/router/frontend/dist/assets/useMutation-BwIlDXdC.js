var gt=i=>{throw TypeError(i)};var it=(i,t,s)=>t.has(i)||gt("Cannot "+s);var e=(i,t,s)=>(it(i,t,"read from private field"),s?s.call(i):t.get(i)),f=(i,t,s)=>t.has(i)?gt("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(i):t.set(i,s),o=(i,t,s,r)=>(it(i,t,"write to private field"),r?r.call(i,s):t.set(i,s),s),d=(i,t,s)=>(it(i,t,"access private method"),s);import{bB as Mt,bC as Rt,bD as Q,bE as Z,bF as X,bG as Pt,bH as rt,bI as Ct,bJ as Lt,bK as _t,bL as kt,bM as Ot,bN as tt,bO as St,bP as Bt,r as S,K as Tt}from"./index-P7H5iTop.js";var C,a,G,g,P,K,T,I,J,j,A,L,_,U,H,u,q,nt,at,ht,ot,ut,ct,lt,Ut,Qt,Kt=(Qt=class extends Mt{constructor(t,s){super();f(this,u);f(this,C);f(this,a);f(this,G);f(this,g);f(this,P);f(this,K);f(this,T);f(this,I);f(this,J);f(this,j);f(this,A);f(this,L);f(this,_);f(this,U);f(this,H,new Set);this.options=s,o(this,C,t),o(this,I,null),o(this,T,Rt()),this.options.experimental_prefetchInRender||e(this,T).reject(new Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(s)}bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){this.listeners.size===1&&(e(this,a).addObserver(this),Et(e(this,a),this.options)?d(this,u,q).call(this):this.updateResult(),d(this,u,ot).call(this))}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return dt(e(this,a),this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return dt(e(this,a),this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,d(this,u,ut).call(this),d(this,u,ct).call(this),e(this,a).removeObserver(this)}setOptions(t,s){const r=this.options,c=e(this,a);if(this.options=e(this,C).defaultQueryOptions(t),this.options.enabled!==void 0&&typeof this.options.enabled!="boolean"&&typeof this.options.enabled!="function"&&typeof Q(this.options.enabled,e(this,a))!="boolean")throw new Error("Expected enabled to be a boolean or a callback that returns a boolean");d(this,u,lt).call(this),e(this,a).setOptions(this.options),r._defaulted&&!Z(this.options,r)&&e(this,C).getQueryCache().notify({type:"observerOptionsUpdated",query:e(this,a),observer:this});const l=this.hasListeners();l&&xt(e(this,a),c,this.options,r)&&d(this,u,q).call(this),this.updateResult(s),l&&(e(this,a)!==c||Q(this.options.enabled,e(this,a))!==Q(r.enabled,e(this,a))||X(this.options.staleTime,e(this,a))!==X(r.staleTime,e(this,a)))&&d(this,u,nt).call(this);const n=d(this,u,at).call(this);l&&(e(this,a)!==c||Q(this.options.enabled,e(this,a))!==Q(r.enabled,e(this,a))||n!==e(this,U))&&d(this,u,ht).call(this,n)}getOptimisticResult(t){const s=e(this,C).getQueryCache().build(e(this,C),t),r=this.createResult(s,t);return At(this,r)&&(o(this,g,r),o(this,K,this.options),o(this,P,e(this,a).state)),r}getCurrentResult(){return e(this,g)}trackResult(t,s){const r={};return Object.keys(t).forEach(c=>{Object.defineProperty(r,c,{configurable:!1,enumerable:!0,get:()=>(this.trackProp(c),s==null||s(c),t[c])})}),r}trackProp(t){e(this,H).add(t)}getCurrentQuery(){return e(this,a)}refetch({...t}={}){return this.fetch({...t})}fetchOptimistic(t){const s=e(this,C).defaultQueryOptions(t),r=e(this,C).getQueryCache().build(e(this,C),s);return r.fetch().then(()=>this.createResult(r,s))}fetch(t){return d(this,u,q).call(this,{...t,cancelRefetch:t.cancelRefetch??!0}).then(()=>(this.updateResult(),e(this,g)))}createResult(t,s){var yt;const r=e(this,a),c=this.options,l=e(this,g),n=e(this,P),R=e(this,K),v=t!==r?t.state:e(this,G),{state:b}=t;let h={...b},N=!1,m;if(s._optimisticResults){const y=this.hasListeners(),k=!y&&Et(t,s),B=y&&xt(t,r,s,c);(k||B)&&(h={...h,...kt(b.data,t.options)}),s._optimisticResults==="isRestoring"&&(h.fetchStatus="idle")}let{error:z,errorUpdatedAt:V,status:E}=h;if(s.select&&h.data!==void 0)if(l&&h.data===(n==null?void 0:n.data)&&s.select===e(this,J))m=e(this,j);else try{o(this,J,s.select),m=s.select(h.data),m=Ot(l==null?void 0:l.data,m,s),o(this,j,m),o(this,I,null)}catch(y){o(this,I,y)}else m=h.data;if(s.placeholderData!==void 0&&m===void 0&&E==="pending"){let y;if(l!=null&&l.isPlaceholderData&&s.placeholderData===(R==null?void 0:R.placeholderData))y=l.data;else if(y=typeof s.placeholderData=="function"?s.placeholderData((yt=e(this,A))==null?void 0:yt.state.data,e(this,A)):s.placeholderData,s.select&&y!==void 0)try{y=s.select(y),o(this,I,null)}catch(k){o(this,I,k)}y!==void 0&&(E="success",m=Ot(l==null?void 0:l.data,y,s),N=!0)}e(this,I)&&(z=e(this,I),m=e(this,j),V=Date.now(),E="error");const W=h.fetchStatus==="fetching",et=E==="pending",st=E==="error",mt=et&&W,vt=m!==void 0,x={status:E,fetchStatus:h.fetchStatus,isPending:et,isSuccess:E==="success",isError:st,isInitialLoading:mt,isLoading:mt,data:m,dataUpdatedAt:h.dataUpdatedAt,error:z,errorUpdatedAt:V,failureCount:h.fetchFailureCount,failureReason:h.fetchFailureReason,errorUpdateCount:h.errorUpdateCount,isFetched:h.dataUpdateCount>0||h.errorUpdateCount>0,isFetchedAfterMount:h.dataUpdateCount>v.dataUpdateCount||h.errorUpdateCount>v.errorUpdateCount,isFetching:W,isRefetching:W&&!et,isLoadingError:st&&!vt,isPaused:h.fetchStatus==="paused",isPlaceholderData:N,isRefetchError:st&&vt,isStale:bt(t,s),refetch:this.refetch,promise:e(this,T)};if(this.options.experimental_prefetchInRender){const y=$=>{x.status==="error"?$.reject(x.error):x.data!==void 0&&$.resolve(x.data)},k=()=>{const $=o(this,T,x.promise=Rt());y($)},B=e(this,T);switch(B.status){case"pending":t.queryHash===r.queryHash&&y(B);break;case"fulfilled":(x.status==="error"||x.data!==B.value)&&k();break;case"rejected":(x.status!=="error"||x.error!==B.reason)&&k();break}}return x}updateResult(t){const s=e(this,g),r=this.createResult(e(this,a),this.options);if(o(this,P,e(this,a).state),o(this,K,this.options),e(this,P).data!==void 0&&o(this,A,e(this,a)),Z(r,s))return;o(this,g,r);const c={},l=()=>{if(!s)return!0;const{notifyOnChangeProps:n}=this.options,R=typeof n=="function"?n():n;if(R==="all"||!R&&!e(this,H).size)return!0;const p=new Set(R??e(this,H));return this.options.throwOnError&&p.add("error"),Object.keys(e(this,g)).some(v=>{const b=v;return e(this,g)[b]!==s[b]&&p.has(b)})};(t==null?void 0:t.listeners)!==!1&&l()&&(c.listeners=!0),d(this,u,Ut).call(this,{...c,...t})}onQueryUpdate(){this.updateResult(),this.hasListeners()&&d(this,u,ot).call(this)}},C=new WeakMap,a=new WeakMap,G=new WeakMap,g=new WeakMap,P=new WeakMap,K=new WeakMap,T=new WeakMap,I=new WeakMap,J=new WeakMap,j=new WeakMap,A=new WeakMap,L=new WeakMap,_=new WeakMap,U=new WeakMap,H=new WeakMap,u=new WeakSet,q=function(t){d(this,u,lt).call(this);let s=e(this,a).fetch(this.options,t);return t!=null&&t.throwOnError||(s=s.catch(Pt)),s},nt=function(){d(this,u,ut).call(this);const t=X(this.options.staleTime,e(this,a));if(rt||e(this,g).isStale||!Ct(t))return;const r=Lt(e(this,g).dataUpdatedAt,t)+1;o(this,L,setTimeout(()=>{e(this,g).isStale||this.updateResult()},r))},at=function(){return(typeof this.options.refetchInterval=="function"?this.options.refetchInterval(e(this,a)):this.options.refetchInterval)??!1},ht=function(t){d(this,u,ct).call(this),o(this,U,t),!(rt||Q(this.options.enabled,e(this,a))===!1||!Ct(e(this,U))||e(this,U)===0)&&o(this,_,setInterval(()=>{(this.options.refetchIntervalInBackground||_t.isFocused())&&d(this,u,q).call(this)},e(this,U)))},ot=function(){d(this,u,nt).call(this),d(this,u,ht).call(this,d(this,u,at).call(this))},ut=function(){e(this,L)&&(clearTimeout(e(this,L)),o(this,L,void 0))},ct=function(){e(this,_)&&(clearInterval(e(this,_)),o(this,_,void 0))},lt=function(){const t=e(this,C).getQueryCache().build(e(this,C),this.options);if(t===e(this,a))return;const s=e(this,a);o(this,a,t),o(this,G,t.state),this.hasListeners()&&(s==null||s.removeObserver(this),t.addObserver(this))},Ut=function(t){tt.batch(()=>{t.listeners&&this.listeners.forEach(s=>{s(e(this,g))}),e(this,C).getQueryCache().notify({query:e(this,a),type:"observerResultsUpdated"})})},Qt);function jt(i,t){return Q(t.enabled,i)!==!1&&i.state.data===void 0&&!(i.state.status==="error"&&t.retryOnMount===!1)}function Et(i,t){return jt(i,t)||i.state.data!==void 0&&dt(i,t,t.refetchOnMount)}function dt(i,t,s){if(Q(t.enabled,i)!==!1){const r=typeof s=="function"?s(i):s;return r==="always"||r!==!1&&bt(i,t)}return!1}function xt(i,t,s,r){return(i!==t||Q(r.enabled,i)===!1)&&(!s.suspense||i.state.status!=="error")&&bt(i,s)}function bt(i,t){return Q(t.enabled,i)!==!1&&i.isStaleByTime(X(t.staleTime,i))}function At(i,t){return!Z(i.getCurrentResult(),t)}var F,D,O,w,M,Y,ft,wt,Ht=(wt=class extends Mt{constructor(t,s){super();f(this,M);f(this,F);f(this,D);f(this,O);f(this,w);o(this,F,t),this.setOptions(s),this.bindMethods(),d(this,M,Y).call(this)}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(t){var r;const s=this.options;this.options=e(this,F).defaultMutationOptions(t),Z(this.options,s)||e(this,F).getMutationCache().notify({type:"observerOptionsUpdated",mutation:e(this,O),observer:this}),s!=null&&s.mutationKey&&this.options.mutationKey&&St(s.mutationKey)!==St(this.options.mutationKey)?this.reset():((r=e(this,O))==null?void 0:r.state.status)==="pending"&&e(this,O).setOptions(this.options)}onUnsubscribe(){var t;this.hasListeners()||(t=e(this,O))==null||t.removeObserver(this)}onMutationUpdate(t){d(this,M,Y).call(this),d(this,M,ft).call(this,t)}getCurrentResult(){return e(this,D)}reset(){var t;(t=e(this,O))==null||t.removeObserver(this),o(this,O,void 0),d(this,M,Y).call(this),d(this,M,ft).call(this)}mutate(t,s){var r;return o(this,w,s),(r=e(this,O))==null||r.removeObserver(this),o(this,O,e(this,F).getMutationCache().build(e(this,F),this.options)),e(this,O).addObserver(this),e(this,O).execute(t)}},F=new WeakMap,D=new WeakMap,O=new WeakMap,w=new WeakMap,M=new WeakSet,Y=function(){var s;const t=((s=e(this,O))==null?void 0:s.state)??Bt();o(this,D,{...t,isPending:t.status==="pending",isSuccess:t.status==="success",isError:t.status==="error",isIdle:t.status==="idle",mutate:this.mutate,reset:this.reset})},ft=function(t){tt.batch(()=>{var s,r,c,l,n,R,p,v;if(e(this,w)&&this.hasListeners()){const b=e(this,D).variables,h=e(this,D).context;(t==null?void 0:t.type)==="success"?((r=(s=e(this,w)).onSuccess)==null||r.call(s,t.data,b,h),(l=(c=e(this,w)).onSettled)==null||l.call(c,t.data,null,b,h)):(t==null?void 0:t.type)==="error"&&((R=(n=e(this,w)).onError)==null||R.call(n,t.error,b,h),(v=(p=e(this,w)).onSettled)==null||v.call(p,void 0,t.error,b,h))}this.listeners.forEach(b=>{b(e(this,D))})})},wt),Ft=S.createContext(!1),Nt=()=>S.useContext(Ft);Ft.Provider;function zt(){let i=!1;return{clearReset:()=>{i=!1},reset:()=>{i=!0},isReset:()=>i}}var Vt=S.createContext(zt()),Wt=()=>S.useContext(Vt);function Dt(i,t){return typeof i=="function"?i(...t):!!i}function pt(){}var qt=(i,t)=>{(i.suspense||i.throwOnError||i.experimental_prefetchInRender)&&(t.isReset()||(i.retryOnMount=!1))},Gt=i=>{S.useEffect(()=>{i.clearReset()},[i])},Jt=({result:i,errorResetBoundary:t,throwOnError:s,query:r,suspense:c})=>i.isError&&!t.isReset()&&!i.isFetching&&r&&(c&&i.data===void 0||Dt(s,[i.error,r])),$t=i=>{const t=i.staleTime;i.suspense&&(i.staleTime=typeof t=="function"?(...s)=>Math.max(t(...s),1e3):Math.max(t??1e3,1e3),typeof i.gcTime=="number"&&(i.gcTime=Math.max(i.gcTime,1e3)))},Xt=(i,t)=>i.isLoading&&i.isFetching&&!t,Yt=(i,t)=>(i==null?void 0:i.suspense)&&t.isPending,It=(i,t,s)=>t.fetchOptimistic(i).catch(()=>{s.clearReset()});function Zt(i,t,s){var h,N,m,z,V;const r=Tt(),c=Nt(),l=Wt(),n=r.defaultQueryOptions(i);(N=(h=r.getDefaultOptions().queries)==null?void 0:h._experimental_beforeQuery)==null||N.call(h,n),n._optimisticResults=c?"isRestoring":"optimistic",$t(n),qt(n,l),Gt(l);const R=!r.getQueryCache().get(n.queryHash),[p]=S.useState(()=>new t(r,n)),v=p.getOptimisticResult(n),b=!c&&i.subscribed!==!1;if(S.useSyncExternalStore(S.useCallback(E=>{const W=b?p.subscribe(tt.batchCalls(E)):pt;return p.updateResult(),W},[p,b]),()=>p.getCurrentResult(),()=>p.getCurrentResult()),S.useEffect(()=>{p.setOptions(n,{listeners:!1})},[n,p]),Yt(n,v))throw It(n,p,l);if(Jt({result:v,errorResetBoundary:l,throwOnError:n.throwOnError,query:r.getQueryCache().get(n.queryHash),suspense:n.suspense}))throw v.error;if((z=(m=r.getDefaultOptions().queries)==null?void 0:m._experimental_afterQuery)==null||z.call(m,n,v),n.experimental_prefetchInRender&&!rt&&Xt(v,c)){const E=R?It(n,p,l):(V=r.getQueryCache().get(n.queryHash))==null?void 0:V.promise;E==null||E.catch(pt).finally(()=>{p.updateResult()})}return n.notifyOnChangeProps?v:p.trackResult(v)}function ie(i,t){return Zt(i,Kt)}function re(i,t){const s=Tt(),[r]=S.useState(()=>new Ht(s,i));S.useEffect(()=>{r.setOptions(i)},[r,i]);const c=S.useSyncExternalStore(S.useCallback(n=>r.subscribe(tt.batchCalls(n)),[r]),()=>r.getCurrentResult(),()=>r.getCurrentResult()),l=S.useCallback((n,R)=>{r.mutate(n,R).catch(pt)},[r]);if(c.error&&Dt(r.options.throwOnError,[c.error]))throw c.error;return{...c,mutate:l,mutateAsync:c.mutate}}export{re as a,ie as u};
