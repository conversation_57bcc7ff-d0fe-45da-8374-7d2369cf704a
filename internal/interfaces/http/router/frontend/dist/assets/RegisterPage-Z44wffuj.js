import{r as j,u as g,b as y,j as e,S as f,T as R,R as I,B as o}from"./index-P7H5iTop.js";import{m as b,C as w}from"./proxy-OhLL-xTq.js";import{F as r,I as i,R as l}from"./index-C73uzCkN.js";import{R as v}from"./MailOutlined-DGjgqFD8.js";import{R as d,a as c}from"./LockOutlined-DkqxqBe7.js";import{D as S}from"./index-BJejOudn.js";const{Title:P,Text:m}=R;function E(){const[x,t]=j.useState(!1),a=g(),{register:p}=y(),u=async s=>{try{t(!0),await p(s),a("/login")}catch{}finally{t(!1)}};return e.jsx("div",{style:{minHeight:"100vh",display:"flex",alignItems:"center",justifyContent:"center",background:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",padding:"20px"},children:e.jsx(b.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},children:e.jsx(w,{style:{width:400,boxShadow:"0 8px 32px rgba(0, 0, 0, 0.1)",borderRadius:16},bodyStyle:{padding:"40px"},children:e.jsxs(f,{direction:"vertical",size:"large",style:{width:"100%"},children:[e.jsxs("div",{style:{textAlign:"center"},children:[e.jsx(P,{level:2,style:{marginBottom:8},children:"创建账户"}),e.jsx(m,{type:"secondary",children:"注册新的账户"})]}),e.jsxs(r,{name:"register",onFinish:u,autoComplete:"off",size:"large",children:[e.jsx(r.Item,{name:"username",rules:[{required:!0,message:"请输入用户名"},{min:2,message:"用户名至少2位字符"}],children:e.jsx(i,{prefix:e.jsx(I,{}),placeholder:"用户名"})}),e.jsx(r.Item,{name:"email",rules:[{required:!0,message:"请输入邮箱地址"},{type:"email",message:"请输入有效的邮箱地址"}],children:e.jsx(i,{prefix:e.jsx(v,{}),placeholder:"邮箱地址"})}),e.jsx(r.Item,{name:"password",rules:[{required:!0,message:"请输入密码"},{min:6,message:"密码至少6位字符"}],children:e.jsx(i.Password,{prefix:e.jsx(c,{}),placeholder:"密码",iconRender:s=>s?e.jsx(d,{}):e.jsx(l,{})})}),e.jsx(r.Item,{name:"confirmPassword",dependencies:["password"],rules:[{required:!0,message:"请确认密码"},({getFieldValue:s})=>({validator(h,n){return!n||s("password")===n?Promise.resolve():Promise.reject(new Error("两次输入的密码不一致"))}})],children:e.jsx(i.Password,{prefix:e.jsx(c,{}),placeholder:"确认密码",iconRender:s=>s?e.jsx(d,{}):e.jsx(l,{})})}),e.jsx(r.Item,{children:e.jsx(o,{type:"primary",htmlType:"submit",loading:x,block:!0,style:{height:48},children:"注册"})})]}),e.jsx(S,{plain:!0,children:e.jsx(m,{type:"secondary",children:"已有账户？"})}),e.jsx(o,{type:"default",block:!0,style:{height:48},onClick:()=>a("/login"),children:"立即登录"})]})})})})}export{E as default};
