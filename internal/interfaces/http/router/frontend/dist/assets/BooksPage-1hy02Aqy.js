import{K as B,L as d,M as c,r as m,j as e,S as y,T as z,v as A,B as o,J as D}from"./index-P7H5iTop.js";import{u as L,a as C}from"./useMutation-BwIlDXdC.js";import{F as u,I as a}from"./index-C73uzCkN.js";import{m as V,C as F,c as _,R as J,a as k,b as O}from"./proxy-OhLL-xTq.js";import{R as U,F as G,M as H,P as W,a as X}from"./Table-bE3bupkb.js";import"./Pagination-DPfaT0Az.js";const j={getBooks:async t=>c.get("/books",{params:t}),getBook:async t=>c.get(`/books/${t}`),getBookByISBN:async t=>c.get(`/books/isbn/${t}`),createBook:async t=>c.post("/books",t),updateBook:async(t,r)=>c.put(`/books/${t}`,r),deleteBook:async t=>c.delete(`/books/${t}`)},Y=t=>L({queryKey:["books",t],queryFn:()=>j.getBooks(t),staleTime:5*60*1e3}),Z=()=>{const t=B();return C({mutationFn:j.createBook,onSuccess:()=>{t.invalidateQueries({queryKey:["books"]}),d.success("图书创建成功")},onError:()=>{d.error("创建失败，请重试")}})},ee=()=>{const t=B();return C({mutationFn:({id:r,data:i})=>j.updateBook(r,i),onSuccess:(r,i)=>{t.setQueryData(["book",i.id],r),t.invalidateQueries({queryKey:["books"]}),d.success("图书更新成功")},onError:()=>{d.error("更新失败，请重试")}})},te=()=>{const t=B();return C({mutationFn:j.deleteBook,onSuccess:()=>{t.invalidateQueries({queryKey:["books"]}),d.success("图书删除成功")},onError:()=>{d.error("删除失败，请重试")}})},{Title:se}=z,{Search:ce}=a;function ue(){const[t,r]=m.useState({title:"",author:"",isbn:""}),[i,g]=m.useState(1),[b,$]=m.useState(10),[q,h]=m.useState(!1),[p,f]=m.useState(null),[x]=u.useForm(),{data:l,isLoading:I,refetch:S}=Y({page:i,pageSize:b,...t}),w=Z(),v=ee(),R=te(),P=[{title:"ID",dataIndex:"id",key:"id",width:80},{title:"书名",dataIndex:"title",key:"title",ellipsis:!0},{title:"作者",dataIndex:"author",key:"author",ellipsis:!0},{title:"ISBN",dataIndex:"isbn",key:"isbn",width:150},{title:"创建时间",dataIndex:"created_at",key:"created_at",render:s=>s?new Date(s).toLocaleDateString():"-"},{title:"操作",key:"actions",width:150,render:(s,n)=>e.jsxs(y,{children:[e.jsx(o,{type:"link",icon:e.jsx(D,{}),onClick:()=>Q(n),children:"编辑"}),e.jsx(W,{title:"确定删除这本图书吗？",onConfirm:()=>K(n.id),okText:"确定",cancelText:"取消",children:e.jsx(o,{type:"link",danger:!0,icon:e.jsx(X,{}),children:"删除"})})]})}],T=()=>{g(1),S()},M=()=>{r({title:"",author:"",isbn:""}),g(1)},E=()=>{f(null),x.resetFields(),h(!0)},Q=s=>{f(s),x.setFieldsValue({title:s.title,author:s.author,isbn:s.isbn}),h(!0)},K=s=>{R.mutate(s)},N=async s=>{try{p?await v.mutateAsync({id:p.id,data:s}):await w.mutateAsync(s),h(!1),x.resetFields()}catch{}};return e.jsxs(V.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},children:[e.jsx(F,{children:e.jsxs(y,{direction:"vertical",size:"large",style:{width:"100%"},children:[e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[e.jsxs(se,{level:3,style:{margin:0},children:[e.jsx(A,{})," 图书管理"]}),e.jsxs(y,{children:[e.jsx(o,{icon:e.jsx(U,{}),onClick:()=>S(),loading:I,children:"刷新"}),e.jsx(o,{type:"primary",icon:e.jsx(_,{}),onClick:E,children:"新建图书"})]})]}),e.jsxs(F,{size:"small",title:"搜索条件",children:[e.jsxs(J,{gutter:[16,16],children:[e.jsx(k,{xs:24,sm:8,children:e.jsx(a,{placeholder:"搜索书名",value:t.title,onChange:s=>r(n=>({...n,title:s.target.value})),allowClear:!0})}),e.jsx(k,{xs:24,sm:8,children:e.jsx(a,{placeholder:"搜索作者",value:t.author,onChange:s=>r(n=>({...n,author:s.target.value})),allowClear:!0})}),e.jsx(k,{xs:24,sm:8,children:e.jsx(a,{placeholder:"搜索ISBN",value:t.isbn,onChange:s=>r(n=>({...n,isbn:s.target.value})),allowClear:!0})})]}),e.jsx("div",{style:{marginTop:16,textAlign:"right"},children:e.jsxs(y,{children:[e.jsx(o,{onClick:M,children:"重置"}),e.jsx(o,{type:"primary",icon:e.jsx(O,{}),onClick:T,children:"搜索"})]})})]}),e.jsx(G,{columns:P,dataSource:(l==null?void 0:l.items)||[],rowKey:"id",loading:I,pagination:{current:i,pageSize:b,total:(l==null?void 0:l.total)||0,showSizeChanger:!0,showQuickJumper:!0,showTotal:(s,n)=>`第 ${n[0]}-${n[1]} 条，共 ${s} 条`,onChange:(s,n)=>{g(s),$(n||10)}}})]})}),e.jsx(H,{title:p?"编辑图书":"新建图书",open:q,onCancel:()=>{h(!1),x.resetFields()},footer:null,destroyOnClose:!0,children:e.jsxs(u,{form:x,layout:"vertical",onFinish:N,children:[e.jsx(u.Item,{name:"title",label:"书名",rules:[{required:!0,message:"请输入书名"}],children:e.jsx(a,{placeholder:"请输入书名"})}),e.jsx(u.Item,{name:"author",label:"作者",rules:[{required:!0,message:"请输入作者"}],children:e.jsx(a,{placeholder:"请输入作者"})}),e.jsx(u.Item,{name:"isbn",label:"ISBN",rules:[{required:!0,message:"请输入ISBN"}],children:e.jsx(a,{placeholder:"请输入ISBN"})}),e.jsx(u.Item,{style:{marginBottom:0,textAlign:"right"},children:e.jsxs(y,{children:[e.jsx(o,{onClick:()=>h(!1),children:"取消"}),e.jsx(o,{type:"primary",htmlType:"submit",loading:w.isPending||v.isPending,children:p?"更新":"创建"})]})})]})})]})}export{ue as default};
