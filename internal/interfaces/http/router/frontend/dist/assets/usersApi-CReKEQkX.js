import{u as c,a as n}from"./useMutation-BwIlDXdC.js";import{K as a,L as r,M as t}from"./index-P7H5iTop.js";const u={getUsers:async e=>t.get("/users",{params:e}),getUser:async e=>t.get(`/users/${e}`),getSelfProfile:async()=>t.get("/users/me"),updateSelfProfile:async e=>t.put("/users/me",e),createUser:async e=>t.post("/users",e),updateUser:async(e,s)=>t.put(`/users/${e}`,s),deleteUser:async e=>t.delete(`/users/${e}`)},l=e=>c({queryKey:["users",e],queryFn:()=>u.getUsers(e),staleTime:5*60*1e3}),d=()=>c({queryKey:["user","me"],queryFn:u.getSelfProfile}),U=()=>{const e=a();return n({mutationFn:u.updateSelfProfile,onSuccess:s=>{e.setQueryData(["user","me"],s),r.success("个人资料更新成功")},onError:()=>{r.error("更新失败，请重试")}})},f=()=>{const e=a();return n({mutationFn:u.createUser,onSuccess:()=>{e.invalidateQueries({queryKey:["users"]}),r.success("用户创建成功")},onError:()=>{r.error("创建失败，请重试")}})},p=()=>{const e=a();return n({mutationFn:({id:s,data:o})=>u.updateUser(s,o),onSuccess:(s,o)=>{e.setQueryData(["user",o.id],s),e.invalidateQueries({queryKey:["users"]}),r.success("用户更新成功")},onError:()=>{r.error("更新失败，请重试")}})},m=()=>{const e=a();return n({mutationFn:u.deleteUser,onSuccess:()=>{e.invalidateQueries({queryKey:["users"]}),r.success("用户删除成功")},onError:()=>{r.error("删除失败，请重试")}})};export{f as a,p as b,m as c,d,U as e,l as u};
