import{r as h,I as f,_ as y,b as g,j as e,S as c,A as v,R as d,T as z,B as I}from"./index-P7H5iTop.js";import{d as S,e as b}from"./usersApi-CReKEQkX.js";import{F as s,I as m}from"./index-C73uzCkN.js";import{m as R,C as V}from"./proxy-OhLL-xTq.js";import{D as x}from"./index-BJejOudn.js";import{R as A}from"./MailOutlined-DGjgqFD8.js";import"./useMutation-BwIlDXdC.js";var D={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M893.3 293.3L730.7 130.7c-7.5-7.5-16.7-13-26.7-16V112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V338.5c0-17-6.7-33.2-18.7-45.2zM384 184h256v104H384V184zm456 656H184V184h136v136c0 17.7 14.3 32 32 32h320c17.7 0 32-14.3 32-32V205.8l136 136V840zM512 442c-79.5 0-144 64.5-144 144s64.5 144 144 144 144-64.5 144-144-64.5-144-144-144zm0 224c-44.2 0-80-35.8-80-80s35.8-80 80-80 80 35.8 80 80-35.8 80-80 80z"}}]},name:"save",theme:"outlined"},P=function(i,r){return h.createElement(f,y({},i,{ref:r,icon:D}))},T=h.forwardRef(P);const{Title:w,Text:t}=z;function $(){const[l]=s.useForm(),{user:i,updateUser:r}=g(),{data:p,isLoading:B}=S(),n=b(),a=p||i,u=async j=>{try{const o=await n.mutateAsync(j);r(o)}catch{}};return e.jsx(R.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},children:e.jsx(V,{style:{maxWidth:600,margin:"0 auto"},children:e.jsxs(c,{direction:"vertical",size:"large",style:{width:"100%"},children:[e.jsxs("div",{style:{textAlign:"center"},children:[e.jsx(v,{size:80,icon:e.jsx(d,{})}),e.jsx(w,{level:3,style:{marginTop:16,marginBottom:8},children:"个人资料"}),e.jsx(t,{type:"secondary",children:"管理您的个人信息"})]}),e.jsx(x,{}),e.jsxs(s,{form:l,layout:"vertical",initialValues:{username:a==null?void 0:a.username,email:a==null?void 0:a.email},onFinish:u,children:[e.jsx(s.Item,{name:"username",label:"用户名",rules:[{required:!0,message:"请输入用户名"},{min:2,message:"用户名至少2位字符"}],children:e.jsx(m,{prefix:e.jsx(d,{}),placeholder:"请输入用户名",size:"large"})}),e.jsx(s.Item,{name:"email",label:"邮箱地址",rules:[{required:!0,message:"请输入邮箱地址"},{type:"email",message:"请输入有效的邮箱地址"}],children:e.jsx(m,{prefix:e.jsx(A,{}),placeholder:"请输入邮箱地址",size:"large"})}),e.jsx(s.Item,{style:{marginBottom:0},children:e.jsx(I,{type:"primary",htmlType:"submit",loading:n.isPending,icon:e.jsx(T,{}),size:"large",block:!0,children:"保存更改"})})]}),e.jsx(x,{}),e.jsx("div",{style:{textAlign:"center"},children:e.jsxs(c,{direction:"vertical",size:"small",children:[e.jsx(t,{type:"secondary",children:"账户信息"}),e.jsxs(t,{children:["用户ID: ",a==null?void 0:a.id]}),e.jsxs(t,{children:["角色: ",(a==null?void 0:a.role)||"user"]}),(a==null?void 0:a.created_at)&&e.jsxs(t,{children:["注册时间: ",new Date(a.created_at).toLocaleDateString()]})]})})]})})})}export{$ as default};
