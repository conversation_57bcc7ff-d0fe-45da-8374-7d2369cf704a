import{r as h,I as xs,_ as Ct,Q as z,c as Aa,N as Ui,V as vt,ci as Ra,a3 as Kn,cj as Ea,ck as Ma,U as ct,d as k,cl as Da,aR as It,ax as Va,l as Gi,aC as pn,b7 as La,O as We,aM as _a,b6 as Oa,P as Ki,bo as Ia,a5 as Ba,b9 as Hi,g as Ts,m as Ps,f as $,h as Hn,a$ as Xn,X as Cs,bs as Rn,C as le,$ as ja,bj as Na,k as ws,cm as Fa,b1 as Je,bl as za,y as ka,s as Wa,cn as Ua,o as Ue,n as Ga,co as Ka,j as En}from"./index-P7H5iTop.js";var Ha={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"}}]},name:"search",theme:"outlined"},Xa=function(e,n){return h.createElement(xs,Ct({},e,{ref:n,icon:Ha}))},$h=h.forwardRef(Xa),qa={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M482 152h60q8 0 8 8v704q0 8-8 8h-60q-8 0-8-8V160q0-8 8-8z"}},{tag:"path",attrs:{d:"M192 474h672q8 0 8 8v60q0 8-8 8H160q-8 0-8-8v-60q0-8 8-8z"}}]},name:"plus",theme:"outlined"},Ya=function(e,n){return h.createElement(xs,Ct({},e,{ref:n,icon:qa}))},Za=h.forwardRef(Ya);const Qe=h.createContext(null);var Ja=function(e){var n=e.activeTabOffset,i=e.horizontal,r=e.rtl,o=e.indicator,s=o===void 0?{}:o,a=s.size,l=s.align,c=l===void 0?"center":l,u=h.useState(),d=z(u,2),f=d[0],m=d[1],v=h.useRef(),g=Aa.useCallback(function(p){return typeof a=="function"?a(p):typeof a=="number"?a:p},[a]);function y(){Ui.cancel(v.current)}return h.useEffect(function(){var p={};if(n)if(i){p.width=g(n.width);var b=r?"right":"left";c==="start"&&(p[b]=n[b]),c==="center"&&(p[b]=n[b]+n.width/2,p.transform=r?"translateX(50%)":"translateX(-50%)"),c==="end"&&(p[b]=n[b]+n.width,p.transform="translateX(-100%)")}else p.height=g(n.height),c==="start"&&(p.top=n.top),c==="center"&&(p.top=n.top+n.height/2,p.transform="translateY(-50%)"),c==="end"&&(p.top=n.top+n.height,p.transform="translateY(-100%)");return y(),v.current=Ui(function(){m(p)}),y},[n,i,r,c,g]),{style:f}},Xi={width:0,height:0,left:0,top:0};function Qa(t,e,n){return h.useMemo(function(){for(var i,r=new Map,o=e.get((i=t[0])===null||i===void 0?void 0:i.key)||Xi,s=o.left+o.width,a=0;a<t.length;a+=1){var l=t[a].key,c=e.get(l);if(!c){var u;c=e.get((u=t[a-1])===null||u===void 0?void 0:u.key)||Xi}var d=r.get(l)||vt({},c);d.right=s-d.left-d.width,r.set(l,d)}return r},[t.map(function(i){return i.key}).join("_"),e,n])}function qi(t,e){var n=h.useRef(t),i=h.useState({}),r=z(i,2),o=r[1];function s(a){var l=typeof a=="function"?a(n.current):a;l!==n.current&&e(l,n.current),n.current=l,o({})}return[n.current,s]}var tl=.1,Yi=.01,Ne=20,Zi=Math.pow(.995,Ne);function el(t,e){var n=h.useState(),i=z(n,2),r=i[0],o=i[1],s=h.useState(0),a=z(s,2),l=a[0],c=a[1],u=h.useState(0),d=z(u,2),f=d[0],m=d[1],v=h.useState(),g=z(v,2),y=g[0],p=g[1],b=h.useRef();function S(P){var C=P.touches[0],E=C.screenX,O=C.screenY;o({x:E,y:O}),window.clearInterval(b.current)}function A(P){if(r){var C=P.touches[0],E=C.screenX,O=C.screenY;o({x:E,y:O});var M=E-r.x,_=O-r.y;e(M,_);var et=Date.now();c(et),m(et-l),p({x:M,y:_})}}function T(){if(r&&(o(null),p(null),y)){var P=y.x/f,C=y.y/f,E=Math.abs(P),O=Math.abs(C);if(Math.max(E,O)<tl)return;var M=P,_=C;b.current=window.setInterval(function(){if(Math.abs(M)<Yi&&Math.abs(_)<Yi){window.clearInterval(b.current);return}M*=Zi,_*=Zi,e(M*Ne,_*Ne)},Ne)}}var R=h.useRef();function L(P){var C=P.deltaX,E=P.deltaY,O=0,M=Math.abs(C),_=Math.abs(E);M===_?O=R.current==="x"?C:E:M>_?(O=C,R.current="x"):(O=E,R.current="y"),e(-O,-O)&&P.preventDefault()}var x=h.useRef(null);x.current={onTouchStart:S,onTouchMove:A,onTouchEnd:T,onWheel:L},h.useEffect(function(){function P(M){x.current.onTouchStart(M)}function C(M){x.current.onTouchMove(M)}function E(M){x.current.onTouchEnd(M)}function O(M){x.current.onWheel(M)}return document.addEventListener("touchmove",C,{passive:!1}),document.addEventListener("touchend",E,{passive:!0}),t.current.addEventListener("touchstart",P,{passive:!0}),t.current.addEventListener("wheel",O,{passive:!1}),function(){document.removeEventListener("touchmove",C),document.removeEventListener("touchend",E)}},[])}function $s(t){var e=h.useState(0),n=z(e,2),i=n[0],r=n[1],o=h.useRef(0),s=h.useRef();return s.current=t,Ra(function(){var a;(a=s.current)===null||a===void 0||a.call(s)},[i]),function(){o.current===i&&(o.current+=1,r(o.current))}}function nl(t){var e=h.useRef([]),n=h.useState({}),i=z(n,2),r=i[1],o=h.useRef(typeof t=="function"?t():t),s=$s(function(){var l=o.current;e.current.forEach(function(c){l=c(l)}),e.current=[],o.current=l,r({})});function a(l){e.current.push(l),s()}return[o.current,a]}var Ji={width:0,height:0,left:0,top:0,right:0};function il(t,e,n,i,r,o,s){var a=s.tabs,l=s.tabPosition,c=s.rtl,u,d,f;return["top","bottom"].includes(l)?(u="width",d=c?"right":"left",f=Math.abs(n)):(u="height",d="top",f=-n),h.useMemo(function(){if(!a.length)return[0,0];for(var m=a.length,v=m,g=0;g<m;g+=1){var y=t.get(a[g].key)||Ji;if(Math.floor(y[d]+y[u])>Math.floor(f+e)){v=g-1;break}}for(var p=0,b=m-1;b>=0;b-=1){var S=t.get(a[b].key)||Ji;if(S[d]<f){p=b+1;break}}return p>=v?[0,0]:[p,v]},[t,e,i,r,o,f,l,a.map(function(m){return m.key}).join("_"),c])}function Qi(t){var e;return t instanceof Map?(e={},t.forEach(function(n,i){e[i]=n})):e=t,JSON.stringify(e)}var rl="TABS_DQ";function As(t){return String(t).replace(/"/g,rl)}function qn(t,e,n,i){return!(!n||i||t===!1||t===void 0&&(e===!1||e===null))}var Rs=h.forwardRef(function(t,e){var n=t.prefixCls,i=t.editable,r=t.locale,o=t.style;return!i||i.showAdd===!1?null:h.createElement("button",{ref:e,type:"button",className:"".concat(n,"-nav-add"),style:o,"aria-label":(r==null?void 0:r.addAriaLabel)||"Add tab",onClick:function(a){i.onEdit("add",{event:a})}},i.addIcon||"+")}),tr=h.forwardRef(function(t,e){var n=t.position,i=t.prefixCls,r=t.extra;if(!r)return null;var o,s={};return Kn(r)==="object"&&!h.isValidElement(r)?s=r:s.right=r,n==="right"&&(o=s.right),n==="left"&&(o=s.left),o?h.createElement("div",{className:"".concat(i,"-extra-content"),ref:e},o):null}),sl=h.forwardRef(function(t,e){var n=t.prefixCls,i=t.id,r=t.tabs,o=t.locale,s=t.mobile,a=t.more,l=a===void 0?{}:a,c=t.style,u=t.className,d=t.editable,f=t.tabBarGutter,m=t.rtl,v=t.removeAriaLabel,g=t.onTabClick,y=t.getPopupContainer,p=t.popupClassName,b=h.useState(!1),S=z(b,2),A=S[0],T=S[1],R=h.useState(null),L=z(R,2),x=L[0],P=L[1],C=l.icon,E=C===void 0?"More":C,O="".concat(i,"-more-popup"),M="".concat(n,"-dropdown"),_=x!==null?"".concat(O,"-").concat(x):null,et=o==null?void 0:o.dropdownAriaLabel;function G(B,K){B.preventDefault(),B.stopPropagation(),d.onEdit("remove",{key:K,event:B})}var w=h.createElement(Ea,{onClick:function(K){var nt=K.key,it=K.domEvent;g(nt,it),T(!1)},prefixCls:"".concat(M,"-menu"),id:O,tabIndex:-1,role:"listbox","aria-activedescendant":_,selectedKeys:[x],"aria-label":et!==void 0?et:"expanded dropdown"},r.map(function(B){var K=B.closable,nt=B.disabled,it=B.closeIcon,Q=B.key,ut=B.label,lt=qn(K,it,d,nt);return h.createElement(Ma,{key:Q,id:"".concat(O,"-").concat(Q),role:"option","aria-controls":i&&"".concat(i,"-panel-").concat(Q),disabled:nt},h.createElement("span",null,ut),lt&&h.createElement("button",{type:"button","aria-label":v||"remove",tabIndex:0,className:"".concat(M,"-menu-item-remove"),onClick:function(Tt){Tt.stopPropagation(),G(Tt,Q)}},it||d.removeIcon||"×"))}));function N(B){for(var K=r.filter(function(lt){return!lt.disabled}),nt=K.findIndex(function(lt){return lt.key===x})||0,it=K.length,Q=0;Q<it;Q+=1){nt=(nt+B+it)%it;var ut=K[nt];if(!ut.disabled){P(ut.key);return}}}function I(B){var K=B.which;if(!A){[It.DOWN,It.SPACE,It.ENTER].includes(K)&&(T(!0),B.preventDefault());return}switch(K){case It.UP:N(-1),B.preventDefault();break;case It.DOWN:N(1),B.preventDefault();break;case It.ESC:T(!1);break;case It.SPACE:case It.ENTER:x!==null&&g(x,B);break}}h.useEffect(function(){var B=document.getElementById(_);B&&B.scrollIntoView&&B.scrollIntoView(!1)},[x]),h.useEffect(function(){A||P(null)},[A]);var H=ct({},m?"marginRight":"marginLeft",f);r.length||(H.visibility="hidden",H.order=1);var st=k(ct({},"".concat(M,"-rtl"),m)),xt=s?null:h.createElement(Da,Ct({prefixCls:M,overlay:w,visible:r.length?A:!1,onVisibleChange:T,overlayClassName:k(st,p),mouseEnterDelay:.1,mouseLeaveDelay:.1,getPopupContainer:y},l),h.createElement("button",{type:"button",className:"".concat(n,"-nav-more"),style:H,"aria-haspopup":"listbox","aria-controls":O,id:"".concat(i,"-more"),"aria-expanded":A,onKeyDown:I},E));return h.createElement("div",{className:k("".concat(n,"-nav-operations"),u),style:c,ref:e},xt,h.createElement(Rs,{prefixCls:n,locale:o,editable:d}))});const ol=h.memo(sl,function(t,e){return e.tabMoving});var al=function(e){var n=e.prefixCls,i=e.id,r=e.active,o=e.focus,s=e.tab,a=s.key,l=s.label,c=s.disabled,u=s.closeIcon,d=s.icon,f=e.closable,m=e.renderWrapper,v=e.removeAriaLabel,g=e.editable,y=e.onClick,p=e.onFocus,b=e.onBlur,S=e.onKeyDown,A=e.onMouseDown,T=e.onMouseUp,R=e.style,L=e.tabCount,x=e.currentPosition,P="".concat(n,"-tab"),C=qn(f,u,g,c);function E(G){c||y(G)}function O(G){G.preventDefault(),G.stopPropagation(),g.onEdit("remove",{key:a,event:G})}var M=h.useMemo(function(){return d&&typeof l=="string"?h.createElement("span",null,l):l},[l,d]),_=h.useRef(null);h.useEffect(function(){o&&_.current&&_.current.focus()},[o]);var et=h.createElement("div",{key:a,"data-node-key":As(a),className:k(P,ct(ct(ct(ct({},"".concat(P,"-with-remove"),C),"".concat(P,"-active"),r),"".concat(P,"-disabled"),c),"".concat(P,"-focus"),o)),style:R,onClick:E},h.createElement("div",{ref:_,role:"tab","aria-selected":r,id:i&&"".concat(i,"-tab-").concat(a),className:"".concat(P,"-btn"),"aria-controls":i&&"".concat(i,"-panel-").concat(a),"aria-disabled":c,tabIndex:c?null:r?0:-1,onClick:function(w){w.stopPropagation(),E(w)},onKeyDown:S,onMouseDown:A,onMouseUp:T,onFocus:p,onBlur:b},o&&h.createElement("div",{"aria-live":"polite",style:{width:0,height:0,position:"absolute",overflow:"hidden",opacity:0}},"Tab ".concat(x," of ").concat(L)),d&&h.createElement("span",{className:"".concat(P,"-icon")},d),l&&M),C&&h.createElement("button",{type:"button",role:"tab","aria-label":v||"remove",tabIndex:r?0:-1,className:"".concat(P,"-remove"),onClick:function(w){w.stopPropagation(),O(w)}},u||g.removeIcon||"×"));return m?m(et):et},ll=function(e,n){var i=e.offsetWidth,r=e.offsetHeight,o=e.offsetTop,s=e.offsetLeft,a=e.getBoundingClientRect(),l=a.width,c=a.height,u=a.left,d=a.top;return Math.abs(l-i)<1?[l,c,u-n.left,d-n.top]:[i,r,s,o]},Qt=function(e){var n=e.current||{},i=n.offsetWidth,r=i===void 0?0:i,o=n.offsetHeight,s=o===void 0?0:o;if(e.current){var a=e.current.getBoundingClientRect(),l=a.width,c=a.height;if(Math.abs(l-r)<1)return[l,c]}return[r,s]},Oe=function(e,n){return e[n?0:1]},er=h.forwardRef(function(t,e){var n=t.className,i=t.style,r=t.id,o=t.animated,s=t.activeKey,a=t.rtl,l=t.extra,c=t.editable,u=t.locale,d=t.tabPosition,f=t.tabBarGutter,m=t.children,v=t.onTabClick,g=t.onTabScroll,y=t.indicator,p=h.useContext(Qe),b=p.prefixCls,S=p.tabs,A=h.useRef(null),T=h.useRef(null),R=h.useRef(null),L=h.useRef(null),x=h.useRef(null),P=h.useRef(null),C=h.useRef(null),E=d==="top"||d==="bottom",O=qi(0,function(j,V){E&&g&&g({direction:j>V?"left":"right"})}),M=z(O,2),_=M[0],et=M[1],G=qi(0,function(j,V){!E&&g&&g({direction:j>V?"top":"bottom"})}),w=z(G,2),N=w[0],I=w[1],H=h.useState([0,0]),st=z(H,2),xt=st[0],B=st[1],K=h.useState([0,0]),nt=z(K,2),it=nt[0],Q=nt[1],ut=h.useState([0,0]),lt=z(ut,2),Lt=lt[0],Tt=lt[1],At=h.useState([0,0]),_t=z(At,2),F=_t[0],yt=_t[1],zt=nl(new Map),De=z(zt,2),on=De[0],an=De[1],tt=Qa(S,on,it[0]),X=Oe(xt,E),Rt=Oe(it,E),de=Oe(Lt,E),Ri=Oe(F,E),Ei=Math.floor(X)<Math.floor(Rt+de),Pt=Ei?X-Ri:X-de,ua="".concat(b,"-nav-operations-hidden"),Ot=0,kt=0;E&&a?(Ot=0,kt=Math.max(0,Rt-Pt)):(Ot=Math.min(0,Pt-Rt),kt=0);function ln(j){return j<Ot?Ot:j>kt?kt:j}var cn=h.useRef(null),da=h.useState(),Mi=z(da,2),Ve=Mi[0],Di=Mi[1];function un(){Di(Date.now())}function dn(){cn.current&&clearTimeout(cn.current)}el(L,function(j,V){function W(Y,bt){Y(function(ht){var Zt=ln(ht+bt);return Zt})}return Ei?(E?W(et,j):W(I,V),dn(),un(),!0):!1}),h.useEffect(function(){return dn(),Ve&&(cn.current=setTimeout(function(){Di(0)},100)),dn},[Ve]);var fa=il(tt,Pt,E?_:N,Rt,de,Ri,vt(vt({},t),{},{tabs:S})),Vi=z(fa,2),ha=Vi[0],ma=Vi[1],Li=Va(function(){var j=arguments.length>0&&arguments[0]!==void 0?arguments[0]:s,V=tt.get(j)||{width:0,height:0,left:0,right:0,top:0};if(E){var W=_;a?V.right<_?W=V.right:V.right+V.width>_+Pt&&(W=V.right+V.width-Pt):V.left<-_?W=-V.left:V.left+V.width>-_+Pt&&(W=-(V.left+V.width-Pt)),I(0),et(ln(W))}else{var Y=N;V.top<-N?Y=-V.top:V.top+V.height>-N+Pt&&(Y=-(V.top+V.height-Pt)),et(0),I(ln(Y))}}),pa=h.useState(),_i=z(pa,2),qt=_i[0],fe=_i[1],ga=h.useState(!1),Oi=z(ga,2),va=Oi[0],Ii=Oi[1],Et=S.filter(function(j){return!j.disabled}).map(function(j){return j.key}),Yt=function(V){var W=Et.indexOf(qt||s),Y=Et.length,bt=(W+V+Y)%Y,ht=Et[bt];fe(ht)},ya=function(V){var W=V.code,Y=a&&E,bt=Et[0],ht=Et[Et.length-1];switch(W){case"ArrowLeft":{E&&Yt(Y?1:-1);break}case"ArrowRight":{E&&Yt(Y?-1:1);break}case"ArrowUp":{V.preventDefault(),E||Yt(-1);break}case"ArrowDown":{V.preventDefault(),E||Yt(1);break}case"Home":{V.preventDefault(),fe(bt);break}case"End":{V.preventDefault(),fe(ht);break}case"Enter":case"Space":{V.preventDefault(),v(qt,V);break}case"Backspace":case"Delete":{var Zt=Et.indexOf(qt),mt=S.find(function(Jt){return Jt.key===qt}),mn=qn(mt==null?void 0:mt.closable,mt==null?void 0:mt.closeIcon,c,mt==null?void 0:mt.disabled);mn&&(V.preventDefault(),V.stopPropagation(),c.onEdit("remove",{key:qt,event:V}),Zt===Et.length-1?Yt(-1):Yt(1));break}}},Le={};E?Le[a?"marginRight":"marginLeft"]=f:Le.marginTop=f;var Bi=S.map(function(j,V){var W=j.key;return h.createElement(al,{id:r,prefixCls:b,key:W,tab:j,style:V===0?void 0:Le,closable:j.closable,editable:c,active:W===s,focus:W===qt,renderWrapper:m,removeAriaLabel:u==null?void 0:u.removeAriaLabel,tabCount:Et.length,currentPosition:V+1,onClick:function(bt){v(W,bt)},onKeyDown:ya,onFocus:function(){va||fe(W),Li(W),un(),L.current&&(a||(L.current.scrollLeft=0),L.current.scrollTop=0)},onBlur:function(){fe(void 0)},onMouseDown:function(){Ii(!0)},onMouseUp:function(){Ii(!1)}})}),ji=function(){return an(function(){var V,W=new Map,Y=(V=x.current)===null||V===void 0?void 0:V.getBoundingClientRect();return S.forEach(function(bt){var ht,Zt=bt.key,mt=(ht=x.current)===null||ht===void 0?void 0:ht.querySelector('[data-node-key="'.concat(As(Zt),'"]'));if(mt){var mn=ll(mt,Y),Jt=z(mn,4),Pa=Jt[0],Ca=Jt[1],wa=Jt[2],$a=Jt[3];W.set(Zt,{width:Pa,height:Ca,left:wa,top:$a})}}),W})};h.useEffect(function(){ji()},[S.map(function(j){return j.key}).join("_")]);var _e=$s(function(){var j=Qt(A),V=Qt(T),W=Qt(R);B([j[0]-V[0]-W[0],j[1]-V[1]-W[1]]);var Y=Qt(C);Tt(Y);var bt=Qt(P);yt(bt);var ht=Qt(x);Q([ht[0]-Y[0],ht[1]-Y[1]]),ji()}),ba=S.slice(0,ha),Sa=S.slice(ma+1),Ni=[].concat(Gi(ba),Gi(Sa)),Fi=tt.get(s),xa=Ja({activeTabOffset:Fi,horizontal:E,indicator:y,rtl:a}),Ta=xa.style;h.useEffect(function(){Li()},[s,Ot,kt,Qi(Fi),Qi(tt),E]),h.useEffect(function(){_e()},[a]);var zi=!!Ni.length,he="".concat(b,"-nav-wrap"),fn,hn,ki,Wi;return E?a?(hn=_>0,fn=_!==kt):(fn=_<0,hn=_!==Ot):(ki=N<0,Wi=N!==Ot),h.createElement(pn,{onResize:_e},h.createElement("div",{ref:La(e,A),role:"tablist","aria-orientation":E?"horizontal":"vertical",className:k("".concat(b,"-nav"),n),style:i,onKeyDown:function(){un()}},h.createElement(tr,{ref:T,position:"left",extra:l,prefixCls:b}),h.createElement(pn,{onResize:_e},h.createElement("div",{className:k(he,ct(ct(ct(ct({},"".concat(he,"-ping-left"),fn),"".concat(he,"-ping-right"),hn),"".concat(he,"-ping-top"),ki),"".concat(he,"-ping-bottom"),Wi)),ref:L},h.createElement(pn,{onResize:_e},h.createElement("div",{ref:x,className:"".concat(b,"-nav-list"),style:{transform:"translate(".concat(_,"px, ").concat(N,"px)"),transition:Ve?"none":void 0}},Bi,h.createElement(Rs,{ref:C,prefixCls:b,locale:u,editable:c,style:vt(vt({},Bi.length===0?void 0:Le),{},{visibility:zi?"hidden":null})}),h.createElement("div",{className:k("".concat(b,"-ink-bar"),ct({},"".concat(b,"-ink-bar-animated"),o.inkBar)),style:Ta}))))),h.createElement(ol,Ct({},t,{removeAriaLabel:u==null?void 0:u.removeAriaLabel,ref:P,prefixCls:b,tabs:Ni,className:!zi&&ua,tabMoving:!!Ve})),h.createElement(tr,{ref:R,position:"right",extra:l,prefixCls:b})))}),Es=h.forwardRef(function(t,e){var n=t.prefixCls,i=t.className,r=t.style,o=t.id,s=t.active,a=t.tabKey,l=t.children;return h.createElement("div",{id:o&&"".concat(o,"-panel-").concat(a),role:"tabpanel",tabIndex:s?0:-1,"aria-labelledby":o&&"".concat(o,"-tab-").concat(a),"aria-hidden":!s,style:r,className:k(n,s&&"".concat(n,"-active"),i),ref:e},l)}),cl=["renderTabBar"],ul=["label","key"],dl=function(e){var n=e.renderTabBar,i=We(e,cl),r=h.useContext(Qe),o=r.tabs;if(n){var s=vt(vt({},i),{},{panes:o.map(function(a){var l=a.label,c=a.key,u=We(a,ul);return h.createElement(Es,Ct({tab:l,key:c,tabKey:c},u))})});return n(s,er)}return h.createElement(er,i)},fl=["key","forceRender","style","className","destroyInactiveTabPane"],hl=function(e){var n=e.id,i=e.activeKey,r=e.animated,o=e.tabPosition,s=e.destroyInactiveTabPane,a=h.useContext(Qe),l=a.prefixCls,c=a.tabs,u=r.tabPane,d="".concat(l,"-tabpane");return h.createElement("div",{className:k("".concat(l,"-content-holder"))},h.createElement("div",{className:k("".concat(l,"-content"),"".concat(l,"-content-").concat(o),ct({},"".concat(l,"-content-animated"),u))},c.map(function(f){var m=f.key,v=f.forceRender,g=f.style,y=f.className,p=f.destroyInactiveTabPane,b=We(f,fl),S=m===i;return h.createElement(_a,Ct({key:m,visible:S,forceRender:v,removeOnLeave:!!(s||p),leavedClassName:"".concat(d,"-hidden")},r.tabPaneMotion),function(A,T){var R=A.style,L=A.className;return h.createElement(Es,Ct({},b,{prefixCls:d,id:n,tabKey:m,animated:u,active:S,style:vt(vt({},g),R),className:k(y,L),ref:T}))})})))};function ml(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{inkBar:!0,tabPane:!1},e;return t===!1?e={inkBar:!1,tabPane:!1}:t===!0?e={inkBar:!0,tabPane:!1}:e=vt({inkBar:!0},Kn(t)==="object"?t:{}),e.tabPaneMotion&&e.tabPane===void 0&&(e.tabPane=!0),!e.tabPaneMotion&&e.tabPane&&(e.tabPane=!1),e}var pl=["id","prefixCls","className","items","direction","activeKey","defaultActiveKey","editable","animated","tabPosition","tabBarGutter","tabBarStyle","tabBarExtraContent","locale","more","destroyInactiveTabPane","renderTabBar","onChange","onTabClick","onTabScroll","getPopupContainer","popupClassName","indicator"],nr=0,gl=h.forwardRef(function(t,e){var n=t.id,i=t.prefixCls,r=i===void 0?"rc-tabs":i,o=t.className,s=t.items,a=t.direction,l=t.activeKey,c=t.defaultActiveKey,u=t.editable,d=t.animated,f=t.tabPosition,m=f===void 0?"top":f,v=t.tabBarGutter,g=t.tabBarStyle,y=t.tabBarExtraContent,p=t.locale,b=t.more,S=t.destroyInactiveTabPane,A=t.renderTabBar,T=t.onChange,R=t.onTabClick,L=t.onTabScroll,x=t.getPopupContainer,P=t.popupClassName,C=t.indicator,E=We(t,pl),O=h.useMemo(function(){return(s||[]).filter(function(F){return F&&Kn(F)==="object"&&"key"in F})},[s]),M=a==="rtl",_=ml(d),et=h.useState(!1),G=z(et,2),w=G[0],N=G[1];h.useEffect(function(){N(Oa())},[]);var I=Ki(function(){var F;return(F=O[0])===null||F===void 0?void 0:F.key},{value:l,defaultValue:c}),H=z(I,2),st=H[0],xt=H[1],B=h.useState(function(){return O.findIndex(function(F){return F.key===st})}),K=z(B,2),nt=K[0],it=K[1];h.useEffect(function(){var F=O.findIndex(function(zt){return zt.key===st});if(F===-1){var yt;F=Math.max(0,Math.min(nt,O.length-1)),xt((yt=O[F])===null||yt===void 0?void 0:yt.key)}it(F)},[O.map(function(F){return F.key}).join("_"),st,nt]);var Q=Ki(null,{value:n}),ut=z(Q,2),lt=ut[0],Lt=ut[1];h.useEffect(function(){n||(Lt("rc-tabs-".concat(nr)),nr+=1)},[]);function Tt(F,yt){R==null||R(F,yt);var zt=F!==st;xt(F),zt&&(T==null||T(F))}var At={id:lt,activeKey:st,animated:_,tabPosition:m,rtl:M,mobile:w},_t=vt(vt({},At),{},{editable:u,locale:p,more:b,tabBarGutter:v,onTabClick:Tt,onTabScroll:L,extra:y,style:g,panes:null,getPopupContainer:x,popupClassName:P,indicator:C});return h.createElement(Qe.Provider,{value:{tabs:O,prefixCls:r}},h.createElement("div",Ct({ref:e,id:n,className:k(r,"".concat(r,"-").concat(m),ct(ct(ct({},"".concat(r,"-mobile"),w),"".concat(r,"-editable"),u),"".concat(r,"-rtl"),M),o)},E),h.createElement(dl,Ct({},_t,{renderTabBar:A})),h.createElement(hl,Ct({destroyInactiveTabPane:S},At,{animated:_}))))});const vl={motionAppear:!1,motionEnter:!0,motionLeave:!0};function yl(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{inkBar:!0,tabPane:!1},n;return e===!1?n={inkBar:!1,tabPane:!1}:e===!0?n={inkBar:!0,tabPane:!0}:n=Object.assign({inkBar:!0},typeof e=="object"?e:{}),n.tabPane&&(n.tabPaneMotion=Object.assign(Object.assign({},vl),{motionName:Ia(t,"switch")})),n}var bl=function(t,e){var n={};for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&e.indexOf(i)<0&&(n[i]=t[i]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,i=Object.getOwnPropertySymbols(t);r<i.length;r++)e.indexOf(i[r])<0&&Object.prototype.propertyIsEnumerable.call(t,i[r])&&(n[i[r]]=t[i[r]]);return n};function Sl(t){return t.filter(e=>e)}function xl(t,e){if(t)return t;const n=Ba(e).map(i=>{if(h.isValidElement(i)){const{key:r,props:o}=i,s=o||{},{tab:a}=s,l=bl(s,["tab"]);return Object.assign(Object.assign({key:String(r)},l),{label:a})}return null});return Sl(n)}const Tl=t=>{const{componentCls:e,motionDurationSlow:n}=t;return[{[e]:{[`${e}-switch`]:{"&-appear, &-enter":{transition:"none","&-start":{opacity:0},"&-active":{opacity:1,transition:`opacity ${n}`}},"&-leave":{position:"absolute",transition:"none",inset:0,"&-start":{opacity:1},"&-active":{opacity:0,transition:`opacity ${n}`}}}}},[Hi(t,"slide-up"),Hi(t,"slide-down")]]},Pl=t=>{const{componentCls:e,tabsCardPadding:n,cardBg:i,cardGutter:r,colorBorderSecondary:o,itemSelectedColor:s}=t;return{[`${e}-card`]:{[`> ${e}-nav, > div > ${e}-nav`]:{[`${e}-tab`]:{margin:0,padding:n,background:i,border:`${$(t.lineWidth)} ${t.lineType} ${o}`,transition:`all ${t.motionDurationSlow} ${t.motionEaseInOut}`},[`${e}-tab-active`]:{color:s,background:t.colorBgContainer},[`${e}-tab-focus`]:Object.assign({},Cs(t,-3)),[`${e}-ink-bar`]:{visibility:"hidden"},[`& ${e}-tab${e}-tab-focus ${e}-tab-btn`]:{outline:"none"}},[`&${e}-top, &${e}-bottom`]:{[`> ${e}-nav, > div > ${e}-nav`]:{[`${e}-tab + ${e}-tab`]:{marginLeft:{_skip_check_:!0,value:$(r)}}}},[`&${e}-top`]:{[`> ${e}-nav, > div > ${e}-nav`]:{[`${e}-tab`]:{borderRadius:`${$(t.borderRadiusLG)} ${$(t.borderRadiusLG)} 0 0`},[`${e}-tab-active`]:{borderBottomColor:t.colorBgContainer}}},[`&${e}-bottom`]:{[`> ${e}-nav, > div > ${e}-nav`]:{[`${e}-tab`]:{borderRadius:`0 0 ${$(t.borderRadiusLG)} ${$(t.borderRadiusLG)}`},[`${e}-tab-active`]:{borderTopColor:t.colorBgContainer}}},[`&${e}-left, &${e}-right`]:{[`> ${e}-nav, > div > ${e}-nav`]:{[`${e}-tab + ${e}-tab`]:{marginTop:$(r)}}},[`&${e}-left`]:{[`> ${e}-nav, > div > ${e}-nav`]:{[`${e}-tab`]:{borderRadius:{_skip_check_:!0,value:`${$(t.borderRadiusLG)} 0 0 ${$(t.borderRadiusLG)}`}},[`${e}-tab-active`]:{borderRightColor:{_skip_check_:!0,value:t.colorBgContainer}}}},[`&${e}-right`]:{[`> ${e}-nav, > div > ${e}-nav`]:{[`${e}-tab`]:{borderRadius:{_skip_check_:!0,value:`0 ${$(t.borderRadiusLG)} ${$(t.borderRadiusLG)} 0`}},[`${e}-tab-active`]:{borderLeftColor:{_skip_check_:!0,value:t.colorBgContainer}}}}}}},Cl=t=>{const{componentCls:e,itemHoverColor:n,dropdownEdgeChildVerticalPadding:i}=t;return{[`${e}-dropdown`]:Object.assign(Object.assign({},Hn(t)),{position:"absolute",top:-9999,left:{_skip_check_:!0,value:-9999},zIndex:t.zIndexPopup,display:"block","&-hidden":{display:"none"},[`${e}-dropdown-menu`]:{maxHeight:t.tabsDropdownHeight,margin:0,padding:`${$(i)} 0`,overflowX:"hidden",overflowY:"auto",textAlign:{_skip_check_:!0,value:"left"},listStyleType:"none",backgroundColor:t.colorBgContainer,backgroundClip:"padding-box",borderRadius:t.borderRadiusLG,outline:"none",boxShadow:t.boxShadowSecondary,"&-item":Object.assign(Object.assign({},Xn),{display:"flex",alignItems:"center",minWidth:t.tabsDropdownWidth,margin:0,padding:`${$(t.paddingXXS)} ${$(t.paddingSM)}`,color:t.colorText,fontWeight:"normal",fontSize:t.fontSize,lineHeight:t.lineHeight,cursor:"pointer",transition:`all ${t.motionDurationSlow}`,"> span":{flex:1,whiteSpace:"nowrap"},"&-remove":{flex:"none",marginLeft:{_skip_check_:!0,value:t.marginSM},color:t.colorTextDescription,fontSize:t.fontSizeSM,background:"transparent",border:0,cursor:"pointer","&:hover":{color:n}},"&:hover":{background:t.controlItemBgHover},"&-disabled":{"&, &:hover":{color:t.colorTextDisabled,background:"transparent",cursor:"not-allowed"}}})}})}},wl=t=>{const{componentCls:e,margin:n,colorBorderSecondary:i,horizontalMargin:r,verticalItemPadding:o,verticalItemMargin:s,calc:a}=t;return{[`${e}-top, ${e}-bottom`]:{flexDirection:"column",[`> ${e}-nav, > div > ${e}-nav`]:{margin:r,"&::before":{position:"absolute",right:{_skip_check_:!0,value:0},left:{_skip_check_:!0,value:0},borderBottom:`${$(t.lineWidth)} ${t.lineType} ${i}`,content:"''"},[`${e}-ink-bar`]:{height:t.lineWidthBold,"&-animated":{transition:`width ${t.motionDurationSlow}, left ${t.motionDurationSlow},
            right ${t.motionDurationSlow}`}},[`${e}-nav-wrap`]:{"&::before, &::after":{top:0,bottom:0,width:t.controlHeight},"&::before":{left:{_skip_check_:!0,value:0},boxShadow:t.boxShadowTabsOverflowLeft},"&::after":{right:{_skip_check_:!0,value:0},boxShadow:t.boxShadowTabsOverflowRight},[`&${e}-nav-wrap-ping-left::before`]:{opacity:1},[`&${e}-nav-wrap-ping-right::after`]:{opacity:1}}}},[`${e}-top`]:{[`> ${e}-nav,
        > div > ${e}-nav`]:{"&::before":{bottom:0},[`${e}-ink-bar`]:{bottom:0}}},[`${e}-bottom`]:{[`> ${e}-nav, > div > ${e}-nav`]:{order:1,marginTop:n,marginBottom:0,"&::before":{top:0},[`${e}-ink-bar`]:{top:0}},[`> ${e}-content-holder, > div > ${e}-content-holder`]:{order:0}},[`${e}-left, ${e}-right`]:{[`> ${e}-nav, > div > ${e}-nav`]:{flexDirection:"column",minWidth:a(t.controlHeight).mul(1.25).equal(),[`${e}-tab`]:{padding:o,textAlign:"center"},[`${e}-tab + ${e}-tab`]:{margin:s},[`${e}-nav-wrap`]:{flexDirection:"column","&::before, &::after":{right:{_skip_check_:!0,value:0},left:{_skip_check_:!0,value:0},height:t.controlHeight},"&::before":{top:0,boxShadow:t.boxShadowTabsOverflowTop},"&::after":{bottom:0,boxShadow:t.boxShadowTabsOverflowBottom},[`&${e}-nav-wrap-ping-top::before`]:{opacity:1},[`&${e}-nav-wrap-ping-bottom::after`]:{opacity:1}},[`${e}-ink-bar`]:{width:t.lineWidthBold,"&-animated":{transition:`height ${t.motionDurationSlow}, top ${t.motionDurationSlow}`}},[`${e}-nav-list, ${e}-nav-operations`]:{flex:"1 0 auto",flexDirection:"column"}}},[`${e}-left`]:{[`> ${e}-nav, > div > ${e}-nav`]:{[`${e}-ink-bar`]:{right:{_skip_check_:!0,value:0}}},[`> ${e}-content-holder, > div > ${e}-content-holder`]:{marginLeft:{_skip_check_:!0,value:$(a(t.lineWidth).mul(-1).equal())},borderLeft:{_skip_check_:!0,value:`${$(t.lineWidth)} ${t.lineType} ${t.colorBorder}`},[`> ${e}-content > ${e}-tabpane`]:{paddingLeft:{_skip_check_:!0,value:t.paddingLG}}}},[`${e}-right`]:{[`> ${e}-nav, > div > ${e}-nav`]:{order:1,[`${e}-ink-bar`]:{left:{_skip_check_:!0,value:0}}},[`> ${e}-content-holder, > div > ${e}-content-holder`]:{order:0,marginRight:{_skip_check_:!0,value:a(t.lineWidth).mul(-1).equal()},borderRight:{_skip_check_:!0,value:`${$(t.lineWidth)} ${t.lineType} ${t.colorBorder}`},[`> ${e}-content > ${e}-tabpane`]:{paddingRight:{_skip_check_:!0,value:t.paddingLG}}}}}},$l=t=>{const{componentCls:e,cardPaddingSM:n,cardPaddingLG:i,horizontalItemPaddingSM:r,horizontalItemPaddingLG:o}=t;return{[e]:{"&-small":{[`> ${e}-nav`]:{[`${e}-tab`]:{padding:r,fontSize:t.titleFontSizeSM}}},"&-large":{[`> ${e}-nav`]:{[`${e}-tab`]:{padding:o,fontSize:t.titleFontSizeLG}}}},[`${e}-card`]:{[`&${e}-small`]:{[`> ${e}-nav`]:{[`${e}-tab`]:{padding:n}},[`&${e}-bottom`]:{[`> ${e}-nav ${e}-tab`]:{borderRadius:`0 0 ${$(t.borderRadius)} ${$(t.borderRadius)}`}},[`&${e}-top`]:{[`> ${e}-nav ${e}-tab`]:{borderRadius:`${$(t.borderRadius)} ${$(t.borderRadius)} 0 0`}},[`&${e}-right`]:{[`> ${e}-nav ${e}-tab`]:{borderRadius:{_skip_check_:!0,value:`0 ${$(t.borderRadius)} ${$(t.borderRadius)} 0`}}},[`&${e}-left`]:{[`> ${e}-nav ${e}-tab`]:{borderRadius:{_skip_check_:!0,value:`${$(t.borderRadius)} 0 0 ${$(t.borderRadius)}`}}}},[`&${e}-large`]:{[`> ${e}-nav`]:{[`${e}-tab`]:{padding:i}}}}}},Al=t=>{const{componentCls:e,itemActiveColor:n,itemHoverColor:i,iconCls:r,tabsHorizontalItemMargin:o,horizontalItemPadding:s,itemSelectedColor:a,itemColor:l}=t,c=`${e}-tab`;return{[c]:{position:"relative",WebkitTouchCallout:"none",WebkitTapHighlightColor:"transparent",display:"inline-flex",alignItems:"center",padding:s,fontSize:t.titleFontSize,background:"transparent",border:0,outline:"none",cursor:"pointer",color:l,"&-btn, &-remove":{"&:focus:not(:focus-visible), &:active":{color:n}},"&-btn":{outline:"none",transition:`all ${t.motionDurationSlow}`,[`${c}-icon:not(:last-child)`]:{marginInlineEnd:t.marginSM}},"&-remove":Object.assign({flex:"none",marginRight:{_skip_check_:!0,value:t.calc(t.marginXXS).mul(-1).equal()},marginLeft:{_skip_check_:!0,value:t.marginXS},color:t.colorTextDescription,fontSize:t.fontSizeSM,background:"transparent",border:"none",outline:"none",cursor:"pointer",transition:`all ${t.motionDurationSlow}`,"&:hover":{color:t.colorTextHeading}},Rn(t)),"&:hover":{color:i},[`&${c}-active ${c}-btn`]:{color:a,textShadow:t.tabsActiveTextShadow},[`&${c}-focus ${c}-btn`]:Object.assign({},Cs(t)),[`&${c}-disabled`]:{color:t.colorTextDisabled,cursor:"not-allowed"},[`&${c}-disabled ${c}-btn, &${c}-disabled ${e}-remove`]:{"&:focus, &:active":{color:t.colorTextDisabled}},[`& ${c}-remove ${r}`]:{margin:0},[`${r}:not(:last-child)`]:{marginRight:{_skip_check_:!0,value:t.marginSM}}},[`${c} + ${c}`]:{margin:{_skip_check_:!0,value:o}}}},Rl=t=>{const{componentCls:e,tabsHorizontalItemMarginRTL:n,iconCls:i,cardGutter:r,calc:o}=t;return{[`${e}-rtl`]:{direction:"rtl",[`${e}-nav`]:{[`${e}-tab`]:{margin:{_skip_check_:!0,value:n},[`${e}-tab:last-of-type`]:{marginLeft:{_skip_check_:!0,value:0}},[i]:{marginRight:{_skip_check_:!0,value:0},marginLeft:{_skip_check_:!0,value:$(t.marginSM)}},[`${e}-tab-remove`]:{marginRight:{_skip_check_:!0,value:$(t.marginXS)},marginLeft:{_skip_check_:!0,value:$(o(t.marginXXS).mul(-1).equal())},[i]:{margin:0}}}},[`&${e}-left`]:{[`> ${e}-nav`]:{order:1},[`> ${e}-content-holder`]:{order:0}},[`&${e}-right`]:{[`> ${e}-nav`]:{order:0},[`> ${e}-content-holder`]:{order:1}},[`&${e}-card${e}-top, &${e}-card${e}-bottom`]:{[`> ${e}-nav, > div > ${e}-nav`]:{[`${e}-tab + ${e}-tab`]:{marginRight:{_skip_check_:!0,value:r},marginLeft:{_skip_check_:!0,value:0}}}}},[`${e}-dropdown-rtl`]:{direction:"rtl"},[`${e}-menu-item`]:{[`${e}-dropdown-rtl`]:{textAlign:{_skip_check_:!0,value:"right"}}}}},El=t=>{const{componentCls:e,tabsCardPadding:n,cardHeight:i,cardGutter:r,itemHoverColor:o,itemActiveColor:s,colorBorderSecondary:a}=t;return{[e]:Object.assign(Object.assign(Object.assign(Object.assign({},Hn(t)),{display:"flex",[`> ${e}-nav, > div > ${e}-nav`]:{position:"relative",display:"flex",flex:"none",alignItems:"center",[`${e}-nav-wrap`]:{position:"relative",display:"flex",flex:"auto",alignSelf:"stretch",overflow:"hidden",whiteSpace:"nowrap",transform:"translate(0)","&::before, &::after":{position:"absolute",zIndex:1,opacity:0,transition:`opacity ${t.motionDurationSlow}`,content:"''",pointerEvents:"none"}},[`${e}-nav-list`]:{position:"relative",display:"flex",transition:`opacity ${t.motionDurationSlow}`},[`${e}-nav-operations`]:{display:"flex",alignSelf:"stretch"},[`${e}-nav-operations-hidden`]:{position:"absolute",visibility:"hidden",pointerEvents:"none"},[`${e}-nav-more`]:{position:"relative",padding:n,background:"transparent",border:0,color:t.colorText,"&::after":{position:"absolute",right:{_skip_check_:!0,value:0},bottom:0,left:{_skip_check_:!0,value:0},height:t.calc(t.controlHeightLG).div(8).equal(),transform:"translateY(100%)",content:"''"}},[`${e}-nav-add`]:Object.assign({minWidth:i,marginLeft:{_skip_check_:!0,value:r},padding:$(t.paddingXS),background:"transparent",border:`${$(t.lineWidth)} ${t.lineType} ${a}`,borderRadius:`${$(t.borderRadiusLG)} ${$(t.borderRadiusLG)} 0 0`,outline:"none",cursor:"pointer",color:t.colorText,transition:`all ${t.motionDurationSlow} ${t.motionEaseInOut}`,"&:hover":{color:o},"&:active, &:focus:not(:focus-visible)":{color:s}},Rn(t,-3))},[`${e}-extra-content`]:{flex:"none"},[`${e}-ink-bar`]:{position:"absolute",background:t.inkBarColor,pointerEvents:"none"}}),Al(t)),{[`${e}-content`]:{position:"relative",width:"100%"},[`${e}-content-holder`]:{flex:"auto",minWidth:0,minHeight:0},[`${e}-tabpane`]:Object.assign(Object.assign({},Rn(t)),{"&-hidden":{display:"none"}})}),[`${e}-centered`]:{[`> ${e}-nav, > div > ${e}-nav`]:{[`${e}-nav-wrap`]:{[`&:not([class*='${e}-nav-wrap-ping']) > ${e}-nav-list`]:{margin:"auto"}}}}}},Ml=t=>{const e=t.controlHeightLG;return{zIndexPopup:t.zIndexPopupBase+50,cardBg:t.colorFillAlter,cardHeight:e,cardPadding:`${(e-Math.round(t.fontSize*t.lineHeight))/2-t.lineWidth}px ${t.padding}px`,cardPaddingSM:`${t.paddingXXS*1.5}px ${t.padding}px`,cardPaddingLG:`${t.paddingXS}px ${t.padding}px ${t.paddingXXS*1.5}px`,titleFontSize:t.fontSize,titleFontSizeLG:t.fontSizeLG,titleFontSizeSM:t.fontSize,inkBarColor:t.colorPrimary,horizontalMargin:`0 0 ${t.margin}px 0`,horizontalItemGutter:32,horizontalItemMargin:"",horizontalItemMarginRTL:"",horizontalItemPadding:`${t.paddingSM}px 0`,horizontalItemPaddingSM:`${t.paddingXS}px 0`,horizontalItemPaddingLG:`${t.padding}px 0`,verticalItemPadding:`${t.paddingXS}px ${t.paddingLG}px`,verticalItemMargin:`${t.margin}px 0 0 0`,itemColor:t.colorText,itemSelectedColor:t.colorPrimary,itemHoverColor:t.colorPrimaryHover,itemActiveColor:t.colorPrimaryActive,cardGutter:t.marginXXS/2}},Dl=Ts("Tabs",t=>{const e=Ps(t,{tabsCardPadding:t.cardPadding,dropdownEdgeChildVerticalPadding:t.paddingXXS,tabsActiveTextShadow:"0 0 0.25px currentcolor",tabsDropdownHeight:200,tabsDropdownWidth:120,tabsHorizontalItemMargin:`0 0 0 ${$(t.horizontalItemGutter)}`,tabsHorizontalItemMarginRTL:`0 0 0 ${$(t.horizontalItemGutter)}`});return[$l(e),Rl(e),wl(e),Cl(e),Pl(e),El(e),Tl(e)]},Ml),Vl=()=>null;var Ll=function(t,e){var n={};for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&e.indexOf(i)<0&&(n[i]=t[i]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,i=Object.getOwnPropertySymbols(t);r<i.length;r++)e.indexOf(i[r])<0&&Object.prototype.propertyIsEnumerable.call(t,i[r])&&(n[i[r]]=t[i[r]]);return n};const Ms=t=>{var e,n,i,r,o,s,a,l,c,u,d;const{type:f,className:m,rootClassName:v,size:g,onEdit:y,hideAdd:p,centered:b,addIcon:S,removeIcon:A,moreIcon:T,more:R,popupClassName:L,children:x,items:P,animated:C,style:E,indicatorSize:O,indicator:M}=t,_=Ll(t,["type","className","rootClassName","size","onEdit","hideAdd","centered","addIcon","removeIcon","moreIcon","more","popupClassName","children","items","animated","style","indicatorSize","indicator"]),{prefixCls:et}=_,{direction:G,tabs:w,getPrefixCls:N,getPopupContainer:I}=h.useContext(le),H=N("tabs",et),st=ja(H),[xt,B,K]=Dl(H,st);let nt;f==="editable-card"&&(nt={onEdit:(At,_t)=>{let{key:F,event:yt}=_t;y==null||y(At==="add"?yt:F,At)},removeIcon:(e=A??(w==null?void 0:w.removeIcon))!==null&&e!==void 0?e:h.createElement(Na,null),addIcon:(S??(w==null?void 0:w.addIcon))||h.createElement(Za,null),showAdd:p!==!0});const it=N(),Q=ws(g),ut=xl(P,x),lt=yl(H,C),Lt=Object.assign(Object.assign({},w==null?void 0:w.style),E),Tt={align:(n=M==null?void 0:M.align)!==null&&n!==void 0?n:(i=w==null?void 0:w.indicator)===null||i===void 0?void 0:i.align,size:(a=(o=(r=M==null?void 0:M.size)!==null&&r!==void 0?r:O)!==null&&o!==void 0?o:(s=w==null?void 0:w.indicator)===null||s===void 0?void 0:s.size)!==null&&a!==void 0?a:w==null?void 0:w.indicatorSize};return xt(h.createElement(gl,Object.assign({direction:G,getPopupContainer:I},_,{items:ut,className:k({[`${H}-${Q}`]:Q,[`${H}-card`]:["card","editable-card"].includes(f),[`${H}-editable-card`]:f==="editable-card",[`${H}-centered`]:b},w==null?void 0:w.className,m,v,B,K,st),popupClassName:k(L,B,K,st),style:Lt,editable:nt,more:Object.assign({icon:(d=(u=(c=(l=w==null?void 0:w.more)===null||l===void 0?void 0:l.icon)!==null&&c!==void 0?c:w==null?void 0:w.moreIcon)!==null&&u!==void 0?u:T)!==null&&d!==void 0?d:h.createElement(Fa,null),transitionName:`${it}-slide-up`},R),prefixCls:H,animated:lt,indicator:Tt})))};Ms.TabPane=Vl;var _l=function(t,e){var n={};for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&e.indexOf(i)<0&&(n[i]=t[i]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,i=Object.getOwnPropertySymbols(t);r<i.length;r++)e.indexOf(i[r])<0&&Object.prototype.propertyIsEnumerable.call(t,i[r])&&(n[i[r]]=t[i[r]]);return n};const Ds=t=>{var{prefixCls:e,className:n,hoverable:i=!0}=t,r=_l(t,["prefixCls","className","hoverable"]);const{getPrefixCls:o}=h.useContext(le),s=o("card",e),a=k(`${s}-grid`,n,{[`${s}-grid-hoverable`]:i});return h.createElement("div",Object.assign({},r,{className:a}))},Ol=t=>{const{antCls:e,componentCls:n,headerHeight:i,headerPadding:r,tabsMarginBottom:o}=t;return Object.assign(Object.assign({display:"flex",justifyContent:"center",flexDirection:"column",minHeight:i,marginBottom:-1,padding:`0 ${$(r)}`,color:t.colorTextHeading,fontWeight:t.fontWeightStrong,fontSize:t.headerFontSize,background:t.headerBg,borderBottom:`${$(t.lineWidth)} ${t.lineType} ${t.colorBorderSecondary}`,borderRadius:`${$(t.borderRadiusLG)} ${$(t.borderRadiusLG)} 0 0`},Je()),{"&-wrapper":{width:"100%",display:"flex",alignItems:"center"},"&-title":Object.assign(Object.assign({display:"inline-block",flex:1},Xn),{[`
          > ${n}-typography,
          > ${n}-typography-edit-content
        `]:{insetInlineStart:0,marginTop:0,marginBottom:0}}),[`${e}-tabs-top`]:{clear:"both",marginBottom:o,color:t.colorText,fontWeight:"normal",fontSize:t.fontSize,"&-bar":{borderBottom:`${$(t.lineWidth)} ${t.lineType} ${t.colorBorderSecondary}`}}})},Il=t=>{const{cardPaddingBase:e,colorBorderSecondary:n,cardShadow:i,lineWidth:r}=t;return{width:"33.33%",padding:e,border:0,borderRadius:0,boxShadow:`
      ${$(r)} 0 0 0 ${n},
      0 ${$(r)} 0 0 ${n},
      ${$(r)} ${$(r)} 0 0 ${n},
      ${$(r)} 0 0 0 ${n} inset,
      0 ${$(r)} 0 0 ${n} inset;
    `,transition:`all ${t.motionDurationMid}`,"&-hoverable:hover":{position:"relative",zIndex:1,boxShadow:i}}},Bl=t=>{const{componentCls:e,iconCls:n,actionsLiMargin:i,cardActionsIconSize:r,colorBorderSecondary:o,actionsBg:s}=t;return Object.assign(Object.assign({margin:0,padding:0,listStyle:"none",background:s,borderTop:`${$(t.lineWidth)} ${t.lineType} ${o}`,display:"flex",borderRadius:`0 0 ${$(t.borderRadiusLG)} ${$(t.borderRadiusLG)}`},Je()),{"& > li":{margin:i,color:t.colorTextDescription,textAlign:"center","> span":{position:"relative",display:"block",minWidth:t.calc(t.cardActionsIconSize).mul(2).equal(),fontSize:t.fontSize,lineHeight:t.lineHeight,cursor:"pointer","&:hover":{color:t.colorPrimary,transition:`color ${t.motionDurationMid}`},[`a:not(${e}-btn), > ${n}`]:{display:"inline-block",width:"100%",color:t.colorTextDescription,lineHeight:$(t.fontHeight),transition:`color ${t.motionDurationMid}`,"&:hover":{color:t.colorPrimary}},[`> ${n}`]:{fontSize:r,lineHeight:$(t.calc(r).mul(t.lineHeight).equal())}},"&:not(:last-child)":{borderInlineEnd:`${$(t.lineWidth)} ${t.lineType} ${o}`}}})},jl=t=>Object.assign(Object.assign({margin:`${$(t.calc(t.marginXXS).mul(-1).equal())} 0`,display:"flex"},Je()),{"&-avatar":{paddingInlineEnd:t.padding},"&-detail":{overflow:"hidden",flex:1,"> div:not(:last-child)":{marginBottom:t.marginXS}},"&-title":Object.assign({color:t.colorTextHeading,fontWeight:t.fontWeightStrong,fontSize:t.fontSizeLG},Xn),"&-description":{color:t.colorTextDescription}}),Nl=t=>{const{componentCls:e,colorFillAlter:n,headerPadding:i,bodyPadding:r}=t;return{[`${e}-head`]:{padding:`0 ${$(i)}`,background:n,"&-title":{fontSize:t.fontSize}},[`${e}-body`]:{padding:`${$(t.padding)} ${$(r)}`}}},Fl=t=>{const{componentCls:e}=t;return{overflow:"hidden",[`${e}-body`]:{userSelect:"none"}}},zl=t=>{const{componentCls:e,cardShadow:n,cardHeadPadding:i,colorBorderSecondary:r,boxShadowTertiary:o,bodyPadding:s,extraColor:a}=t;return{[e]:Object.assign(Object.assign({},Hn(t)),{position:"relative",background:t.colorBgContainer,borderRadius:t.borderRadiusLG,[`&:not(${e}-bordered)`]:{boxShadow:o},[`${e}-head`]:Ol(t),[`${e}-extra`]:{marginInlineStart:"auto",color:a,fontWeight:"normal",fontSize:t.fontSize},[`${e}-body`]:Object.assign({padding:s,borderRadius:`0 0 ${$(t.borderRadiusLG)} ${$(t.borderRadiusLG)}`},Je()),[`${e}-grid`]:Il(t),[`${e}-cover`]:{"> *":{display:"block",width:"100%",borderRadius:`${$(t.borderRadiusLG)} ${$(t.borderRadiusLG)} 0 0`}},[`${e}-actions`]:Bl(t),[`${e}-meta`]:jl(t)}),[`${e}-bordered`]:{border:`${$(t.lineWidth)} ${t.lineType} ${r}`,[`${e}-cover`]:{marginTop:-1,marginInlineStart:-1,marginInlineEnd:-1}},[`${e}-hoverable`]:{cursor:"pointer",transition:`box-shadow ${t.motionDurationMid}, border-color ${t.motionDurationMid}`,"&:hover":{borderColor:"transparent",boxShadow:n}},[`${e}-contain-grid`]:{borderRadius:`${$(t.borderRadiusLG)} ${$(t.borderRadiusLG)} 0 0 `,[`${e}-body`]:{display:"flex",flexWrap:"wrap"},[`&:not(${e}-loading) ${e}-body`]:{marginBlockStart:t.calc(t.lineWidth).mul(-1).equal(),marginInlineStart:t.calc(t.lineWidth).mul(-1).equal(),padding:0}},[`${e}-contain-tabs`]:{[`> div${e}-head`]:{minHeight:0,[`${e}-head-title, ${e}-extra`]:{paddingTop:i}}},[`${e}-type-inner`]:Nl(t),[`${e}-loading`]:Fl(t),[`${e}-rtl`]:{direction:"rtl"}}},kl=t=>{const{componentCls:e,bodyPaddingSM:n,headerPaddingSM:i,headerHeightSM:r,headerFontSizeSM:o}=t;return{[`${e}-small`]:{[`> ${e}-head`]:{minHeight:r,padding:`0 ${$(i)}`,fontSize:o,[`> ${e}-head-wrapper`]:{[`> ${e}-extra`]:{fontSize:t.fontSize}}},[`> ${e}-body`]:{padding:n}},[`${e}-small${e}-contain-tabs`]:{[`> ${e}-head`]:{[`${e}-head-title, ${e}-extra`]:{paddingTop:0,display:"flex",alignItems:"center"}}}}},Wl=t=>{var e,n;return{headerBg:"transparent",headerFontSize:t.fontSizeLG,headerFontSizeSM:t.fontSize,headerHeight:t.fontSizeLG*t.lineHeightLG+t.padding*2,headerHeightSM:t.fontSize*t.lineHeight+t.paddingXS*2,actionsBg:t.colorBgContainer,actionsLiMargin:`${t.paddingSM}px 0`,tabsMarginBottom:-t.padding-t.lineWidth,extraColor:t.colorText,bodyPaddingSM:12,headerPaddingSM:12,bodyPadding:(e=t.bodyPadding)!==null&&e!==void 0?e:t.paddingLG,headerPadding:(n=t.headerPadding)!==null&&n!==void 0?n:t.paddingLG}},Ul=Ts("Card",t=>{const e=Ps(t,{cardShadow:t.boxShadowCard,cardHeadPadding:t.padding,cardPaddingBase:t.paddingLG,cardActionsIconSize:t.fontSize});return[zl(e),kl(e)]},Wl);var ir=function(t,e){var n={};for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&e.indexOf(i)<0&&(n[i]=t[i]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,i=Object.getOwnPropertySymbols(t);r<i.length;r++)e.indexOf(i[r])<0&&Object.prototype.propertyIsEnumerable.call(t,i[r])&&(n[i[r]]=t[i[r]]);return n};const Gl=t=>{const{actionClasses:e,actions:n=[],actionStyle:i}=t;return h.createElement("ul",{className:e,style:i},n.map((r,o)=>{const s=`action-${o}`;return h.createElement("li",{style:{width:`${100/n.length}%`},key:s},h.createElement("span",null,r))}))},Kl=h.forwardRef((t,e)=>{const{prefixCls:n,className:i,rootClassName:r,style:o,extra:s,headStyle:a={},bodyStyle:l={},title:c,loading:u,bordered:d,variant:f,size:m,type:v,cover:g,actions:y,tabList:p,children:b,activeTabKey:S,defaultActiveTabKey:A,tabBarExtraContent:T,hoverable:R,tabProps:L={},classNames:x,styles:P}=t,C=ir(t,["prefixCls","className","rootClassName","style","extra","headStyle","bodyStyle","title","loading","bordered","variant","size","type","cover","actions","tabList","children","activeTabKey","defaultActiveTabKey","tabBarExtraContent","hoverable","tabProps","classNames","styles"]),{getPrefixCls:E,direction:O,card:M}=h.useContext(le),[_]=za("card",f,d),et=tt=>{var X;(X=t.onTabChange)===null||X===void 0||X.call(t,tt)},G=tt=>{var X;return k((X=M==null?void 0:M.classNames)===null||X===void 0?void 0:X[tt],x==null?void 0:x[tt])},w=tt=>{var X;return Object.assign(Object.assign({},(X=M==null?void 0:M.styles)===null||X===void 0?void 0:X[tt]),P==null?void 0:P[tt])},N=h.useMemo(()=>{let tt=!1;return h.Children.forEach(b,X=>{(X==null?void 0:X.type)===Ds&&(tt=!0)}),tt},[b]),I=E("card",n),[H,st,xt]=Ul(I),B=h.createElement(Wa,{loading:!0,active:!0,paragraph:{rows:4},title:!1},b),K=S!==void 0,nt=Object.assign(Object.assign({},L),{[K?"activeKey":"defaultActiveKey"]:K?S:A,tabBarExtraContent:T});let it;const Q=ws(m),ut=!Q||Q==="default"?"large":Q,lt=p?h.createElement(Ms,Object.assign({size:ut},nt,{className:`${I}-head-tabs`,onChange:et,items:p.map(tt=>{var{tab:X}=tt,Rt=ir(tt,["tab"]);return Object.assign({label:X},Rt)})})):null;if(c||s||lt){const tt=k(`${I}-head`,G("header")),X=k(`${I}-head-title`,G("title")),Rt=k(`${I}-extra`,G("extra")),de=Object.assign(Object.assign({},a),w("header"));it=h.createElement("div",{className:tt,style:de},h.createElement("div",{className:`${I}-head-wrapper`},c&&h.createElement("div",{className:X,style:w("title")},c),s&&h.createElement("div",{className:Rt,style:w("extra")},s)),lt)}const Lt=k(`${I}-cover`,G("cover")),Tt=g?h.createElement("div",{className:Lt,style:w("cover")},g):null,At=k(`${I}-body`,G("body")),_t=Object.assign(Object.assign({},l),w("body")),F=h.createElement("div",{className:At,style:_t},u?B:b),yt=k(`${I}-actions`,G("actions")),zt=y!=null&&y.length?h.createElement(Gl,{actionClasses:yt,actionStyle:w("actions"),actions:y}):null,De=ka(C,["onTabChange"]),on=k(I,M==null?void 0:M.className,{[`${I}-loading`]:u,[`${I}-bordered`]:_!=="borderless",[`${I}-hoverable`]:R,[`${I}-contain-grid`]:N,[`${I}-contain-tabs`]:p==null?void 0:p.length,[`${I}-${Q}`]:Q,[`${I}-type-${v}`]:!!v,[`${I}-rtl`]:O==="rtl"},i,r,st,xt),an=Object.assign(Object.assign({},M==null?void 0:M.style),o);return H(h.createElement("div",Object.assign({ref:e},De,{className:on,style:an}),it,Tt,F,zt))});var Hl=function(t,e){var n={};for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&e.indexOf(i)<0&&(n[i]=t[i]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,i=Object.getOwnPropertySymbols(t);r<i.length;r++)e.indexOf(i[r])<0&&Object.prototype.propertyIsEnumerable.call(t,i[r])&&(n[i[r]]=t[i[r]]);return n};const Xl=t=>{const{prefixCls:e,className:n,avatar:i,title:r,description:o}=t,s=Hl(t,["prefixCls","className","avatar","title","description"]),{getPrefixCls:a}=h.useContext(le),l=a("card",e),c=k(`${l}-meta`,n),u=i?h.createElement("div",{className:`${l}-meta-avatar`},i):null,d=r?h.createElement("div",{className:`${l}-meta-title`},r):null,f=o?h.createElement("div",{className:`${l}-meta-description`},o):null,m=d||f?h.createElement("div",{className:`${l}-meta-detail`},d,f):null;return h.createElement("div",Object.assign({},s,{className:c}),u,m)},Vs=Kl;Vs.Grid=Ds;Vs.Meta=Xl;const Ls=h.createContext({});var ql=function(t,e){var n={};for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&e.indexOf(i)<0&&(n[i]=t[i]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,i=Object.getOwnPropertySymbols(t);r<i.length;r++)e.indexOf(i[r])<0&&Object.prototype.propertyIsEnumerable.call(t,i[r])&&(n[i[r]]=t[i[r]]);return n};function rr(t){return typeof t=="number"?`${t} ${t} auto`:/^\d+(\.\d+)?(px|em|rem|%)$/.test(t)?`0 0 ${t}`:t}const Yl=["xs","sm","md","lg","xl","xxl"],Ah=h.forwardRef((t,e)=>{const{getPrefixCls:n,direction:i}=h.useContext(le),{gutter:r,wrap:o}=h.useContext(Ls),{prefixCls:s,span:a,order:l,offset:c,push:u,pull:d,className:f,children:m,flex:v,style:g}=t,y=ql(t,["prefixCls","span","order","offset","push","pull","className","children","flex","style"]),p=n("col",s),[b,S,A]=Ua(p),T={};let R={};Yl.forEach(P=>{let C={};const E=t[P];typeof E=="number"?C.span=E:typeof E=="object"&&(C=E||{}),delete y[P],R=Object.assign(Object.assign({},R),{[`${p}-${P}-${C.span}`]:C.span!==void 0,[`${p}-${P}-order-${C.order}`]:C.order||C.order===0,[`${p}-${P}-offset-${C.offset}`]:C.offset||C.offset===0,[`${p}-${P}-push-${C.push}`]:C.push||C.push===0,[`${p}-${P}-pull-${C.pull}`]:C.pull||C.pull===0,[`${p}-rtl`]:i==="rtl"}),C.flex&&(R[`${p}-${P}-flex`]=!0,T[`--${p}-${P}-flex`]=rr(C.flex))});const L=k(p,{[`${p}-${a}`]:a!==void 0,[`${p}-order-${l}`]:l,[`${p}-offset-${c}`]:c,[`${p}-push-${u}`]:u,[`${p}-pull-${d}`]:d},f,R,S,A),x={};if(r&&r[0]>0){const P=r[0]/2;x.paddingLeft=P,x.paddingRight=P}return v&&(x.flex=rr(v),o===!1&&!x.minWidth&&(x.minWidth=0)),b(h.createElement("div",Object.assign({},y,{style:Object.assign(Object.assign(Object.assign({},x),g),T),className:L,ref:e}),m))});function Zl(t,e){const n=[void 0,void 0],i=Array.isArray(t)?t:[t,void 0],r=e||{xs:!0,sm:!0,md:!0,lg:!0,xl:!0,xxl:!0};return i.forEach((o,s)=>{if(typeof o=="object"&&o!==null)for(let a=0;a<Ue.length;a++){const l=Ue[a];if(r[l]&&o[l]!==void 0){n[s]=o[l];break}}else n[s]=o}),n}var Jl=function(t,e){var n={};for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&e.indexOf(i)<0&&(n[i]=t[i]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,i=Object.getOwnPropertySymbols(t);r<i.length;r++)e.indexOf(i[r])<0&&Object.prototype.propertyIsEnumerable.call(t,i[r])&&(n[i[r]]=t[i[r]]);return n};function sr(t,e){const[n,i]=h.useState(typeof t=="string"?t:""),r=()=>{if(typeof t=="string"&&i(t),typeof t=="object")for(let o=0;o<Ue.length;o++){const s=Ue[o];if(!e||!e[s])continue;const a=t[s];if(a!==void 0){i(a);return}}};return h.useEffect(()=>{r()},[JSON.stringify(t),e]),n}const Rh=h.forwardRef((t,e)=>{const{prefixCls:n,justify:i,align:r,className:o,style:s,children:a,gutter:l=0,wrap:c}=t,u=Jl(t,["prefixCls","justify","align","className","style","children","gutter","wrap"]),{getPrefixCls:d,direction:f}=h.useContext(le),m=Ga(!0,null),v=sr(r,m),g=sr(i,m),y=d("row",n),[p,b,S]=Ka(y),A=Zl(l,m),T=k(y,{[`${y}-no-wrap`]:c===!1,[`${y}-${g}`]:g,[`${y}-${v}`]:v,[`${y}-rtl`]:f==="rtl"},o,b,S),R={},L=A[0]!=null&&A[0]>0?A[0]/-2:void 0;L&&(R.marginLeft=L,R.marginRight=L);const[x,P]=A;R.rowGap=P;const C=h.useMemo(()=>({gutter:[x,P],wrap:c}),[x,P,c]);return p(h.createElement(Ls.Provider,{value:C},h.createElement("div",Object.assign({},u,{className:T,style:Object.assign(Object.assign({},R),s),ref:e}),a)))}),_s=h.createContext({});function Ql(t){const e=h.useRef(null);return e.current===null&&(e.current=t()),e.current}const Yn=h.createContext(null),Os=h.createContext({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"});function tc(t=!0){const e=h.useContext(Yn);if(e===null)return[!0,null];const{isPresent:n,onExitComplete:i,register:r}=e,o=h.useId();h.useEffect(()=>{t&&r(o)},[t]);const s=h.useCallback(()=>t&&i&&i(o),[o,i,t]);return!n&&i?[!1,s]:[!0]}const Zn=typeof window<"u",ec=Zn?h.useLayoutEffect:h.useEffect,dt=t=>t;let Is=dt;function Jn(t){let e;return()=>(e===void 0&&(e=t()),e)}const se=(t,e,n)=>{const i=e-t;return i===0?1:(n-t)/i},Mt=t=>t*1e3,Dt=t=>t/1e3,nc={useManualTiming:!1};function ic(t){let e=new Set,n=new Set,i=!1,r=!1;const o=new WeakSet;let s={delta:0,timestamp:0,isProcessing:!1};function a(c){o.has(c)&&(l.schedule(c),t()),c(s)}const l={schedule:(c,u=!1,d=!1)=>{const m=d&&i?e:n;return u&&o.add(c),m.has(c)||m.add(c),c},cancel:c=>{n.delete(c),o.delete(c)},process:c=>{if(s=c,i){r=!0;return}i=!0,[e,n]=[n,e],e.forEach(a),e.clear(),i=!1,r&&(r=!1,l.process(c))}};return l}const Ie=["read","resolveKeyframes","update","preRender","render","postRender"],rc=40;function Bs(t,e){let n=!1,i=!0;const r={delta:0,timestamp:0,isProcessing:!1},o=()=>n=!0,s=Ie.reduce((p,b)=>(p[b]=ic(o),p),{}),{read:a,resolveKeyframes:l,update:c,preRender:u,render:d,postRender:f}=s,m=()=>{const p=performance.now();n=!1,r.delta=i?1e3/60:Math.max(Math.min(p-r.timestamp,rc),1),r.timestamp=p,r.isProcessing=!0,a.process(r),l.process(r),c.process(r),u.process(r),d.process(r),f.process(r),r.isProcessing=!1,n&&e&&(i=!1,t(m))},v=()=>{n=!0,i=!0,r.isProcessing||t(m)};return{schedule:Ie.reduce((p,b)=>{const S=s[b];return p[b]=(A,T=!1,R=!1)=>(n||v(),S.schedule(A,T,R)),p},{}),cancel:p=>{for(let b=0;b<Ie.length;b++)s[Ie[b]].cancel(p)},state:r,steps:s}}const{schedule:U,cancel:jt,state:rt,steps:gn}=Bs(typeof requestAnimationFrame<"u"?requestAnimationFrame:dt,!0),js=h.createContext({strict:!1}),or={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},oe={};for(const t in or)oe[t]={isEnabled:e=>or[t].some(n=>!!e[n])};function sc(t){for(const e in t)oe[e]={...oe[e],...t[e]}}const oc=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function Ge(t){return t.startsWith("while")||t.startsWith("drag")&&t!=="draggable"||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||oc.has(t)}let Ns=t=>!Ge(t);function ac(t){t&&(Ns=e=>e.startsWith("on")?!Ge(e):t(e))}try{ac(require("@emotion/is-prop-valid").default)}catch{}function lc(t,e,n){const i={};for(const r in t)r==="values"&&typeof t.values=="object"||(Ns(r)||n===!0&&Ge(r)||!e&&!Ge(r)||t.draggable&&r.startsWith("onDrag"))&&(i[r]=t[r]);return i}function cc(t){if(typeof Proxy>"u")return t;const e=new Map,n=(...i)=>t(...i);return new Proxy(n,{get:(i,r)=>r==="create"?t:(e.has(r)||e.set(r,t(r)),e.get(r))})}const tn=h.createContext({});function Te(t){return typeof t=="string"||Array.isArray(t)}function en(t){return t!==null&&typeof t=="object"&&typeof t.start=="function"}const Qn=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],ti=["initial",...Qn];function nn(t){return en(t.animate)||ti.some(e=>Te(t[e]))}function Fs(t){return!!(nn(t)||t.variants)}function uc(t,e){if(nn(t)){const{initial:n,animate:i}=t;return{initial:n===!1||Te(n)?n:void 0,animate:Te(i)?i:void 0}}return t.inherit!==!1?e:{}}function dc(t){const{initial:e,animate:n}=uc(t,h.useContext(tn));return h.useMemo(()=>({initial:e,animate:n}),[ar(e),ar(n)])}function ar(t){return Array.isArray(t)?t.join(" "):t}const fc=Symbol.for("motionComponentSymbol");function te(t){return t&&typeof t=="object"&&Object.prototype.hasOwnProperty.call(t,"current")}function hc(t,e,n){return h.useCallback(i=>{i&&t.onMount&&t.onMount(i),e&&(i?e.mount(i):e.unmount()),n&&(typeof n=="function"?n(i):te(n)&&(n.current=i))},[e])}const ei=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),mc="framerAppearId",zs="data-"+ei(mc),{schedule:ni}=Bs(queueMicrotask,!1),ks=h.createContext({});function pc(t,e,n,i,r){var o,s;const{visualElement:a}=h.useContext(tn),l=h.useContext(js),c=h.useContext(Yn),u=h.useContext(Os).reducedMotion,d=h.useRef(null);i=i||l.renderer,!d.current&&i&&(d.current=i(t,{visualState:e,parent:a,props:n,presenceContext:c,blockInitialAnimation:c?c.initial===!1:!1,reducedMotionConfig:u}));const f=d.current,m=h.useContext(ks);f&&!f.projection&&r&&(f.type==="html"||f.type==="svg")&&gc(d.current,n,r,m);const v=h.useRef(!1);h.useInsertionEffect(()=>{f&&v.current&&f.update(n,c)});const g=n[zs],y=h.useRef(!!g&&!(!((o=window.MotionHandoffIsComplete)===null||o===void 0)&&o.call(window,g))&&((s=window.MotionHasOptimisedAnimation)===null||s===void 0?void 0:s.call(window,g)));return ec(()=>{f&&(v.current=!0,window.MotionIsMounted=!0,f.updateFeatures(),ni.render(f.render),y.current&&f.animationState&&f.animationState.animateChanges())}),h.useEffect(()=>{f&&(!y.current&&f.animationState&&f.animationState.animateChanges(),y.current&&(queueMicrotask(()=>{var p;(p=window.MotionHandoffMarkAsComplete)===null||p===void 0||p.call(window,g)}),y.current=!1))}),f}function gc(t,e,n,i){const{layoutId:r,layout:o,drag:s,dragConstraints:a,layoutScroll:l,layoutRoot:c}=e;t.projection=new n(t.latestValues,e["data-framer-portal-id"]?void 0:Ws(t.parent)),t.projection.setOptions({layoutId:r,layout:o,alwaysMeasureLayout:!!s||a&&te(a),visualElement:t,animationType:typeof o=="string"?o:"both",initialPromotionConfig:i,layoutScroll:l,layoutRoot:c})}function Ws(t){if(t)return t.options.allowProjection!==!1?t.projection:Ws(t.parent)}function vc({preloadedFeatures:t,createVisualElement:e,useRender:n,useVisualState:i,Component:r}){var o,s;t&&sc(t);function a(c,u){let d;const f={...h.useContext(Os),...c,layoutId:yc(c)},{isStatic:m}=f,v=dc(c),g=i(c,m);if(!m&&Zn){bc();const y=Sc(f);d=y.MeasureLayout,v.visualElement=pc(r,g,f,e,y.ProjectionNode)}return En.jsxs(tn.Provider,{value:v,children:[d&&v.visualElement?En.jsx(d,{visualElement:v.visualElement,...f}):null,n(r,c,hc(g,v.visualElement,u),g,m,v.visualElement)]})}a.displayName=`motion.${typeof r=="string"?r:`create(${(s=(o=r.displayName)!==null&&o!==void 0?o:r.name)!==null&&s!==void 0?s:""})`}`;const l=h.forwardRef(a);return l[fc]=r,l}function yc({layoutId:t}){const e=h.useContext(_s).id;return e&&t!==void 0?e+"-"+t:t}function bc(t,e){h.useContext(js).strict}function Sc(t){const{drag:e,layout:n}=oe;if(!e&&!n)return{};const i={...e,...n};return{MeasureLayout:e!=null&&e.isEnabled(t)||n!=null&&n.isEnabled(t)?i.MeasureLayout:void 0,ProjectionNode:i.ProjectionNode}}const xc=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function ii(t){return typeof t!="string"||t.includes("-")?!1:!!(xc.indexOf(t)>-1||/[A-Z]/u.test(t))}function lr(t){const e=[{},{}];return t==null||t.values.forEach((n,i)=>{e[0][i]=n.get(),e[1][i]=n.getVelocity()}),e}function ri(t,e,n,i){if(typeof e=="function"){const[r,o]=lr(i);e=e(n!==void 0?n:t.custom,r,o)}if(typeof e=="string"&&(e=t.variants&&t.variants[e]),typeof e=="function"){const[r,o]=lr(i);e=e(n!==void 0?n:t.custom,r,o)}return e}const Mn=t=>Array.isArray(t),Tc=t=>!!(t&&typeof t=="object"&&t.mix&&t.toValue),Pc=t=>Mn(t)?t[t.length-1]||0:t,at=t=>!!(t&&t.getVelocity);function Fe(t){const e=at(t)?t.get():t;return Tc(e)?e.toValue():e}function Cc({scrapeMotionValuesFromProps:t,createRenderState:e,onUpdate:n},i,r,o){const s={latestValues:wc(i,r,o,t),renderState:e()};return n&&(s.onMount=a=>n({props:i,current:a,...s}),s.onUpdate=a=>n(a)),s}const Us=t=>(e,n)=>{const i=h.useContext(tn),r=h.useContext(Yn),o=()=>Cc(t,e,i,r);return n?o():Ql(o)};function wc(t,e,n,i){const r={},o=i(t,{});for(const f in o)r[f]=Fe(o[f]);let{initial:s,animate:a}=t;const l=nn(t),c=Fs(t);e&&c&&!l&&t.inherit!==!1&&(s===void 0&&(s=e.initial),a===void 0&&(a=e.animate));let u=n?n.initial===!1:!1;u=u||s===!1;const d=u?a:s;if(d&&typeof d!="boolean"&&!en(d)){const f=Array.isArray(d)?d:[d];for(let m=0;m<f.length;m++){const v=ri(t,f[m]);if(v){const{transitionEnd:g,transition:y,...p}=v;for(const b in p){let S=p[b];if(Array.isArray(S)){const A=u?S.length-1:0;S=S[A]}S!==null&&(r[b]=S)}for(const b in g)r[b]=g[b]}}}return r}const ce=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],Xt=new Set(ce),Gs=t=>e=>typeof e=="string"&&e.startsWith(t),Ks=Gs("--"),$c=Gs("var(--"),si=t=>$c(t)?Ac.test(t.split("/*")[0].trim()):!1,Ac=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,Hs=(t,e)=>e&&typeof t=="number"?e.transform(t):t,Vt=(t,e,n)=>n>e?e:n<t?t:n,ue={test:t=>typeof t=="number",parse:parseFloat,transform:t=>t},Pe={...ue,transform:t=>Vt(0,1,t)},Be={...ue,default:1},Ae=t=>({test:e=>typeof e=="string"&&e.endsWith(t)&&e.split(" ").length===1,parse:parseFloat,transform:e=>`${e}${t}`}),Bt=Ae("deg"),wt=Ae("%"),D=Ae("px"),Rc=Ae("vh"),Ec=Ae("vw"),cr={...wt,parse:t=>wt.parse(t)/100,transform:t=>wt.transform(t*100)},Mc={borderWidth:D,borderTopWidth:D,borderRightWidth:D,borderBottomWidth:D,borderLeftWidth:D,borderRadius:D,radius:D,borderTopLeftRadius:D,borderTopRightRadius:D,borderBottomRightRadius:D,borderBottomLeftRadius:D,width:D,maxWidth:D,height:D,maxHeight:D,top:D,right:D,bottom:D,left:D,padding:D,paddingTop:D,paddingRight:D,paddingBottom:D,paddingLeft:D,margin:D,marginTop:D,marginRight:D,marginBottom:D,marginLeft:D,backgroundPositionX:D,backgroundPositionY:D},Dc={rotate:Bt,rotateX:Bt,rotateY:Bt,rotateZ:Bt,scale:Be,scaleX:Be,scaleY:Be,scaleZ:Be,skew:Bt,skewX:Bt,skewY:Bt,distance:D,translateX:D,translateY:D,translateZ:D,x:D,y:D,z:D,perspective:D,transformPerspective:D,opacity:Pe,originX:cr,originY:cr,originZ:D},ur={...ue,transform:Math.round},oi={...Mc,...Dc,zIndex:ur,size:D,fillOpacity:Pe,strokeOpacity:Pe,numOctaves:ur},Vc={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},Lc=ce.length;function _c(t,e,n){let i="",r=!0;for(let o=0;o<Lc;o++){const s=ce[o],a=t[s];if(a===void 0)continue;let l=!0;if(typeof a=="number"?l=a===(s.startsWith("scale")?1:0):l=parseFloat(a)===0,!l||n){const c=Hs(a,oi[s]);if(!l){r=!1;const u=Vc[s]||s;i+=`${u}(${c}) `}n&&(e[s]=c)}}return i=i.trim(),n?i=n(e,r?"":i):r&&(i="none"),i}function ai(t,e,n){const{style:i,vars:r,transformOrigin:o}=t;let s=!1,a=!1;for(const l in e){const c=e[l];if(Xt.has(l)){s=!0;continue}else if(Ks(l)){r[l]=c;continue}else{const u=Hs(c,oi[l]);l.startsWith("origin")?(a=!0,o[l]=u):i[l]=u}}if(e.transform||(s||n?i.transform=_c(e,t.transform,n):i.transform&&(i.transform="none")),a){const{originX:l="50%",originY:c="50%",originZ:u=0}=o;i.transformOrigin=`${l} ${c} ${u}`}}const Oc={offset:"stroke-dashoffset",array:"stroke-dasharray"},Ic={offset:"strokeDashoffset",array:"strokeDasharray"};function Bc(t,e,n=1,i=0,r=!0){t.pathLength=1;const o=r?Oc:Ic;t[o.offset]=D.transform(-i);const s=D.transform(e),a=D.transform(n);t[o.array]=`${s} ${a}`}function dr(t,e,n){return typeof t=="string"?t:D.transform(e+n*t)}function jc(t,e,n){const i=dr(e,t.x,t.width),r=dr(n,t.y,t.height);return`${i} ${r}`}function li(t,{attrX:e,attrY:n,attrScale:i,originX:r,originY:o,pathLength:s,pathSpacing:a=1,pathOffset:l=0,...c},u,d){if(ai(t,c,d),u){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};const{attrs:f,style:m,dimensions:v}=t;f.transform&&(v&&(m.transform=f.transform),delete f.transform),v&&(r!==void 0||o!==void 0||m.transform)&&(m.transformOrigin=jc(v,r!==void 0?r:.5,o!==void 0?o:.5)),e!==void 0&&(f.x=e),n!==void 0&&(f.y=n),i!==void 0&&(f.scale=i),s!==void 0&&Bc(f,s,a,l,!1)}const ci=()=>({style:{},transform:{},transformOrigin:{},vars:{}}),Xs=()=>({...ci(),attrs:{}}),ui=t=>typeof t=="string"&&t.toLowerCase()==="svg";function qs(t,{style:e,vars:n},i,r){Object.assign(t.style,e,r&&r.getProjectionStyles(i));for(const o in n)t.style.setProperty(o,n[o])}const Ys=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function Zs(t,e,n,i){qs(t,e,void 0,i);for(const r in e.attrs)t.setAttribute(Ys.has(r)?r:ei(r),e.attrs[r])}const Ke={};function Nc(t){Object.assign(Ke,t)}function Js(t,{layout:e,layoutId:n}){return Xt.has(t)||t.startsWith("origin")||(e||n!==void 0)&&(!!Ke[t]||t==="opacity")}function di(t,e,n){var i;const{style:r}=t,o={};for(const s in r)(at(r[s])||e.style&&at(e.style[s])||Js(s,t)||((i=n==null?void 0:n.getValue(s))===null||i===void 0?void 0:i.liveStyle)!==void 0)&&(o[s]=r[s]);return o}function Qs(t,e,n){const i=di(t,e,n);for(const r in t)if(at(t[r])||at(e[r])){const o=ce.indexOf(r)!==-1?"attr"+r.charAt(0).toUpperCase()+r.substring(1):r;i[o]=t[r]}return i}function Fc(t,e){try{e.dimensions=typeof t.getBBox=="function"?t.getBBox():t.getBoundingClientRect()}catch{e.dimensions={x:0,y:0,width:0,height:0}}}const fr=["x","y","width","height","cx","cy","r"],zc={useVisualState:Us({scrapeMotionValuesFromProps:Qs,createRenderState:Xs,onUpdate:({props:t,prevProps:e,current:n,renderState:i,latestValues:r})=>{if(!n)return;let o=!!t.drag;if(!o){for(const a in r)if(Xt.has(a)){o=!0;break}}if(!o)return;let s=!e;if(e)for(let a=0;a<fr.length;a++){const l=fr[a];t[l]!==e[l]&&(s=!0)}s&&U.read(()=>{Fc(n,i),U.render(()=>{li(i,r,ui(n.tagName),t.transformTemplate),Zs(n,i)})})}})},kc={useVisualState:Us({scrapeMotionValuesFromProps:di,createRenderState:ci})};function to(t,e,n){for(const i in e)!at(e[i])&&!Js(i,n)&&(t[i]=e[i])}function Wc({transformTemplate:t},e){return h.useMemo(()=>{const n=ci();return ai(n,e,t),Object.assign({},n.vars,n.style)},[e])}function Uc(t,e){const n=t.style||{},i={};return to(i,n,t),Object.assign(i,Wc(t,e)),i}function Gc(t,e){const n={},i=Uc(t,e);return t.drag&&t.dragListener!==!1&&(n.draggable=!1,i.userSelect=i.WebkitUserSelect=i.WebkitTouchCallout="none",i.touchAction=t.drag===!0?"none":`pan-${t.drag==="x"?"y":"x"}`),t.tabIndex===void 0&&(t.onTap||t.onTapStart||t.whileTap)&&(n.tabIndex=0),n.style=i,n}function Kc(t,e,n,i){const r=h.useMemo(()=>{const o=Xs();return li(o,e,ui(i),t.transformTemplate),{...o.attrs,style:{...o.style}}},[e]);if(t.style){const o={};to(o,t.style,t),r.style={...o,...r.style}}return r}function Hc(t=!1){return(n,i,r,{latestValues:o},s)=>{const l=(ii(n)?Kc:Gc)(i,o,s,n),c=lc(i,typeof n=="string",t),u=n!==h.Fragment?{...c,...l,ref:r}:{},{children:d}=i,f=h.useMemo(()=>at(d)?d.get():d,[d]);return h.createElement(n,{...u,children:f})}}function Xc(t,e){return function(i,{forwardMotionProps:r}={forwardMotionProps:!1}){const s={...ii(i)?zc:kc,preloadedFeatures:t,useRender:Hc(r),createVisualElement:e,Component:i};return vc(s)}}function eo(t,e){if(!Array.isArray(e))return!1;const n=e.length;if(n!==t.length)return!1;for(let i=0;i<n;i++)if(e[i]!==t[i])return!1;return!0}function rn(t,e,n){const i=t.getProps();return ri(i,e,n!==void 0?n:i.custom,t)}const qc=Jn(()=>window.ScrollTimeline!==void 0);class Yc{constructor(e){this.stop=()=>this.runAll("stop"),this.animations=e.filter(Boolean)}get finished(){return Promise.all(this.animations.map(e=>"finished"in e?e.finished:e))}getAll(e){return this.animations[0][e]}setAll(e,n){for(let i=0;i<this.animations.length;i++)this.animations[i][e]=n}attachTimeline(e,n){const i=this.animations.map(r=>{if(qc()&&r.attachTimeline)return r.attachTimeline(e);if(typeof n=="function")return n(r)});return()=>{i.forEach((r,o)=>{r&&r(),this.animations[o].stop()})}}get time(){return this.getAll("time")}set time(e){this.setAll("time",e)}get speed(){return this.getAll("speed")}set speed(e){this.setAll("speed",e)}get startTime(){return this.getAll("startTime")}get duration(){let e=0;for(let n=0;n<this.animations.length;n++)e=Math.max(e,this.animations[n].duration);return e}runAll(e){this.animations.forEach(n=>n[e]())}flatten(){this.runAll("flatten")}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}class Zc extends Yc{then(e,n){return Promise.all(this.animations).then(e).catch(n)}}function fi(t,e){return t?t[e]||t.default||t:void 0}const Dn=2e4;function no(t){let e=0;const n=50;let i=t.next(e);for(;!i.done&&e<Dn;)e+=n,i=t.next(e);return e>=Dn?1/0:e}function hi(t){return typeof t=="function"}function hr(t,e){t.timeline=e,t.onfinish=null}const mi=t=>Array.isArray(t)&&typeof t[0]=="number",Jc={linearEasing:void 0};function Qc(t,e){const n=Jn(t);return()=>{var i;return(i=Jc[e])!==null&&i!==void 0?i:n()}}const He=Qc(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch{return!1}return!0},"linearEasing"),io=(t,e,n=10)=>{let i="";const r=Math.max(Math.round(e/n),2);for(let o=0;o<r;o++)i+=t(se(0,r-1,o))+", ";return`linear(${i.substring(0,i.length-2)})`};function ro(t){return!!(typeof t=="function"&&He()||!t||typeof t=="string"&&(t in Vn||He())||mi(t)||Array.isArray(t)&&t.every(ro))}const pe=([t,e,n,i])=>`cubic-bezier(${t}, ${e}, ${n}, ${i})`,Vn={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:pe([0,.65,.55,1]),circOut:pe([.55,0,1,.45]),backIn:pe([.31,.01,.66,-.59]),backOut:pe([.33,1.53,.69,.99])};function so(t,e){if(t)return typeof t=="function"&&He()?io(t,e):mi(t)?pe(t):Array.isArray(t)?t.map(n=>so(n,e)||Vn.easeOut):Vn[t]}const St={x:!1,y:!1};function oo(){return St.x||St.y}function tu(t,e,n){var i;if(t instanceof Element)return[t];if(typeof t=="string"){let r=document;const o=(i=void 0)!==null&&i!==void 0?i:r.querySelectorAll(t);return o?Array.from(o):[]}return Array.from(t)}function ao(t,e){const n=tu(t),i=new AbortController,r={passive:!0,...e,signal:i.signal};return[n,r,()=>i.abort()]}function mr(t){return e=>{e.pointerType==="touch"||oo()||t(e)}}function eu(t,e,n={}){const[i,r,o]=ao(t,n),s=mr(a=>{const{target:l}=a,c=e(a);if(typeof c!="function"||!l)return;const u=mr(d=>{c(d),l.removeEventListener("pointerleave",u)});l.addEventListener("pointerleave",u,r)});return i.forEach(a=>{a.addEventListener("pointerenter",s,r)}),o}const lo=(t,e)=>e?t===e?!0:lo(t,e.parentElement):!1,pi=t=>t.pointerType==="mouse"?typeof t.button!="number"||t.button<=0:t.isPrimary!==!1,nu=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);function iu(t){return nu.has(t.tagName)||t.tabIndex!==-1}const ge=new WeakSet;function pr(t){return e=>{e.key==="Enter"&&t(e)}}function vn(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}const ru=(t,e)=>{const n=t.currentTarget;if(!n)return;const i=pr(()=>{if(ge.has(n))return;vn(n,"down");const r=pr(()=>{vn(n,"up")}),o=()=>vn(n,"cancel");n.addEventListener("keyup",r,e),n.addEventListener("blur",o,e)});n.addEventListener("keydown",i,e),n.addEventListener("blur",()=>n.removeEventListener("keydown",i),e)};function gr(t){return pi(t)&&!oo()}function su(t,e,n={}){const[i,r,o]=ao(t,n),s=a=>{const l=a.currentTarget;if(!gr(a)||ge.has(l))return;ge.add(l);const c=e(a),u=(m,v)=>{window.removeEventListener("pointerup",d),window.removeEventListener("pointercancel",f),!(!gr(m)||!ge.has(l))&&(ge.delete(l),typeof c=="function"&&c(m,{success:v}))},d=m=>{u(m,n.useGlobalTarget||lo(l,m.target))},f=m=>{u(m,!1)};window.addEventListener("pointerup",d,r),window.addEventListener("pointercancel",f,r)};return i.forEach(a=>{!iu(a)&&a.getAttribute("tabindex")===null&&(a.tabIndex=0),(n.useGlobalTarget?window:a).addEventListener("pointerdown",s,r),a.addEventListener("focus",c=>ru(c,r),r)}),o}function ou(t){return t==="x"||t==="y"?St[t]?null:(St[t]=!0,()=>{St[t]=!1}):St.x||St.y?null:(St.x=St.y=!0,()=>{St.x=St.y=!1})}const co=new Set(["width","height","top","left","right","bottom",...ce]);let ze;function au(){ze=void 0}const $t={now:()=>(ze===void 0&&$t.set(rt.isProcessing||nc.useManualTiming?rt.timestamp:performance.now()),ze),set:t=>{ze=t,queueMicrotask(au)}};function gi(t,e){t.indexOf(e)===-1&&t.push(e)}function vi(t,e){const n=t.indexOf(e);n>-1&&t.splice(n,1)}class yi{constructor(){this.subscriptions=[]}add(e){return gi(this.subscriptions,e),()=>vi(this.subscriptions,e)}notify(e,n,i){const r=this.subscriptions.length;if(r)if(r===1)this.subscriptions[0](e,n,i);else for(let o=0;o<r;o++){const s=this.subscriptions[o];s&&s(e,n,i)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function uo(t,e){return e?t*(1e3/e):0}const vr=30,lu=t=>!isNaN(parseFloat(t));class cu{constructor(e,n={}){this.version="11.18.2",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(i,r=!0)=>{const o=$t.now();this.updatedAt!==o&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(i),this.current!==this.prev&&this.events.change&&this.events.change.notify(this.current),r&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.hasAnimated=!1,this.setCurrent(e),this.owner=n.owner}setCurrent(e){this.current=e,this.updatedAt=$t.now(),this.canTrackVelocity===null&&e!==void 0&&(this.canTrackVelocity=lu(this.current))}setPrevFrameValue(e=this.current){this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt}onChange(e){return this.on("change",e)}on(e,n){this.events[e]||(this.events[e]=new yi);const i=this.events[e].add(n);return e==="change"?()=>{i(),U.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(const e in this.events)this.events[e].clear()}attach(e,n){this.passiveEffect=e,this.stopPassiveEffect=n}set(e,n=!0){!n||!this.passiveEffect?this.updateAndNotify(e,n):this.passiveEffect(e,this.updateAndNotify)}setWithVelocity(e,n,i){this.set(n),this.prev=void 0,this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt-i}jump(e,n=!0){this.updateAndNotify(e),this.prev=e,this.prevUpdatedAt=this.prevFrameValue=void 0,n&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const e=$t.now();if(!this.canTrackVelocity||this.prevFrameValue===void 0||e-this.updatedAt>vr)return 0;const n=Math.min(this.updatedAt-this.prevUpdatedAt,vr);return uo(parseFloat(this.current)-parseFloat(this.prevFrameValue),n)}start(e){return this.stop(),new Promise(n=>{this.hasAnimated=!0,this.animation=e(n),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function Ce(t,e){return new cu(t,e)}function uu(t,e,n){t.hasValue(e)?t.getValue(e).set(n):t.addValue(e,Ce(n))}function du(t,e){const n=rn(t,e);let{transitionEnd:i={},transition:r={},...o}=n||{};o={...o,...i};for(const s in o){const a=Pc(o[s]);uu(t,s,a)}}function fu(t){return!!(at(t)&&t.add)}function Ln(t,e){const n=t.getValue("willChange");if(fu(n))return n.add(e)}function fo(t){return t.props[zs]}const ho=(t,e,n)=>(((1-3*n+3*e)*t+(3*n-6*e))*t+3*e)*t,hu=1e-7,mu=12;function pu(t,e,n,i,r){let o,s,a=0;do s=e+(n-e)/2,o=ho(s,i,r)-t,o>0?n=s:e=s;while(Math.abs(o)>hu&&++a<mu);return s}function Re(t,e,n,i){if(t===e&&n===i)return dt;const r=o=>pu(o,0,1,t,n);return o=>o===0||o===1?o:ho(r(o),e,i)}const mo=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,po=t=>e=>1-t(1-e),go=Re(.33,1.53,.69,.99),bi=po(go),vo=mo(bi),yo=t=>(t*=2)<1?.5*bi(t):.5*(2-Math.pow(2,-10*(t-1))),Si=t=>1-Math.sin(Math.acos(t)),bo=po(Si),So=mo(Si),xo=t=>/^0[^.\s]+$/u.test(t);function gu(t){return typeof t=="number"?t===0:t!==null?t==="none"||t==="0"||xo(t):!0}const ye=t=>Math.round(t*1e5)/1e5,xi=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;function vu(t){return t==null}const yu=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,Ti=(t,e)=>n=>!!(typeof n=="string"&&yu.test(n)&&n.startsWith(t)||e&&!vu(n)&&Object.prototype.hasOwnProperty.call(n,e)),To=(t,e,n)=>i=>{if(typeof i!="string")return i;const[r,o,s,a]=i.match(xi);return{[t]:parseFloat(r),[e]:parseFloat(o),[n]:parseFloat(s),alpha:a!==void 0?parseFloat(a):1}},bu=t=>Vt(0,255,t),yn={...ue,transform:t=>Math.round(bu(t))},Kt={test:Ti("rgb","red"),parse:To("red","green","blue"),transform:({red:t,green:e,blue:n,alpha:i=1})=>"rgba("+yn.transform(t)+", "+yn.transform(e)+", "+yn.transform(n)+", "+ye(Pe.transform(i))+")"};function Su(t){let e="",n="",i="",r="";return t.length>5?(e=t.substring(1,3),n=t.substring(3,5),i=t.substring(5,7),r=t.substring(7,9)):(e=t.substring(1,2),n=t.substring(2,3),i=t.substring(3,4),r=t.substring(4,5),e+=e,n+=n,i+=i,r+=r),{red:parseInt(e,16),green:parseInt(n,16),blue:parseInt(i,16),alpha:r?parseInt(r,16)/255:1}}const _n={test:Ti("#"),parse:Su,transform:Kt.transform},ee={test:Ti("hsl","hue"),parse:To("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:n,alpha:i=1})=>"hsla("+Math.round(t)+", "+wt.transform(ye(e))+", "+wt.transform(ye(n))+", "+ye(Pe.transform(i))+")"},ot={test:t=>Kt.test(t)||_n.test(t)||ee.test(t),parse:t=>Kt.test(t)?Kt.parse(t):ee.test(t)?ee.parse(t):_n.parse(t),transform:t=>typeof t=="string"?t:t.hasOwnProperty("red")?Kt.transform(t):ee.transform(t)},xu=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;function Tu(t){var e,n;return isNaN(t)&&typeof t=="string"&&(((e=t.match(xi))===null||e===void 0?void 0:e.length)||0)+(((n=t.match(xu))===null||n===void 0?void 0:n.length)||0)>0}const Po="number",Co="color",Pu="var",Cu="var(",yr="${}",wu=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function we(t){const e=t.toString(),n=[],i={color:[],number:[],var:[]},r=[];let o=0;const a=e.replace(wu,l=>(ot.test(l)?(i.color.push(o),r.push(Co),n.push(ot.parse(l))):l.startsWith(Cu)?(i.var.push(o),r.push(Pu),n.push(l)):(i.number.push(o),r.push(Po),n.push(parseFloat(l))),++o,yr)).split(yr);return{values:n,split:a,indexes:i,types:r}}function wo(t){return we(t).values}function $o(t){const{split:e,types:n}=we(t),i=e.length;return r=>{let o="";for(let s=0;s<i;s++)if(o+=e[s],r[s]!==void 0){const a=n[s];a===Po?o+=ye(r[s]):a===Co?o+=ot.transform(r[s]):o+=r[s]}return o}}const $u=t=>typeof t=="number"?0:t;function Au(t){const e=wo(t);return $o(t)(e.map($u))}const Nt={test:Tu,parse:wo,createTransformer:$o,getAnimatableNone:Au},Ru=new Set(["brightness","contrast","saturate","opacity"]);function Eu(t){const[e,n]=t.slice(0,-1).split("(");if(e==="drop-shadow")return t;const[i]=n.match(xi)||[];if(!i)return t;const r=n.replace(i,"");let o=Ru.has(e)?1:0;return i!==n&&(o*=100),e+"("+o+r+")"}const Mu=/\b([a-z-]*)\(.*?\)/gu,On={...Nt,getAnimatableNone:t=>{const e=t.match(Mu);return e?e.map(Eu).join(" "):t}},Du={...oi,color:ot,backgroundColor:ot,outlineColor:ot,fill:ot,stroke:ot,borderColor:ot,borderTopColor:ot,borderRightColor:ot,borderBottomColor:ot,borderLeftColor:ot,filter:On,WebkitFilter:On},Pi=t=>Du[t];function Ao(t,e){let n=Pi(t);return n!==On&&(n=Nt),n.getAnimatableNone?n.getAnimatableNone(e):void 0}const Vu=new Set(["auto","none","0"]);function Lu(t,e,n){let i=0,r;for(;i<t.length&&!r;){const o=t[i];typeof o=="string"&&!Vu.has(o)&&we(o).values.length&&(r=t[i]),i++}if(r&&n)for(const o of e)t[o]=Ao(n,r)}const br=t=>t===ue||t===D,Sr=(t,e)=>parseFloat(t.split(", ")[e]),xr=(t,e)=>(n,{transform:i})=>{if(i==="none"||!i)return 0;const r=i.match(/^matrix3d\((.+)\)$/u);if(r)return Sr(r[1],e);{const o=i.match(/^matrix\((.+)\)$/u);return o?Sr(o[1],t):0}},_u=new Set(["x","y","z"]),Ou=ce.filter(t=>!_u.has(t));function Iu(t){const e=[];return Ou.forEach(n=>{const i=t.getValue(n);i!==void 0&&(e.push([n,i.get()]),i.set(n.startsWith("scale")?1:0))}),e}const ae={width:({x:t},{paddingLeft:e="0",paddingRight:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),height:({y:t},{paddingTop:e="0",paddingBottom:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:xr(4,13),y:xr(5,14)};ae.translateX=ae.x;ae.translateY=ae.y;const Ht=new Set;let In=!1,Bn=!1;function Ro(){if(Bn){const t=Array.from(Ht).filter(i=>i.needsMeasurement),e=new Set(t.map(i=>i.element)),n=new Map;e.forEach(i=>{const r=Iu(i);r.length&&(n.set(i,r),i.render())}),t.forEach(i=>i.measureInitialState()),e.forEach(i=>{i.render();const r=n.get(i);r&&r.forEach(([o,s])=>{var a;(a=i.getValue(o))===null||a===void 0||a.set(s)})}),t.forEach(i=>i.measureEndState()),t.forEach(i=>{i.suspendedScrollY!==void 0&&window.scrollTo(0,i.suspendedScrollY)})}Bn=!1,In=!1,Ht.forEach(t=>t.complete()),Ht.clear()}function Eo(){Ht.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(Bn=!0)})}function Bu(){Eo(),Ro()}class Ci{constructor(e,n,i,r,o,s=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...e],this.onComplete=n,this.name=i,this.motionValue=r,this.element=o,this.isAsync=s}scheduleResolve(){this.isScheduled=!0,this.isAsync?(Ht.add(this),In||(In=!0,U.read(Eo),U.resolveKeyframes(Ro))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:e,name:n,element:i,motionValue:r}=this;for(let o=0;o<e.length;o++)if(e[o]===null)if(o===0){const s=r==null?void 0:r.get(),a=e[e.length-1];if(s!==void 0)e[0]=s;else if(i&&n){const l=i.readValue(n,a);l!=null&&(e[0]=l)}e[0]===void 0&&(e[0]=a),r&&s===void 0&&r.set(e[0])}else e[o]=e[o-1]}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe),Ht.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,Ht.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}const Mo=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),ju=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function Nu(t){const e=ju.exec(t);if(!e)return[,];const[,n,i,r]=e;return[`--${n??i}`,r]}function Do(t,e,n=1){const[i,r]=Nu(t);if(!i)return;const o=window.getComputedStyle(e).getPropertyValue(i);if(o){const s=o.trim();return Mo(s)?parseFloat(s):s}return si(r)?Do(r,e,n+1):r}const Vo=t=>e=>e.test(t),Fu={test:t=>t==="auto",parse:t=>t},Lo=[ue,D,wt,Bt,Ec,Rc,Fu],Tr=t=>Lo.find(Vo(t));class _o extends Ci{constructor(e,n,i,r,o){super(e,n,i,r,o,!0)}readKeyframes(){const{unresolvedKeyframes:e,element:n,name:i}=this;if(!n||!n.current)return;super.readKeyframes();for(let l=0;l<e.length;l++){let c=e[l];if(typeof c=="string"&&(c=c.trim(),si(c))){const u=Do(c,n.current);u!==void 0&&(e[l]=u),l===e.length-1&&(this.finalKeyframe=c)}}if(this.resolveNoneKeyframes(),!co.has(i)||e.length!==2)return;const[r,o]=e,s=Tr(r),a=Tr(o);if(s!==a)if(br(s)&&br(a))for(let l=0;l<e.length;l++){const c=e[l];typeof c=="string"&&(e[l]=parseFloat(c))}else this.needsMeasurement=!0}resolveNoneKeyframes(){const{unresolvedKeyframes:e,name:n}=this,i=[];for(let r=0;r<e.length;r++)gu(e[r])&&i.push(r);i.length&&Lu(e,i,n)}measureInitialState(){const{element:e,unresolvedKeyframes:n,name:i}=this;if(!e||!e.current)return;i==="height"&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=ae[i](e.measureViewportBox(),window.getComputedStyle(e.current)),n[0]=this.measuredOrigin;const r=n[n.length-1];r!==void 0&&e.getValue(i,r).jump(r,!1)}measureEndState(){var e;const{element:n,name:i,unresolvedKeyframes:r}=this;if(!n||!n.current)return;const o=n.getValue(i);o&&o.jump(this.measuredOrigin,!1);const s=r.length-1,a=r[s];r[s]=ae[i](n.measureViewportBox(),window.getComputedStyle(n.current)),a!==null&&this.finalKeyframe===void 0&&(this.finalKeyframe=a),!((e=this.removedTransforms)===null||e===void 0)&&e.length&&this.removedTransforms.forEach(([l,c])=>{n.getValue(l).set(c)}),this.resolveNoneKeyframes()}}const Pr=(t,e)=>e==="zIndex"?!1:!!(typeof t=="number"||Array.isArray(t)||typeof t=="string"&&(Nt.test(t)||t==="0")&&!t.startsWith("url("));function zu(t){const e=t[0];if(t.length===1)return!0;for(let n=0;n<t.length;n++)if(t[n]!==e)return!0}function ku(t,e,n,i){const r=t[0];if(r===null)return!1;if(e==="display"||e==="visibility")return!0;const o=t[t.length-1],s=Pr(r,e),a=Pr(o,e);return!s||!a?!1:zu(t)||(n==="spring"||hi(n))&&i}const Wu=t=>t!==null;function sn(t,{repeat:e,repeatType:n="loop"},i){const r=t.filter(Wu),o=e&&n!=="loop"&&e%2===1?0:r.length-1;return!o||i===void 0?r[o]:i}const Uu=40;class Oo{constructor({autoplay:e=!0,delay:n=0,type:i="keyframes",repeat:r=0,repeatDelay:o=0,repeatType:s="loop",...a}){this.isStopped=!1,this.hasAttemptedResolve=!1,this.createdAt=$t.now(),this.options={autoplay:e,delay:n,type:i,repeat:r,repeatDelay:o,repeatType:s,...a},this.updateFinishedPromise()}calcStartTime(){return this.resolvedAt?this.resolvedAt-this.createdAt>Uu?this.resolvedAt:this.createdAt:this.createdAt}get resolved(){return!this._resolved&&!this.hasAttemptedResolve&&Bu(),this._resolved}onKeyframesResolved(e,n){this.resolvedAt=$t.now(),this.hasAttemptedResolve=!0;const{name:i,type:r,velocity:o,delay:s,onComplete:a,onUpdate:l,isGenerator:c}=this.options;if(!c&&!ku(e,i,r,o))if(s)this.options.duration=0;else{l&&l(sn(e,this.options,n)),a&&a(),this.resolveFinishedPromise();return}const u=this.initPlayback(e,n);u!==!1&&(this._resolved={keyframes:e,finalKeyframe:n,...u},this.onPostResolved())}onPostResolved(){}then(e,n){return this.currentFinishedPromise.then(e,n)}flatten(){this.options.type="keyframes",this.options.ease="linear"}updateFinishedPromise(){this.currentFinishedPromise=new Promise(e=>{this.resolveFinishedPromise=e})}}const q=(t,e,n)=>t+(e-t)*n;function bn(t,e,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?t+(e-t)*6*n:n<1/2?e:n<2/3?t+(e-t)*(2/3-n)*6:t}function Gu({hue:t,saturation:e,lightness:n,alpha:i}){t/=360,e/=100,n/=100;let r=0,o=0,s=0;if(!e)r=o=s=n;else{const a=n<.5?n*(1+e):n+e-n*e,l=2*n-a;r=bn(l,a,t+1/3),o=bn(l,a,t),s=bn(l,a,t-1/3)}return{red:Math.round(r*255),green:Math.round(o*255),blue:Math.round(s*255),alpha:i}}function Xe(t,e){return n=>n>0?e:t}const Sn=(t,e,n)=>{const i=t*t,r=n*(e*e-i)+i;return r<0?0:Math.sqrt(r)},Ku=[_n,Kt,ee],Hu=t=>Ku.find(e=>e.test(t));function Cr(t){const e=Hu(t);if(!e)return!1;let n=e.parse(t);return e===ee&&(n=Gu(n)),n}const wr=(t,e)=>{const n=Cr(t),i=Cr(e);if(!n||!i)return Xe(t,e);const r={...n};return o=>(r.red=Sn(n.red,i.red,o),r.green=Sn(n.green,i.green,o),r.blue=Sn(n.blue,i.blue,o),r.alpha=q(n.alpha,i.alpha,o),Kt.transform(r))},Xu=(t,e)=>n=>e(t(n)),Ee=(...t)=>t.reduce(Xu),jn=new Set(["none","hidden"]);function qu(t,e){return jn.has(t)?n=>n<=0?t:e:n=>n>=1?e:t}function Yu(t,e){return n=>q(t,e,n)}function wi(t){return typeof t=="number"?Yu:typeof t=="string"?si(t)?Xe:ot.test(t)?wr:Qu:Array.isArray(t)?Io:typeof t=="object"?ot.test(t)?wr:Zu:Xe}function Io(t,e){const n=[...t],i=n.length,r=t.map((o,s)=>wi(o)(o,e[s]));return o=>{for(let s=0;s<i;s++)n[s]=r[s](o);return n}}function Zu(t,e){const n={...t,...e},i={};for(const r in n)t[r]!==void 0&&e[r]!==void 0&&(i[r]=wi(t[r])(t[r],e[r]));return r=>{for(const o in i)n[o]=i[o](r);return n}}function Ju(t,e){var n;const i=[],r={color:0,var:0,number:0};for(let o=0;o<e.values.length;o++){const s=e.types[o],a=t.indexes[s][r[s]],l=(n=t.values[a])!==null&&n!==void 0?n:0;i[o]=l,r[s]++}return i}const Qu=(t,e)=>{const n=Nt.createTransformer(e),i=we(t),r=we(e);return i.indexes.var.length===r.indexes.var.length&&i.indexes.color.length===r.indexes.color.length&&i.indexes.number.length>=r.indexes.number.length?jn.has(t)&&!r.values.length||jn.has(e)&&!i.values.length?qu(t,e):Ee(Io(Ju(i,r),r.values),n):Xe(t,e)};function Bo(t,e,n){return typeof t=="number"&&typeof e=="number"&&typeof n=="number"?q(t,e,n):wi(t)(t,e)}const td=5;function jo(t,e,n){const i=Math.max(e-td,0);return uo(n-t(i),e-i)}const Z={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1},$r=.001;function ed({duration:t=Z.duration,bounce:e=Z.bounce,velocity:n=Z.velocity,mass:i=Z.mass}){let r,o,s=1-e;s=Vt(Z.minDamping,Z.maxDamping,s),t=Vt(Z.minDuration,Z.maxDuration,Dt(t)),s<1?(r=c=>{const u=c*s,d=u*t,f=u-n,m=Nn(c,s),v=Math.exp(-d);return $r-f/m*v},o=c=>{const d=c*s*t,f=d*n+n,m=Math.pow(s,2)*Math.pow(c,2)*t,v=Math.exp(-d),g=Nn(Math.pow(c,2),s);return(-r(c)+$r>0?-1:1)*((f-m)*v)/g}):(r=c=>{const u=Math.exp(-c*t),d=(c-n)*t+1;return-.001+u*d},o=c=>{const u=Math.exp(-c*t),d=(n-c)*(t*t);return u*d});const a=5/t,l=id(r,o,a);if(t=Mt(t),isNaN(l))return{stiffness:Z.stiffness,damping:Z.damping,duration:t};{const c=Math.pow(l,2)*i;return{stiffness:c,damping:s*2*Math.sqrt(i*c),duration:t}}}const nd=12;function id(t,e,n){let i=n;for(let r=1;r<nd;r++)i=i-t(i)/e(i);return i}function Nn(t,e){return t*Math.sqrt(1-e*e)}const rd=["duration","bounce"],sd=["stiffness","damping","mass"];function Ar(t,e){return e.some(n=>t[n]!==void 0)}function od(t){let e={velocity:Z.velocity,stiffness:Z.stiffness,damping:Z.damping,mass:Z.mass,isResolvedFromDuration:!1,...t};if(!Ar(t,sd)&&Ar(t,rd))if(t.visualDuration){const n=t.visualDuration,i=2*Math.PI/(n*1.2),r=i*i,o=2*Vt(.05,1,1-(t.bounce||0))*Math.sqrt(r);e={...e,mass:Z.mass,stiffness:r,damping:o}}else{const n=ed(t);e={...e,...n,mass:Z.mass},e.isResolvedFromDuration=!0}return e}function No(t=Z.visualDuration,e=Z.bounce){const n=typeof t!="object"?{visualDuration:t,keyframes:[0,1],bounce:e}:t;let{restSpeed:i,restDelta:r}=n;const o=n.keyframes[0],s=n.keyframes[n.keyframes.length-1],a={done:!1,value:o},{stiffness:l,damping:c,mass:u,duration:d,velocity:f,isResolvedFromDuration:m}=od({...n,velocity:-Dt(n.velocity||0)}),v=f||0,g=c/(2*Math.sqrt(l*u)),y=s-o,p=Dt(Math.sqrt(l/u)),b=Math.abs(y)<5;i||(i=b?Z.restSpeed.granular:Z.restSpeed.default),r||(r=b?Z.restDelta.granular:Z.restDelta.default);let S;if(g<1){const T=Nn(p,g);S=R=>{const L=Math.exp(-g*p*R);return s-L*((v+g*p*y)/T*Math.sin(T*R)+y*Math.cos(T*R))}}else if(g===1)S=T=>s-Math.exp(-p*T)*(y+(v+p*y)*T);else{const T=p*Math.sqrt(g*g-1);S=R=>{const L=Math.exp(-g*p*R),x=Math.min(T*R,300);return s-L*((v+g*p*y)*Math.sinh(x)+T*y*Math.cosh(x))/T}}const A={calculatedDuration:m&&d||null,next:T=>{const R=S(T);if(m)a.done=T>=d;else{let L=0;g<1&&(L=T===0?Mt(v):jo(S,T,R));const x=Math.abs(L)<=i,P=Math.abs(s-R)<=r;a.done=x&&P}return a.value=a.done?s:R,a},toString:()=>{const T=Math.min(no(A),Dn),R=io(L=>A.next(T*L).value,T,30);return T+"ms "+R}};return A}function Rr({keyframes:t,velocity:e=0,power:n=.8,timeConstant:i=325,bounceDamping:r=10,bounceStiffness:o=500,modifyTarget:s,min:a,max:l,restDelta:c=.5,restSpeed:u}){const d=t[0],f={done:!1,value:d},m=x=>a!==void 0&&x<a||l!==void 0&&x>l,v=x=>a===void 0?l:l===void 0||Math.abs(a-x)<Math.abs(l-x)?a:l;let g=n*e;const y=d+g,p=s===void 0?y:s(y);p!==y&&(g=p-d);const b=x=>-g*Math.exp(-x/i),S=x=>p+b(x),A=x=>{const P=b(x),C=S(x);f.done=Math.abs(P)<=c,f.value=f.done?p:C};let T,R;const L=x=>{m(f.value)&&(T=x,R=No({keyframes:[f.value,v(f.value)],velocity:jo(S,x,f.value),damping:r,stiffness:o,restDelta:c,restSpeed:u}))};return L(0),{calculatedDuration:null,next:x=>{let P=!1;return!R&&T===void 0&&(P=!0,A(x),L(x)),T!==void 0&&x>=T?R.next(x-T):(!P&&A(x),f)}}}const ad=Re(.42,0,1,1),ld=Re(0,0,.58,1),Fo=Re(.42,0,.58,1),cd=t=>Array.isArray(t)&&typeof t[0]!="number",ud={linear:dt,easeIn:ad,easeInOut:Fo,easeOut:ld,circIn:Si,circInOut:So,circOut:bo,backIn:bi,backInOut:vo,backOut:go,anticipate:yo},Er=t=>{if(mi(t)){Is(t.length===4);const[e,n,i,r]=t;return Re(e,n,i,r)}else if(typeof t=="string")return ud[t];return t};function dd(t,e,n){const i=[],r=n||Bo,o=t.length-1;for(let s=0;s<o;s++){let a=r(t[s],t[s+1]);if(e){const l=Array.isArray(e)?e[s]||dt:e;a=Ee(l,a)}i.push(a)}return i}function fd(t,e,{clamp:n=!0,ease:i,mixer:r}={}){const o=t.length;if(Is(o===e.length),o===1)return()=>e[0];if(o===2&&e[0]===e[1])return()=>e[1];const s=t[0]===t[1];t[0]>t[o-1]&&(t=[...t].reverse(),e=[...e].reverse());const a=dd(e,i,r),l=a.length,c=u=>{if(s&&u<t[0])return e[0];let d=0;if(l>1)for(;d<t.length-2&&!(u<t[d+1]);d++);const f=se(t[d],t[d+1],u);return a[d](f)};return n?u=>c(Vt(t[0],t[o-1],u)):c}function hd(t,e){const n=t[t.length-1];for(let i=1;i<=e;i++){const r=se(0,e,i);t.push(q(n,1,r))}}function md(t){const e=[0];return hd(e,t.length-1),e}function pd(t,e){return t.map(n=>n*e)}function gd(t,e){return t.map(()=>e||Fo).splice(0,t.length-1)}function qe({duration:t=300,keyframes:e,times:n,ease:i="easeInOut"}){const r=cd(i)?i.map(Er):Er(i),o={done:!1,value:e[0]},s=pd(n&&n.length===e.length?n:md(e),t),a=fd(s,e,{ease:Array.isArray(r)?r:gd(e,r)});return{calculatedDuration:t,next:l=>(o.value=a(l),o.done=l>=t,o)}}const vd=t=>{const e=({timestamp:n})=>t(n);return{start:()=>U.update(e,!0),stop:()=>jt(e),now:()=>rt.isProcessing?rt.timestamp:$t.now()}},yd={decay:Rr,inertia:Rr,tween:qe,keyframes:qe,spring:No},bd=t=>t/100;class $i extends Oo{constructor(e){super(e),this.holdTime=null,this.cancelTime=null,this.currentTime=0,this.playbackSpeed=1,this.pendingPlayState="running",this.startTime=null,this.state="idle",this.stop=()=>{if(this.resolver.cancel(),this.isStopped=!0,this.state==="idle")return;this.teardown();const{onStop:l}=this.options;l&&l()};const{name:n,motionValue:i,element:r,keyframes:o}=this.options,s=(r==null?void 0:r.KeyframeResolver)||Ci,a=(l,c)=>this.onKeyframesResolved(l,c);this.resolver=new s(o,a,n,i,r),this.resolver.scheduleResolve()}flatten(){super.flatten(),this._resolved&&Object.assign(this._resolved,this.initPlayback(this._resolved.keyframes))}initPlayback(e){const{type:n="keyframes",repeat:i=0,repeatDelay:r=0,repeatType:o,velocity:s=0}=this.options,a=hi(n)?n:yd[n]||qe;let l,c;a!==qe&&typeof e[0]!="number"&&(l=Ee(bd,Bo(e[0],e[1])),e=[0,100]);const u=a({...this.options,keyframes:e});o==="mirror"&&(c=a({...this.options,keyframes:[...e].reverse(),velocity:-s})),u.calculatedDuration===null&&(u.calculatedDuration=no(u));const{calculatedDuration:d}=u,f=d+r,m=f*(i+1)-r;return{generator:u,mirroredGenerator:c,mapPercentToKeyframes:l,calculatedDuration:d,resolvedDuration:f,totalDuration:m}}onPostResolved(){const{autoplay:e=!0}=this.options;this.play(),this.pendingPlayState==="paused"||!e?this.pause():this.state=this.pendingPlayState}tick(e,n=!1){const{resolved:i}=this;if(!i){const{keyframes:x}=this.options;return{done:!0,value:x[x.length-1]}}const{finalKeyframe:r,generator:o,mirroredGenerator:s,mapPercentToKeyframes:a,keyframes:l,calculatedDuration:c,totalDuration:u,resolvedDuration:d}=i;if(this.startTime===null)return o.next(0);const{delay:f,repeat:m,repeatType:v,repeatDelay:g,onUpdate:y}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,e):this.speed<0&&(this.startTime=Math.min(e-u/this.speed,this.startTime)),n?this.currentTime=e:this.holdTime!==null?this.currentTime=this.holdTime:this.currentTime=Math.round(e-this.startTime)*this.speed;const p=this.currentTime-f*(this.speed>=0?1:-1),b=this.speed>=0?p<0:p>u;this.currentTime=Math.max(p,0),this.state==="finished"&&this.holdTime===null&&(this.currentTime=u);let S=this.currentTime,A=o;if(m){const x=Math.min(this.currentTime,u)/d;let P=Math.floor(x),C=x%1;!C&&x>=1&&(C=1),C===1&&P--,P=Math.min(P,m+1),!!(P%2)&&(v==="reverse"?(C=1-C,g&&(C-=g/d)):v==="mirror"&&(A=s)),S=Vt(0,1,C)*d}const T=b?{done:!1,value:l[0]}:A.next(S);a&&(T.value=a(T.value));let{done:R}=T;!b&&c!==null&&(R=this.speed>=0?this.currentTime>=u:this.currentTime<=0);const L=this.holdTime===null&&(this.state==="finished"||this.state==="running"&&R);return L&&r!==void 0&&(T.value=sn(l,this.options,r)),y&&y(T.value),L&&this.finish(),T}get duration(){const{resolved:e}=this;return e?Dt(e.calculatedDuration):0}get time(){return Dt(this.currentTime)}set time(e){e=Mt(e),this.currentTime=e,this.holdTime!==null||this.speed===0?this.holdTime=e:this.driver&&(this.startTime=this.driver.now()-e/this.speed)}get speed(){return this.playbackSpeed}set speed(e){const n=this.playbackSpeed!==e;this.playbackSpeed=e,n&&(this.time=Dt(this.currentTime))}play(){if(this.resolver.isScheduled||this.resolver.resume(),!this._resolved){this.pendingPlayState="running";return}if(this.isStopped)return;const{driver:e=vd,onPlay:n,startTime:i}=this.options;this.driver||(this.driver=e(o=>this.tick(o))),n&&n();const r=this.driver.now();this.holdTime!==null?this.startTime=r-this.holdTime:this.startTime?this.state==="finished"&&(this.startTime=r):this.startTime=i??this.calcStartTime(),this.state==="finished"&&this.updateFinishedPromise(),this.cancelTime=this.startTime,this.holdTime=null,this.state="running",this.driver.start()}pause(){var e;if(!this._resolved){this.pendingPlayState="paused";return}this.state="paused",this.holdTime=(e=this.currentTime)!==null&&e!==void 0?e:0}complete(){this.state!=="running"&&this.play(),this.pendingPlayState=this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";const{onComplete:e}=this.options;e&&e()}cancel(){this.cancelTime!==null&&this.tick(this.cancelTime),this.teardown(),this.updateFinishedPromise()}teardown(){this.state="idle",this.stopDriver(),this.resolveFinishedPromise(),this.updateFinishedPromise(),this.startTime=this.cancelTime=null,this.resolver.cancel()}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(e){return this.startTime=0,this.tick(e,!0)}}const Sd=new Set(["opacity","clipPath","filter","transform"]);function xd(t,e,n,{delay:i=0,duration:r=300,repeat:o=0,repeatType:s="loop",ease:a="easeInOut",times:l}={}){const c={[e]:n};l&&(c.offset=l);const u=so(a,r);return Array.isArray(u)&&(c.easing=u),t.animate(c,{delay:i,duration:r,easing:Array.isArray(u)?"linear":u,fill:"both",iterations:o+1,direction:s==="reverse"?"alternate":"normal"})}const Td=Jn(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),Ye=10,Pd=2e4;function Cd(t){return hi(t.type)||t.type==="spring"||!ro(t.ease)}function wd(t,e){const n=new $i({...e,keyframes:t,repeat:0,delay:0,isGenerator:!0});let i={done:!1,value:t[0]};const r=[];let o=0;for(;!i.done&&o<Pd;)i=n.sample(o),r.push(i.value),o+=Ye;return{times:void 0,keyframes:r,duration:o-Ye,ease:"linear"}}const zo={anticipate:yo,backInOut:vo,circInOut:So};function $d(t){return t in zo}class Mr extends Oo{constructor(e){super(e);const{name:n,motionValue:i,element:r,keyframes:o}=this.options;this.resolver=new _o(o,(s,a)=>this.onKeyframesResolved(s,a),n,i,r),this.resolver.scheduleResolve()}initPlayback(e,n){let{duration:i=300,times:r,ease:o,type:s,motionValue:a,name:l,startTime:c}=this.options;if(!a.owner||!a.owner.current)return!1;if(typeof o=="string"&&He()&&$d(o)&&(o=zo[o]),Cd(this.options)){const{onComplete:d,onUpdate:f,motionValue:m,element:v,...g}=this.options,y=wd(e,g);e=y.keyframes,e.length===1&&(e[1]=e[0]),i=y.duration,r=y.times,o=y.ease,s="keyframes"}const u=xd(a.owner.current,l,e,{...this.options,duration:i,times:r,ease:o});return u.startTime=c??this.calcStartTime(),this.pendingTimeline?(hr(u,this.pendingTimeline),this.pendingTimeline=void 0):u.onfinish=()=>{const{onComplete:d}=this.options;a.set(sn(e,this.options,n)),d&&d(),this.cancel(),this.resolveFinishedPromise()},{animation:u,duration:i,times:r,type:s,ease:o,keyframes:e}}get duration(){const{resolved:e}=this;if(!e)return 0;const{duration:n}=e;return Dt(n)}get time(){const{resolved:e}=this;if(!e)return 0;const{animation:n}=e;return Dt(n.currentTime||0)}set time(e){const{resolved:n}=this;if(!n)return;const{animation:i}=n;i.currentTime=Mt(e)}get speed(){const{resolved:e}=this;if(!e)return 1;const{animation:n}=e;return n.playbackRate}set speed(e){const{resolved:n}=this;if(!n)return;const{animation:i}=n;i.playbackRate=e}get state(){const{resolved:e}=this;if(!e)return"idle";const{animation:n}=e;return n.playState}get startTime(){const{resolved:e}=this;if(!e)return null;const{animation:n}=e;return n.startTime}attachTimeline(e){if(!this._resolved)this.pendingTimeline=e;else{const{resolved:n}=this;if(!n)return dt;const{animation:i}=n;hr(i,e)}return dt}play(){if(this.isStopped)return;const{resolved:e}=this;if(!e)return;const{animation:n}=e;n.playState==="finished"&&this.updateFinishedPromise(),n.play()}pause(){const{resolved:e}=this;if(!e)return;const{animation:n}=e;n.pause()}stop(){if(this.resolver.cancel(),this.isStopped=!0,this.state==="idle")return;this.resolveFinishedPromise(),this.updateFinishedPromise();const{resolved:e}=this;if(!e)return;const{animation:n,keyframes:i,duration:r,type:o,ease:s,times:a}=e;if(n.playState==="idle"||n.playState==="finished")return;if(this.time){const{motionValue:c,onUpdate:u,onComplete:d,element:f,...m}=this.options,v=new $i({...m,keyframes:i,duration:r,type:o,ease:s,times:a,isGenerator:!0}),g=Mt(this.time);c.setWithVelocity(v.sample(g-Ye).value,v.sample(g).value,Ye)}const{onStop:l}=this.options;l&&l(),this.cancel()}complete(){const{resolved:e}=this;e&&e.animation.finish()}cancel(){const{resolved:e}=this;e&&e.animation.cancel()}static supports(e){const{motionValue:n,name:i,repeatDelay:r,repeatType:o,damping:s,type:a}=e;if(!n||!n.owner||!(n.owner.current instanceof HTMLElement))return!1;const{onUpdate:l,transformTemplate:c}=n.owner.getProps();return Td()&&i&&Sd.has(i)&&!l&&!c&&!r&&o!=="mirror"&&s!==0&&a!=="inertia"}}const Ad={type:"spring",stiffness:500,damping:25,restSpeed:10},Rd=t=>({type:"spring",stiffness:550,damping:t===0?2*Math.sqrt(550):30,restSpeed:10}),Ed={type:"keyframes",duration:.8},Md={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},Dd=(t,{keyframes:e})=>e.length>2?Ed:Xt.has(t)?t.startsWith("scale")?Rd(e[1]):Ad:Md;function Vd({when:t,delay:e,delayChildren:n,staggerChildren:i,staggerDirection:r,repeat:o,repeatType:s,repeatDelay:a,from:l,elapsed:c,...u}){return!!Object.keys(u).length}const Ai=(t,e,n,i={},r,o)=>s=>{const a=fi(i,t)||{},l=a.delay||i.delay||0;let{elapsed:c=0}=i;c=c-Mt(l);let u={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-c,onUpdate:f=>{e.set(f),a.onUpdate&&a.onUpdate(f)},onComplete:()=>{s(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:o?void 0:r};Vd(a)||(u={...u,...Dd(t,u)}),u.duration&&(u.duration=Mt(u.duration)),u.repeatDelay&&(u.repeatDelay=Mt(u.repeatDelay)),u.from!==void 0&&(u.keyframes[0]=u.from);let d=!1;if((u.type===!1||u.duration===0&&!u.repeatDelay)&&(u.duration=0,u.delay===0&&(d=!0)),d&&!o&&e.get()!==void 0){const f=sn(u.keyframes,a);if(f!==void 0)return U.update(()=>{u.onUpdate(f),u.onComplete()}),new Zc([])}return!o&&Mr.supports(u)?new Mr(u):new $i(u)};function Ld({protectedKeys:t,needsAnimating:e},n){const i=t.hasOwnProperty(n)&&e[n]!==!0;return e[n]=!1,i}function ko(t,e,{delay:n=0,transitionOverride:i,type:r}={}){var o;let{transition:s=t.getDefaultTransition(),transitionEnd:a,...l}=e;i&&(s=i);const c=[],u=r&&t.animationState&&t.animationState.getState()[r];for(const d in l){const f=t.getValue(d,(o=t.latestValues[d])!==null&&o!==void 0?o:null),m=l[d];if(m===void 0||u&&Ld(u,d))continue;const v={delay:n,...fi(s||{},d)};let g=!1;if(window.MotionHandoffAnimation){const p=fo(t);if(p){const b=window.MotionHandoffAnimation(p,d,U);b!==null&&(v.startTime=b,g=!0)}}Ln(t,d),f.start(Ai(d,f,m,t.shouldReduceMotion&&co.has(d)?{type:!1}:v,t,g));const y=f.animation;y&&c.push(y)}return a&&Promise.all(c).then(()=>{U.update(()=>{a&&du(t,a)})}),c}function Fn(t,e,n={}){var i;const r=rn(t,e,n.type==="exit"?(i=t.presenceContext)===null||i===void 0?void 0:i.custom:void 0);let{transition:o=t.getDefaultTransition()||{}}=r||{};n.transitionOverride&&(o=n.transitionOverride);const s=r?()=>Promise.all(ko(t,r,n)):()=>Promise.resolve(),a=t.variantChildren&&t.variantChildren.size?(c=0)=>{const{delayChildren:u=0,staggerChildren:d,staggerDirection:f}=o;return _d(t,e,u+c,d,f,n)}:()=>Promise.resolve(),{when:l}=o;if(l){const[c,u]=l==="beforeChildren"?[s,a]:[a,s];return c().then(()=>u())}else return Promise.all([s(),a(n.delay)])}function _d(t,e,n=0,i=0,r=1,o){const s=[],a=(t.variantChildren.size-1)*i,l=r===1?(c=0)=>c*i:(c=0)=>a-c*i;return Array.from(t.variantChildren).sort(Od).forEach((c,u)=>{c.notify("AnimationStart",e),s.push(Fn(c,e,{...o,delay:n+l(u)}).then(()=>c.notify("AnimationComplete",e)))}),Promise.all(s)}function Od(t,e){return t.sortNodePosition(e)}function Id(t,e,n={}){t.notify("AnimationStart",e);let i;if(Array.isArray(e)){const r=e.map(o=>Fn(t,o,n));i=Promise.all(r)}else if(typeof e=="string")i=Fn(t,e,n);else{const r=typeof e=="function"?rn(t,e,n.custom):e;i=Promise.all(ko(t,r,n))}return i.then(()=>{t.notify("AnimationComplete",e)})}const Bd=ti.length;function Wo(t){if(!t)return;if(!t.isControllingVariants){const n=t.parent?Wo(t.parent)||{}:{};return t.props.initial!==void 0&&(n.initial=t.props.initial),n}const e={};for(let n=0;n<Bd;n++){const i=ti[n],r=t.props[i];(Te(r)||r===!1)&&(e[i]=r)}return e}const jd=[...Qn].reverse(),Nd=Qn.length;function Fd(t){return e=>Promise.all(e.map(({animation:n,options:i})=>Id(t,n,i)))}function zd(t){let e=Fd(t),n=Dr(),i=!0;const r=l=>(c,u)=>{var d;const f=rn(t,u,l==="exit"?(d=t.presenceContext)===null||d===void 0?void 0:d.custom:void 0);if(f){const{transition:m,transitionEnd:v,...g}=f;c={...c,...g,...v}}return c};function o(l){e=l(t)}function s(l){const{props:c}=t,u=Wo(t.parent)||{},d=[],f=new Set;let m={},v=1/0;for(let y=0;y<Nd;y++){const p=jd[y],b=n[p],S=c[p]!==void 0?c[p]:u[p],A=Te(S),T=p===l?b.isActive:null;T===!1&&(v=y);let R=S===u[p]&&S!==c[p]&&A;if(R&&i&&t.manuallyAnimateOnMount&&(R=!1),b.protectedKeys={...m},!b.isActive&&T===null||!S&&!b.prevProp||en(S)||typeof S=="boolean")continue;const L=kd(b.prevProp,S);let x=L||p===l&&b.isActive&&!R&&A||y>v&&A,P=!1;const C=Array.isArray(S)?S:[S];let E=C.reduce(r(p),{});T===!1&&(E={});const{prevResolvedValues:O={}}=b,M={...O,...E},_=w=>{x=!0,f.has(w)&&(P=!0,f.delete(w)),b.needsAnimating[w]=!0;const N=t.getValue(w);N&&(N.liveStyle=!1)};for(const w in M){const N=E[w],I=O[w];if(m.hasOwnProperty(w))continue;let H=!1;Mn(N)&&Mn(I)?H=!eo(N,I):H=N!==I,H?N!=null?_(w):f.add(w):N!==void 0&&f.has(w)?_(w):b.protectedKeys[w]=!0}b.prevProp=S,b.prevResolvedValues=E,b.isActive&&(m={...m,...E}),i&&t.blockInitialAnimation&&(x=!1),x&&(!(R&&L)||P)&&d.push(...C.map(w=>({animation:w,options:{type:p}})))}if(f.size){const y={};f.forEach(p=>{const b=t.getBaseTarget(p),S=t.getValue(p);S&&(S.liveStyle=!0),y[p]=b??null}),d.push({animation:y})}let g=!!d.length;return i&&(c.initial===!1||c.initial===c.animate)&&!t.manuallyAnimateOnMount&&(g=!1),i=!1,g?e(d):Promise.resolve()}function a(l,c){var u;if(n[l].isActive===c)return Promise.resolve();(u=t.variantChildren)===null||u===void 0||u.forEach(f=>{var m;return(m=f.animationState)===null||m===void 0?void 0:m.setActive(l,c)}),n[l].isActive=c;const d=s(l);for(const f in n)n[f].protectedKeys={};return d}return{animateChanges:s,setActive:a,setAnimateFunction:o,getState:()=>n,reset:()=>{n=Dr(),i=!0}}}function kd(t,e){return typeof e=="string"?e!==t:Array.isArray(e)?!eo(e,t):!1}function Wt(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function Dr(){return{animate:Wt(!0),whileInView:Wt(),whileHover:Wt(),whileTap:Wt(),whileDrag:Wt(),whileFocus:Wt(),exit:Wt()}}class Ft{constructor(e){this.isMounted=!1,this.node=e}update(){}}class Wd extends Ft{constructor(e){super(e),e.animationState||(e.animationState=zd(e))}updateAnimationControlsSubscription(){const{animate:e}=this.node.getProps();en(e)&&(this.unmountControls=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:e}=this.node.getProps(),{animate:n}=this.node.prevProps||{};e!==n&&this.updateAnimationControlsSubscription()}unmount(){var e;this.node.animationState.reset(),(e=this.unmountControls)===null||e===void 0||e.call(this)}}let Ud=0;class Gd extends Ft{constructor(){super(...arguments),this.id=Ud++}update(){if(!this.node.presenceContext)return;const{isPresent:e,onExitComplete:n}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===i)return;const r=this.node.animationState.setActive("exit",!e);n&&!e&&r.then(()=>n(this.id))}mount(){const{register:e}=this.node.presenceContext||{};e&&(this.unmount=e(this.id))}unmount(){}}const Kd={animation:{Feature:Wd},exit:{Feature:Gd}};function $e(t,e,n,i={passive:!0}){return t.addEventListener(e,n,i),()=>t.removeEventListener(e,n)}function Me(t){return{point:{x:t.pageX,y:t.pageY}}}const Hd=t=>e=>pi(e)&&t(e,Me(e));function be(t,e,n,i){return $e(t,e,Hd(n),i)}const Vr=(t,e)=>Math.abs(t-e);function Xd(t,e){const n=Vr(t.x,e.x),i=Vr(t.y,e.y);return Math.sqrt(n**2+i**2)}class Uo{constructor(e,n,{transformPagePoint:i,contextWindow:r,dragSnapToOrigin:o=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const d=Tn(this.lastMoveEventInfo,this.history),f=this.startEvent!==null,m=Xd(d.offset,{x:0,y:0})>=3;if(!f&&!m)return;const{point:v}=d,{timestamp:g}=rt;this.history.push({...v,timestamp:g});const{onStart:y,onMove:p}=this.handlers;f||(y&&y(this.lastMoveEvent,d),this.startEvent=this.lastMoveEvent),p&&p(this.lastMoveEvent,d)},this.handlePointerMove=(d,f)=>{this.lastMoveEvent=d,this.lastMoveEventInfo=xn(f,this.transformPagePoint),U.update(this.updatePoint,!0)},this.handlePointerUp=(d,f)=>{this.end();const{onEnd:m,onSessionEnd:v,resumeAnimation:g}=this.handlers;if(this.dragSnapToOrigin&&g&&g(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const y=Tn(d.type==="pointercancel"?this.lastMoveEventInfo:xn(f,this.transformPagePoint),this.history);this.startEvent&&m&&m(d,y),v&&v(d,y)},!pi(e))return;this.dragSnapToOrigin=o,this.handlers=n,this.transformPagePoint=i,this.contextWindow=r||window;const s=Me(e),a=xn(s,this.transformPagePoint),{point:l}=a,{timestamp:c}=rt;this.history=[{...l,timestamp:c}];const{onSessionStart:u}=n;u&&u(e,Tn(a,this.history)),this.removeListeners=Ee(be(this.contextWindow,"pointermove",this.handlePointerMove),be(this.contextWindow,"pointerup",this.handlePointerUp),be(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),jt(this.updatePoint)}}function xn(t,e){return e?{point:e(t.point)}:t}function Lr(t,e){return{x:t.x-e.x,y:t.y-e.y}}function Tn({point:t},e){return{point:t,delta:Lr(t,Go(e)),offset:Lr(t,qd(e)),velocity:Yd(e,.1)}}function qd(t){return t[0]}function Go(t){return t[t.length-1]}function Yd(t,e){if(t.length<2)return{x:0,y:0};let n=t.length-1,i=null;const r=Go(t);for(;n>=0&&(i=t[n],!(r.timestamp-i.timestamp>Mt(e)));)n--;if(!i)return{x:0,y:0};const o=Dt(r.timestamp-i.timestamp);if(o===0)return{x:0,y:0};const s={x:(r.x-i.x)/o,y:(r.y-i.y)/o};return s.x===1/0&&(s.x=0),s.y===1/0&&(s.y=0),s}const Ko=1e-4,Zd=1-Ko,Jd=1+Ko,Ho=.01,Qd=0-Ho,tf=0+Ho;function ft(t){return t.max-t.min}function ef(t,e,n){return Math.abs(t-e)<=n}function _r(t,e,n,i=.5){t.origin=i,t.originPoint=q(e.min,e.max,t.origin),t.scale=ft(n)/ft(e),t.translate=q(n.min,n.max,t.origin)-t.originPoint,(t.scale>=Zd&&t.scale<=Jd||isNaN(t.scale))&&(t.scale=1),(t.translate>=Qd&&t.translate<=tf||isNaN(t.translate))&&(t.translate=0)}function Se(t,e,n,i){_r(t.x,e.x,n.x,i?i.originX:void 0),_r(t.y,e.y,n.y,i?i.originY:void 0)}function Or(t,e,n){t.min=n.min+e.min,t.max=t.min+ft(e)}function nf(t,e,n){Or(t.x,e.x,n.x),Or(t.y,e.y,n.y)}function Ir(t,e,n){t.min=e.min-n.min,t.max=t.min+ft(e)}function xe(t,e,n){Ir(t.x,e.x,n.x),Ir(t.y,e.y,n.y)}function rf(t,{min:e,max:n},i){return e!==void 0&&t<e?t=i?q(e,t,i.min):Math.max(t,e):n!==void 0&&t>n&&(t=i?q(n,t,i.max):Math.min(t,n)),t}function Br(t,e,n){return{min:e!==void 0?t.min+e:void 0,max:n!==void 0?t.max+n-(t.max-t.min):void 0}}function sf(t,{top:e,left:n,bottom:i,right:r}){return{x:Br(t.x,n,r),y:Br(t.y,e,i)}}function jr(t,e){let n=e.min-t.min,i=e.max-t.max;return e.max-e.min<t.max-t.min&&([n,i]=[i,n]),{min:n,max:i}}function of(t,e){return{x:jr(t.x,e.x),y:jr(t.y,e.y)}}function af(t,e){let n=.5;const i=ft(t),r=ft(e);return r>i?n=se(e.min,e.max-i,t.min):i>r&&(n=se(t.min,t.max-r,e.min)),Vt(0,1,n)}function lf(t,e){const n={};return e.min!==void 0&&(n.min=e.min-t.min),e.max!==void 0&&(n.max=e.max-t.min),n}const zn=.35;function cf(t=zn){return t===!1?t=0:t===!0&&(t=zn),{x:Nr(t,"left","right"),y:Nr(t,"top","bottom")}}function Nr(t,e,n){return{min:Fr(t,e),max:Fr(t,n)}}function Fr(t,e){return typeof t=="number"?t:t[e]||0}const zr=()=>({translate:0,scale:1,origin:0,originPoint:0}),ne=()=>({x:zr(),y:zr()}),kr=()=>({min:0,max:0}),J=()=>({x:kr(),y:kr()});function gt(t){return[t("x"),t("y")]}function Xo({top:t,left:e,right:n,bottom:i}){return{x:{min:e,max:n},y:{min:t,max:i}}}function uf({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}function df(t,e){if(!e)return t;const n=e({x:t.left,y:t.top}),i=e({x:t.right,y:t.bottom});return{top:n.y,left:n.x,bottom:i.y,right:i.x}}function Pn(t){return t===void 0||t===1}function kn({scale:t,scaleX:e,scaleY:n}){return!Pn(t)||!Pn(e)||!Pn(n)}function Ut(t){return kn(t)||qo(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function qo(t){return Wr(t.x)||Wr(t.y)}function Wr(t){return t&&t!=="0%"}function Ze(t,e,n){const i=t-n,r=e*i;return n+r}function Ur(t,e,n,i,r){return r!==void 0&&(t=Ze(t,r,i)),Ze(t,n,i)+e}function Wn(t,e=0,n=1,i,r){t.min=Ur(t.min,e,n,i,r),t.max=Ur(t.max,e,n,i,r)}function Yo(t,{x:e,y:n}){Wn(t.x,e.translate,e.scale,e.originPoint),Wn(t.y,n.translate,n.scale,n.originPoint)}const Gr=.999999999999,Kr=1.0000000000001;function ff(t,e,n,i=!1){const r=n.length;if(!r)return;e.x=e.y=1;let o,s;for(let a=0;a<r;a++){o=n[a],s=o.projectionDelta;const{visualElement:l}=o.options;l&&l.props.style&&l.props.style.display==="contents"||(i&&o.options.layoutScroll&&o.scroll&&o!==o.root&&re(t,{x:-o.scroll.offset.x,y:-o.scroll.offset.y}),s&&(e.x*=s.x.scale,e.y*=s.y.scale,Yo(t,s)),i&&Ut(o.latestValues)&&re(t,o.latestValues))}e.x<Kr&&e.x>Gr&&(e.x=1),e.y<Kr&&e.y>Gr&&(e.y=1)}function ie(t,e){t.min=t.min+e,t.max=t.max+e}function Hr(t,e,n,i,r=.5){const o=q(t.min,t.max,r);Wn(t,e,n,o,i)}function re(t,e){Hr(t.x,e.x,e.scaleX,e.scale,e.originX),Hr(t.y,e.y,e.scaleY,e.scale,e.originY)}function Zo(t,e){return Xo(df(t.getBoundingClientRect(),e))}function hf(t,e,n){const i=Zo(t,n),{scroll:r}=e;return r&&(ie(i.x,r.offset.x),ie(i.y,r.offset.y)),i}const Jo=({current:t})=>t?t.ownerDocument.defaultView:null,mf=new WeakMap;class pf{constructor(e){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=J(),this.visualElement=e}start(e,{snapToCursor:n=!1}={}){const{presenceContext:i}=this.visualElement;if(i&&i.isPresent===!1)return;const r=u=>{const{dragSnapToOrigin:d}=this.getProps();d?this.pauseAnimation():this.stopAnimation(),n&&this.snapToCursor(Me(u).point)},o=(u,d)=>{const{drag:f,dragPropagation:m,onDragStart:v}=this.getProps();if(f&&!m&&(this.openDragLock&&this.openDragLock(),this.openDragLock=ou(f),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),gt(y=>{let p=this.getAxisMotionValue(y).get()||0;if(wt.test(p)){const{projection:b}=this.visualElement;if(b&&b.layout){const S=b.layout.layoutBox[y];S&&(p=ft(S)*(parseFloat(p)/100))}}this.originPoint[y]=p}),v&&U.postRender(()=>v(u,d)),Ln(this.visualElement,"transform");const{animationState:g}=this.visualElement;g&&g.setActive("whileDrag",!0)},s=(u,d)=>{const{dragPropagation:f,dragDirectionLock:m,onDirectionLock:v,onDrag:g}=this.getProps();if(!f&&!this.openDragLock)return;const{offset:y}=d;if(m&&this.currentDirection===null){this.currentDirection=gf(y),this.currentDirection!==null&&v&&v(this.currentDirection);return}this.updateAxis("x",d.point,y),this.updateAxis("y",d.point,y),this.visualElement.render(),g&&g(u,d)},a=(u,d)=>this.stop(u,d),l=()=>gt(u=>{var d;return this.getAnimationState(u)==="paused"&&((d=this.getAxisMotionValue(u).animation)===null||d===void 0?void 0:d.play())}),{dragSnapToOrigin:c}=this.getProps();this.panSession=new Uo(e,{onSessionStart:r,onStart:o,onMove:s,onSessionEnd:a,resumeAnimation:l},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:c,contextWindow:Jo(this.visualElement)})}stop(e,n){const i=this.isDragging;if(this.cancel(),!i)return;const{velocity:r}=n;this.startAnimation(r);const{onDragEnd:o}=this.getProps();o&&U.postRender(()=>o(e,n))}cancel(){this.isDragging=!1;const{projection:e,animationState:n}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),n&&n.setActive("whileDrag",!1)}updateAxis(e,n,i){const{drag:r}=this.getProps();if(!i||!je(e,r,this.currentDirection))return;const o=this.getAxisMotionValue(e);let s=this.originPoint[e]+i[e];this.constraints&&this.constraints[e]&&(s=rf(s,this.constraints[e],this.elastic[e])),o.set(s)}resolveConstraints(){var e;const{dragConstraints:n,dragElastic:i}=this.getProps(),r=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(e=this.visualElement.projection)===null||e===void 0?void 0:e.layout,o=this.constraints;n&&te(n)?this.constraints||(this.constraints=this.resolveRefConstraints()):n&&r?this.constraints=sf(r.layoutBox,n):this.constraints=!1,this.elastic=cf(i),o!==this.constraints&&r&&this.constraints&&!this.hasMutatedConstraints&&gt(s=>{this.constraints!==!1&&this.getAxisMotionValue(s)&&(this.constraints[s]=lf(r.layoutBox[s],this.constraints[s]))})}resolveRefConstraints(){const{dragConstraints:e,onMeasureDragConstraints:n}=this.getProps();if(!e||!te(e))return!1;const i=e.current,{projection:r}=this.visualElement;if(!r||!r.layout)return!1;const o=hf(i,r.root,this.visualElement.getTransformPagePoint());let s=of(r.layout.layoutBox,o);if(n){const a=n(uf(s));this.hasMutatedConstraints=!!a,a&&(s=Xo(a))}return s}startAnimation(e){const{drag:n,dragMomentum:i,dragElastic:r,dragTransition:o,dragSnapToOrigin:s,onDragTransitionEnd:a}=this.getProps(),l=this.constraints||{},c=gt(u=>{if(!je(u,n,this.currentDirection))return;let d=l&&l[u]||{};s&&(d={min:0,max:0});const f=r?200:1e6,m=r?40:1e7,v={type:"inertia",velocity:i?e[u]:0,bounceStiffness:f,bounceDamping:m,timeConstant:750,restDelta:1,restSpeed:10,...o,...d};return this.startAxisValueAnimation(u,v)});return Promise.all(c).then(a)}startAxisValueAnimation(e,n){const i=this.getAxisMotionValue(e);return Ln(this.visualElement,e),i.start(Ai(e,i,0,n,this.visualElement,!1))}stopAnimation(){gt(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){gt(e=>{var n;return(n=this.getAxisMotionValue(e).animation)===null||n===void 0?void 0:n.pause()})}getAnimationState(e){var n;return(n=this.getAxisMotionValue(e).animation)===null||n===void 0?void 0:n.state}getAxisMotionValue(e){const n=`_drag${e.toUpperCase()}`,i=this.visualElement.getProps(),r=i[n];return r||this.visualElement.getValue(e,(i.initial?i.initial[e]:void 0)||0)}snapToCursor(e){gt(n=>{const{drag:i}=this.getProps();if(!je(n,i,this.currentDirection))return;const{projection:r}=this.visualElement,o=this.getAxisMotionValue(n);if(r&&r.layout){const{min:s,max:a}=r.layout.layoutBox[n];o.set(e[n]-q(s,a,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:e,dragConstraints:n}=this.getProps(),{projection:i}=this.visualElement;if(!te(n)||!i||!this.constraints)return;this.stopAnimation();const r={x:0,y:0};gt(s=>{const a=this.getAxisMotionValue(s);if(a&&this.constraints!==!1){const l=a.get();r[s]=af({min:l,max:l},this.constraints[s])}});const{transformTemplate:o}=this.visualElement.getProps();this.visualElement.current.style.transform=o?o({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),gt(s=>{if(!je(s,e,null))return;const a=this.getAxisMotionValue(s),{min:l,max:c}=this.constraints[s];a.set(q(l,c,r[s]))})}addListeners(){if(!this.visualElement.current)return;mf.set(this.visualElement,this);const e=this.visualElement.current,n=be(e,"pointerdown",l=>{const{drag:c,dragListener:u=!0}=this.getProps();c&&u&&this.start(l)}),i=()=>{const{dragConstraints:l}=this.getProps();te(l)&&l.current&&(this.constraints=this.resolveRefConstraints())},{projection:r}=this.visualElement,o=r.addEventListener("measure",i);r&&!r.layout&&(r.root&&r.root.updateScroll(),r.updateLayout()),U.read(i);const s=$e(window,"resize",()=>this.scalePositionWithinConstraints()),a=r.addEventListener("didUpdate",({delta:l,hasLayoutChanged:c})=>{this.isDragging&&c&&(gt(u=>{const d=this.getAxisMotionValue(u);d&&(this.originPoint[u]+=l[u].translate,d.set(d.get()+l[u].translate))}),this.visualElement.render())});return()=>{s(),n(),o(),a&&a()}}getProps(){const e=this.visualElement.getProps(),{drag:n=!1,dragDirectionLock:i=!1,dragPropagation:r=!1,dragConstraints:o=!1,dragElastic:s=zn,dragMomentum:a=!0}=e;return{...e,drag:n,dragDirectionLock:i,dragPropagation:r,dragConstraints:o,dragElastic:s,dragMomentum:a}}}function je(t,e,n){return(e===!0||e===t)&&(n===null||n===t)}function gf(t,e=10){let n=null;return Math.abs(t.y)>e?n="y":Math.abs(t.x)>e&&(n="x"),n}class vf extends Ft{constructor(e){super(e),this.removeGroupControls=dt,this.removeListeners=dt,this.controls=new pf(e)}mount(){const{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||dt}unmount(){this.removeGroupControls(),this.removeListeners()}}const Xr=t=>(e,n)=>{t&&U.postRender(()=>t(e,n))};class yf extends Ft{constructor(){super(...arguments),this.removePointerDownListener=dt}onPointerDown(e){this.session=new Uo(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:Jo(this.node)})}createPanHandlers(){const{onPanSessionStart:e,onPanStart:n,onPan:i,onPanEnd:r}=this.node.getProps();return{onSessionStart:Xr(e),onStart:Xr(n),onMove:i,onEnd:(o,s)=>{delete this.session,r&&U.postRender(()=>r(o,s))}}}mount(){this.removePointerDownListener=be(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}const ke={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function qr(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}const me={correct:(t,e)=>{if(!e.target)return t;if(typeof t=="string")if(D.test(t))t=parseFloat(t);else return t;const n=qr(t,e.target.x),i=qr(t,e.target.y);return`${n}% ${i}%`}},bf={correct:(t,{treeScale:e,projectionDelta:n})=>{const i=t,r=Nt.parse(t);if(r.length>5)return i;const o=Nt.createTransformer(t),s=typeof r[0]!="number"?1:0,a=n.x.scale*e.x,l=n.y.scale*e.y;r[0+s]/=a,r[1+s]/=l;const c=q(a,l,.5);return typeof r[2+s]=="number"&&(r[2+s]/=c),typeof r[3+s]=="number"&&(r[3+s]/=c),o(r)}};class Sf extends h.Component{componentDidMount(){const{visualElement:e,layoutGroup:n,switchLayoutGroup:i,layoutId:r}=this.props,{projection:o}=e;Nc(xf),o&&(n.group&&n.group.add(o),i&&i.register&&r&&i.register(o),o.root.didUpdate(),o.addEventListener("animationComplete",()=>{this.safeToRemove()}),o.setOptions({...o.options,onExitComplete:()=>this.safeToRemove()})),ke.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){const{layoutDependency:n,visualElement:i,drag:r,isPresent:o}=this.props,s=i.projection;return s&&(s.isPresent=o,r||e.layoutDependency!==n||n===void 0?s.willUpdate():this.safeToRemove(),e.isPresent!==o&&(o?s.promote():s.relegate()||U.postRender(()=>{const a=s.getStack();(!a||!a.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),ni.postRender(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:e,layoutGroup:n,switchLayoutGroup:i}=this.props,{projection:r}=e;r&&(r.scheduleCheckAfterUnmount(),n&&n.group&&n.group.remove(r),i&&i.deregister&&i.deregister(r))}safeToRemove(){const{safeToRemove:e}=this.props;e&&e()}render(){return null}}function Qo(t){const[e,n]=tc(),i=h.useContext(_s);return En.jsx(Sf,{...t,layoutGroup:i,switchLayoutGroup:h.useContext(ks),isPresent:e,safeToRemove:n})}const xf={borderRadius:{...me,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:me,borderTopRightRadius:me,borderBottomLeftRadius:me,borderBottomRightRadius:me,boxShadow:bf};function Tf(t,e,n){const i=at(t)?t:Ce(t);return i.start(Ai("",i,e,n)),i.animation}function Pf(t){return t instanceof SVGElement&&t.tagName!=="svg"}const Cf=(t,e)=>t.depth-e.depth;class wf{constructor(){this.children=[],this.isDirty=!1}add(e){gi(this.children,e),this.isDirty=!0}remove(e){vi(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(Cf),this.isDirty=!1,this.children.forEach(e)}}function $f(t,e){const n=$t.now(),i=({timestamp:r})=>{const o=r-n;o>=e&&(jt(i),t(o-e))};return U.read(i,!0),()=>jt(i)}const ta=["TopLeft","TopRight","BottomLeft","BottomRight"],Af=ta.length,Yr=t=>typeof t=="string"?parseFloat(t):t,Zr=t=>typeof t=="number"||D.test(t);function Rf(t,e,n,i,r,o){r?(t.opacity=q(0,n.opacity!==void 0?n.opacity:1,Ef(i)),t.opacityExit=q(e.opacity!==void 0?e.opacity:1,0,Mf(i))):o&&(t.opacity=q(e.opacity!==void 0?e.opacity:1,n.opacity!==void 0?n.opacity:1,i));for(let s=0;s<Af;s++){const a=`border${ta[s]}Radius`;let l=Jr(e,a),c=Jr(n,a);if(l===void 0&&c===void 0)continue;l||(l=0),c||(c=0),l===0||c===0||Zr(l)===Zr(c)?(t[a]=Math.max(q(Yr(l),Yr(c),i),0),(wt.test(c)||wt.test(l))&&(t[a]+="%")):t[a]=c}(e.rotate||n.rotate)&&(t.rotate=q(e.rotate||0,n.rotate||0,i))}function Jr(t,e){return t[e]!==void 0?t[e]:t.borderRadius}const Ef=ea(0,.5,bo),Mf=ea(.5,.95,dt);function ea(t,e,n){return i=>i<t?0:i>e?1:n(se(t,e,i))}function Qr(t,e){t.min=e.min,t.max=e.max}function pt(t,e){Qr(t.x,e.x),Qr(t.y,e.y)}function ts(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function es(t,e,n,i,r){return t-=e,t=Ze(t,1/n,i),r!==void 0&&(t=Ze(t,1/r,i)),t}function Df(t,e=0,n=1,i=.5,r,o=t,s=t){if(wt.test(e)&&(e=parseFloat(e),e=q(s.min,s.max,e/100)-s.min),typeof e!="number")return;let a=q(o.min,o.max,i);t===o&&(a-=e),t.min=es(t.min,e,n,a,r),t.max=es(t.max,e,n,a,r)}function ns(t,e,[n,i,r],o,s){Df(t,e[n],e[i],e[r],e.scale,o,s)}const Vf=["x","scaleX","originX"],Lf=["y","scaleY","originY"];function is(t,e,n,i){ns(t.x,e,Vf,n?n.x:void 0,i?i.x:void 0),ns(t.y,e,Lf,n?n.y:void 0,i?i.y:void 0)}function rs(t){return t.translate===0&&t.scale===1}function na(t){return rs(t.x)&&rs(t.y)}function ss(t,e){return t.min===e.min&&t.max===e.max}function _f(t,e){return ss(t.x,e.x)&&ss(t.y,e.y)}function os(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function ia(t,e){return os(t.x,e.x)&&os(t.y,e.y)}function as(t){return ft(t.x)/ft(t.y)}function ls(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class Of{constructor(){this.members=[]}add(e){gi(this.members,e),e.scheduleRender()}remove(e){if(vi(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){const n=this.members[this.members.length-1];n&&this.promote(n)}}relegate(e){const n=this.members.findIndex(r=>e===r);if(n===0)return!1;let i;for(let r=n;r>=0;r--){const o=this.members[r];if(o.isPresent!==!1){i=o;break}}return i?(this.promote(i),!0):!1}promote(e,n){const i=this.lead;if(e!==i&&(this.prevLead=i,this.lead=e,e.show(),i)){i.instance&&i.scheduleRender(),e.scheduleRender(),e.resumeFrom=i,n&&(e.resumeFrom.preserveOpacity=!0),i.snapshot&&(e.snapshot=i.snapshot,e.snapshot.latestValues=i.animationValues||i.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);const{crossfade:r}=e.options;r===!1&&i.hide()}}exitAnimationComplete(){this.members.forEach(e=>{const{options:n,resumingFrom:i}=e;n.onExitComplete&&n.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function If(t,e,n){let i="";const r=t.x.translate/e.x,o=t.y.translate/e.y,s=(n==null?void 0:n.z)||0;if((r||o||s)&&(i=`translate3d(${r}px, ${o}px, ${s}px) `),(e.x!==1||e.y!==1)&&(i+=`scale(${1/e.x}, ${1/e.y}) `),n){const{transformPerspective:c,rotate:u,rotateX:d,rotateY:f,skewX:m,skewY:v}=n;c&&(i=`perspective(${c}px) ${i}`),u&&(i+=`rotate(${u}deg) `),d&&(i+=`rotateX(${d}deg) `),f&&(i+=`rotateY(${f}deg) `),m&&(i+=`skewX(${m}deg) `),v&&(i+=`skewY(${v}deg) `)}const a=t.x.scale*e.x,l=t.y.scale*e.y;return(a!==1||l!==1)&&(i+=`scale(${a}, ${l})`),i||"none"}const Gt={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0},ve=typeof window<"u"&&window.MotionDebug!==void 0,Cn=["","X","Y","Z"],Bf={visibility:"hidden"},cs=1e3;let jf=0;function wn(t,e,n,i){const{latestValues:r}=e;r[t]&&(n[t]=r[t],e.setStaticValue(t,0),i&&(i[t]=0))}function ra(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;const{visualElement:e}=t.options;if(!e)return;const n=fo(e);if(window.MotionHasOptimisedAnimation(n,"transform")){const{layout:r,layoutId:o}=t.options;window.MotionCancelOptimisedAnimation(n,"transform",U,!(r||o))}const{parent:i}=t;i&&!i.hasCheckedOptimisedAppear&&ra(i)}function sa({attachResizeListener:t,defaultParent:e,measureScroll:n,checkIsScrollRoot:i,resetTransform:r}){return class{constructor(s={},a=e==null?void 0:e()){this.id=jf++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,ve&&(Gt.totalNodes=Gt.resolvedTargetDeltas=Gt.recalculatedProjection=0),this.nodes.forEach(zf),this.nodes.forEach(Kf),this.nodes.forEach(Hf),this.nodes.forEach(kf),ve&&window.MotionDebug.record(Gt)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=s,this.root=a?a.root||a:this,this.path=a?[...a.path,a]:[],this.parent=a,this.depth=a?a.depth+1:0;for(let l=0;l<this.path.length;l++)this.path[l].shouldResetTransform=!0;this.root===this&&(this.nodes=new wf)}addEventListener(s,a){return this.eventHandlers.has(s)||this.eventHandlers.set(s,new yi),this.eventHandlers.get(s).add(a)}notifyListeners(s,...a){const l=this.eventHandlers.get(s);l&&l.notify(...a)}hasListeners(s){return this.eventHandlers.has(s)}mount(s,a=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=Pf(s),this.instance=s;const{layoutId:l,layout:c,visualElement:u}=this.options;if(u&&!u.current&&u.mount(s),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),a&&(c||l)&&(this.isLayoutDirty=!0),t){let d;const f=()=>this.root.updateBlockedByResize=!1;t(s,()=>{this.root.updateBlockedByResize=!0,d&&d(),d=$f(f,250),ke.hasAnimatedSinceResize&&(ke.hasAnimatedSinceResize=!1,this.nodes.forEach(ds))})}l&&this.root.registerSharedNode(l,this),this.options.animate!==!1&&u&&(l||c)&&this.addEventListener("didUpdate",({delta:d,hasLayoutChanged:f,hasRelativeTargetChanged:m,layout:v})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const g=this.options.transition||u.getDefaultTransition()||Jf,{onLayoutAnimationStart:y,onLayoutAnimationComplete:p}=u.getProps(),b=!this.targetLayout||!ia(this.targetLayout,v)||m,S=!f&&m;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||S||f&&(b||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(d,S);const A={...fi(g,"layout"),onPlay:y,onComplete:p};(u.shouldReduceMotion||this.options.layoutRoot)&&(A.delay=0,A.type=!1),this.startAnimation(A)}else f||ds(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=v})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const s=this.getStack();s&&s.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,jt(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(Xf),this.animationId++)}getTransformTemplate(){const{visualElement:s}=this.options;return s&&s.getProps().transformTemplate}willUpdate(s=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&ra(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let u=0;u<this.path.length;u++){const d=this.path[u];d.shouldResetTransform=!0,d.updateScroll("snapshot"),d.options.layoutRoot&&d.willUpdate(!1)}const{layoutId:a,layout:l}=this.options;if(a===void 0&&!l)return;const c=this.getTransformTemplate();this.prevTransformTemplateValue=c?c(this.latestValues,""):void 0,this.updateSnapshot(),s&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(us);return}this.isUpdating||this.nodes.forEach(Uf),this.isUpdating=!1,this.nodes.forEach(Gf),this.nodes.forEach(Nf),this.nodes.forEach(Ff),this.clearAllSnapshots();const a=$t.now();rt.delta=Vt(0,1e3/60,a-rt.timestamp),rt.timestamp=a,rt.isProcessing=!0,gn.update.process(rt),gn.preRender.process(rt),gn.render.process(rt),rt.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,ni.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(Wf),this.sharedNodes.forEach(qf)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,U.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){U.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let l=0;l<this.path.length;l++)this.path[l].updateScroll();const s=this.layout;this.layout=this.measure(!1),this.layoutCorrected=J(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:a}=this.options;a&&a.notify("LayoutMeasure",this.layout.layoutBox,s?s.layoutBox:void 0)}updateScroll(s="measure"){let a=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===s&&(a=!1),a){const l=i(this.instance);this.scroll={animationId:this.root.animationId,phase:s,isRoot:l,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:l}}}resetTransform(){if(!r)return;const s=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,a=this.projectionDelta&&!na(this.projectionDelta),l=this.getTransformTemplate(),c=l?l(this.latestValues,""):void 0,u=c!==this.prevTransformTemplateValue;s&&(a||Ut(this.latestValues)||u)&&(r(this.instance,c),this.shouldResetTransform=!1,this.scheduleRender())}measure(s=!0){const a=this.measurePageBox();let l=this.removeElementScroll(a);return s&&(l=this.removeTransform(l)),Qf(l),{animationId:this.root.animationId,measuredBox:a,layoutBox:l,latestValues:{},source:this.id}}measurePageBox(){var s;const{visualElement:a}=this.options;if(!a)return J();const l=a.measureViewportBox();if(!(((s=this.scroll)===null||s===void 0?void 0:s.wasRoot)||this.path.some(th))){const{scroll:u}=this.root;u&&(ie(l.x,u.offset.x),ie(l.y,u.offset.y))}return l}removeElementScroll(s){var a;const l=J();if(pt(l,s),!((a=this.scroll)===null||a===void 0)&&a.wasRoot)return l;for(let c=0;c<this.path.length;c++){const u=this.path[c],{scroll:d,options:f}=u;u!==this.root&&d&&f.layoutScroll&&(d.wasRoot&&pt(l,s),ie(l.x,d.offset.x),ie(l.y,d.offset.y))}return l}applyTransform(s,a=!1){const l=J();pt(l,s);for(let c=0;c<this.path.length;c++){const u=this.path[c];!a&&u.options.layoutScroll&&u.scroll&&u!==u.root&&re(l,{x:-u.scroll.offset.x,y:-u.scroll.offset.y}),Ut(u.latestValues)&&re(l,u.latestValues)}return Ut(this.latestValues)&&re(l,this.latestValues),l}removeTransform(s){const a=J();pt(a,s);for(let l=0;l<this.path.length;l++){const c=this.path[l];if(!c.instance||!Ut(c.latestValues))continue;kn(c.latestValues)&&c.updateSnapshot();const u=J(),d=c.measurePageBox();pt(u,d),is(a,c.latestValues,c.snapshot?c.snapshot.layoutBox:void 0,u)}return Ut(this.latestValues)&&is(a,this.latestValues),a}setTargetDelta(s){this.targetDelta=s,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(s){this.options={...this.options,...s,crossfade:s.crossfade!==void 0?s.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==rt.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(s=!1){var a;const l=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=l.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=l.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=l.isSharedProjectionDirty);const c=!!this.resumingFrom||this!==l;if(!(s||c&&this.isSharedProjectionDirty||this.isProjectionDirty||!((a=this.parent)===null||a===void 0)&&a.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:d,layoutId:f}=this.options;if(!(!this.layout||!(d||f))){if(this.resolvedRelativeTargetAt=rt.timestamp,!this.targetDelta&&!this.relativeTarget){const m=this.getClosestProjectingParent();m&&m.layout&&this.animationProgress!==1?(this.relativeParent=m,this.forceRelativeParentToResolveTarget(),this.relativeTarget=J(),this.relativeTargetOrigin=J(),xe(this.relativeTargetOrigin,this.layout.layoutBox,m.layout.layoutBox),pt(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)){if(this.target||(this.target=J(),this.targetWithTransforms=J()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),nf(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):pt(this.target,this.layout.layoutBox),Yo(this.target,this.targetDelta)):pt(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const m=this.getClosestProjectingParent();m&&!!m.resumingFrom==!!this.resumingFrom&&!m.options.layoutScroll&&m.target&&this.animationProgress!==1?(this.relativeParent=m,this.forceRelativeParentToResolveTarget(),this.relativeTarget=J(),this.relativeTargetOrigin=J(),xe(this.relativeTargetOrigin,this.target,m.target),pt(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}ve&&Gt.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||kn(this.parent.latestValues)||qo(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var s;const a=this.getLead(),l=!!this.resumingFrom||this!==a;let c=!0;if((this.isProjectionDirty||!((s=this.parent)===null||s===void 0)&&s.isProjectionDirty)&&(c=!1),l&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(c=!1),this.resolvedRelativeTargetAt===rt.timestamp&&(c=!1),c)return;const{layout:u,layoutId:d}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(u||d))return;pt(this.layoutCorrected,this.layout.layoutBox);const f=this.treeScale.x,m=this.treeScale.y;ff(this.layoutCorrected,this.treeScale,this.path,l),a.layout&&!a.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(a.target=a.layout.layoutBox,a.targetWithTransforms=J());const{target:v}=a;if(!v){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}!this.projectionDelta||!this.prevProjectionDelta?this.createProjectionDeltas():(ts(this.prevProjectionDelta.x,this.projectionDelta.x),ts(this.prevProjectionDelta.y,this.projectionDelta.y)),Se(this.projectionDelta,this.layoutCorrected,v,this.latestValues),(this.treeScale.x!==f||this.treeScale.y!==m||!ls(this.projectionDelta.x,this.prevProjectionDelta.x)||!ls(this.projectionDelta.y,this.prevProjectionDelta.y))&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",v)),ve&&Gt.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(s=!0){var a;if((a=this.options.visualElement)===null||a===void 0||a.scheduleRender(),s){const l=this.getStack();l&&l.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=ne(),this.projectionDelta=ne(),this.projectionDeltaWithTransform=ne()}setAnimationOrigin(s,a=!1){const l=this.snapshot,c=l?l.latestValues:{},u={...this.latestValues},d=ne();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!a;const f=J(),m=l?l.source:void 0,v=this.layout?this.layout.source:void 0,g=m!==v,y=this.getStack(),p=!y||y.members.length<=1,b=!!(g&&!p&&this.options.crossfade===!0&&!this.path.some(Zf));this.animationProgress=0;let S;this.mixTargetDelta=A=>{const T=A/1e3;fs(d.x,s.x,T),fs(d.y,s.y,T),this.setTargetDelta(d),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(xe(f,this.layout.layoutBox,this.relativeParent.layout.layoutBox),Yf(this.relativeTarget,this.relativeTargetOrigin,f,T),S&&_f(this.relativeTarget,S)&&(this.isProjectionDirty=!1),S||(S=J()),pt(S,this.relativeTarget)),g&&(this.animationValues=u,Rf(u,c,this.latestValues,T,b,p)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=T},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(s){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(jt(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=U.update(()=>{ke.hasAnimatedSinceResize=!0,this.currentAnimation=Tf(0,cs,{...s,onUpdate:a=>{this.mixTargetDelta(a),s.onUpdate&&s.onUpdate(a)},onComplete:()=>{s.onComplete&&s.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const s=this.getStack();s&&s.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(cs),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const s=this.getLead();let{targetWithTransforms:a,target:l,layout:c,latestValues:u}=s;if(!(!a||!l||!c)){if(this!==s&&this.layout&&c&&oa(this.options.animationType,this.layout.layoutBox,c.layoutBox)){l=this.target||J();const d=ft(this.layout.layoutBox.x);l.x.min=s.target.x.min,l.x.max=l.x.min+d;const f=ft(this.layout.layoutBox.y);l.y.min=s.target.y.min,l.y.max=l.y.min+f}pt(a,l),re(a,u),Se(this.projectionDeltaWithTransform,this.layoutCorrected,a,u)}}registerSharedNode(s,a){this.sharedNodes.has(s)||this.sharedNodes.set(s,new Of),this.sharedNodes.get(s).add(a);const c=a.options.initialPromotionConfig;a.promote({transition:c?c.transition:void 0,preserveFollowOpacity:c&&c.shouldPreserveFollowOpacity?c.shouldPreserveFollowOpacity(a):void 0})}isLead(){const s=this.getStack();return s?s.lead===this:!0}getLead(){var s;const{layoutId:a}=this.options;return a?((s=this.getStack())===null||s===void 0?void 0:s.lead)||this:this}getPrevLead(){var s;const{layoutId:a}=this.options;return a?(s=this.getStack())===null||s===void 0?void 0:s.prevLead:void 0}getStack(){const{layoutId:s}=this.options;if(s)return this.root.sharedNodes.get(s)}promote({needsReset:s,transition:a,preserveFollowOpacity:l}={}){const c=this.getStack();c&&c.promote(this,l),s&&(this.projectionDelta=void 0,this.needsReset=!0),a&&this.setOptions({transition:a})}relegate(){const s=this.getStack();return s?s.relegate(this):!1}resetSkewAndRotation(){const{visualElement:s}=this.options;if(!s)return;let a=!1;const{latestValues:l}=s;if((l.z||l.rotate||l.rotateX||l.rotateY||l.rotateZ||l.skewX||l.skewY)&&(a=!0),!a)return;const c={};l.z&&wn("z",s,c,this.animationValues);for(let u=0;u<Cn.length;u++)wn(`rotate${Cn[u]}`,s,c,this.animationValues),wn(`skew${Cn[u]}`,s,c,this.animationValues);s.render();for(const u in c)s.setStaticValue(u,c[u]),this.animationValues&&(this.animationValues[u]=c[u]);s.scheduleRender()}getProjectionStyles(s){var a,l;if(!this.instance||this.isSVG)return;if(!this.isVisible)return Bf;const c={visibility:""},u=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,c.opacity="",c.pointerEvents=Fe(s==null?void 0:s.pointerEvents)||"",c.transform=u?u(this.latestValues,""):"none",c;const d=this.getLead();if(!this.projectionDelta||!this.layout||!d.target){const g={};return this.options.layoutId&&(g.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,g.pointerEvents=Fe(s==null?void 0:s.pointerEvents)||""),this.hasProjected&&!Ut(this.latestValues)&&(g.transform=u?u({},""):"none",this.hasProjected=!1),g}const f=d.animationValues||d.latestValues;this.applyTransformsToTarget(),c.transform=If(this.projectionDeltaWithTransform,this.treeScale,f),u&&(c.transform=u(f,c.transform));const{x:m,y:v}=this.projectionDelta;c.transformOrigin=`${m.origin*100}% ${v.origin*100}% 0`,d.animationValues?c.opacity=d===this?(l=(a=f.opacity)!==null&&a!==void 0?a:this.latestValues.opacity)!==null&&l!==void 0?l:1:this.preserveOpacity?this.latestValues.opacity:f.opacityExit:c.opacity=d===this?f.opacity!==void 0?f.opacity:"":f.opacityExit!==void 0?f.opacityExit:0;for(const g in Ke){if(f[g]===void 0)continue;const{correct:y,applyTo:p}=Ke[g],b=c.transform==="none"?f[g]:y(f[g],d);if(p){const S=p.length;for(let A=0;A<S;A++)c[p[A]]=b}else c[g]=b}return this.options.layoutId&&(c.pointerEvents=d===this?Fe(s==null?void 0:s.pointerEvents)||"":"none"),c}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(s=>{var a;return(a=s.currentAnimation)===null||a===void 0?void 0:a.stop()}),this.root.nodes.forEach(us),this.root.sharedNodes.clear()}}}function Nf(t){t.updateLayout()}function Ff(t){var e;const n=((e=t.resumeFrom)===null||e===void 0?void 0:e.snapshot)||t.snapshot;if(t.isLead()&&t.layout&&n&&t.hasListeners("didUpdate")){const{layoutBox:i,measuredBox:r}=t.layout,{animationType:o}=t.options,s=n.source!==t.layout.source;o==="size"?gt(d=>{const f=s?n.measuredBox[d]:n.layoutBox[d],m=ft(f);f.min=i[d].min,f.max=f.min+m}):oa(o,n.layoutBox,i)&&gt(d=>{const f=s?n.measuredBox[d]:n.layoutBox[d],m=ft(i[d]);f.max=f.min+m,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[d].max=t.relativeTarget[d].min+m)});const a=ne();Se(a,i,n.layoutBox);const l=ne();s?Se(l,t.applyTransform(r,!0),n.measuredBox):Se(l,i,n.layoutBox);const c=!na(a);let u=!1;if(!t.resumeFrom){const d=t.getClosestProjectingParent();if(d&&!d.resumeFrom){const{snapshot:f,layout:m}=d;if(f&&m){const v=J();xe(v,n.layoutBox,f.layoutBox);const g=J();xe(g,i,m.layoutBox),ia(v,g)||(u=!0),d.options.layoutRoot&&(t.relativeTarget=g,t.relativeTargetOrigin=v,t.relativeParent=d)}}}t.notifyListeners("didUpdate",{layout:i,snapshot:n,delta:l,layoutDelta:a,hasLayoutChanged:c,hasRelativeTargetChanged:u})}else if(t.isLead()){const{onExitComplete:i}=t.options;i&&i()}t.options.transition=void 0}function zf(t){ve&&Gt.totalNodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function kf(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function Wf(t){t.clearSnapshot()}function us(t){t.clearMeasurements()}function Uf(t){t.isLayoutDirty=!1}function Gf(t){const{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function ds(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function Kf(t){t.resolveTargetDelta()}function Hf(t){t.calcProjection()}function Xf(t){t.resetSkewAndRotation()}function qf(t){t.removeLeadSnapshot()}function fs(t,e,n){t.translate=q(e.translate,0,n),t.scale=q(e.scale,1,n),t.origin=e.origin,t.originPoint=e.originPoint}function hs(t,e,n,i){t.min=q(e.min,n.min,i),t.max=q(e.max,n.max,i)}function Yf(t,e,n,i){hs(t.x,e.x,n.x,i),hs(t.y,e.y,n.y,i)}function Zf(t){return t.animationValues&&t.animationValues.opacityExit!==void 0}const Jf={duration:.45,ease:[.4,0,.1,1]},ms=t=>typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),ps=ms("applewebkit/")&&!ms("chrome/")?Math.round:dt;function gs(t){t.min=ps(t.min),t.max=ps(t.max)}function Qf(t){gs(t.x),gs(t.y)}function oa(t,e,n){return t==="position"||t==="preserve-aspect"&&!ef(as(e),as(n),.2)}function th(t){var e;return t!==t.root&&((e=t.scroll)===null||e===void 0?void 0:e.wasRoot)}const eh=sa({attachResizeListener:(t,e)=>$e(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),$n={current:void 0},aa=sa({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!$n.current){const t=new eh({});t.mount(window),t.setOptions({layoutScroll:!0}),$n.current=t}return $n.current},resetTransform:(t,e)=>{t.style.transform=e!==void 0?e:"none"},checkIsScrollRoot:t=>window.getComputedStyle(t).position==="fixed"}),nh={pan:{Feature:yf},drag:{Feature:vf,ProjectionNode:aa,MeasureLayout:Qo}};function vs(t,e,n){const{props:i}=t;t.animationState&&i.whileHover&&t.animationState.setActive("whileHover",n==="Start");const r="onHover"+n,o=i[r];o&&U.postRender(()=>o(e,Me(e)))}class ih extends Ft{mount(){const{current:e}=this.node;e&&(this.unmount=eu(e,n=>(vs(this.node,n,"Start"),i=>vs(this.node,i,"End"))))}unmount(){}}class rh extends Ft{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch{e=!0}!e||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=Ee($e(this.node.current,"focus",()=>this.onFocus()),$e(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function ys(t,e,n){const{props:i}=t;t.animationState&&i.whileTap&&t.animationState.setActive("whileTap",n==="Start");const r="onTap"+(n==="End"?"":n),o=i[r];o&&U.postRender(()=>o(e,Me(e)))}class sh extends Ft{mount(){const{current:e}=this.node;e&&(this.unmount=su(e,n=>(ys(this.node,n,"Start"),(i,{success:r})=>ys(this.node,i,r?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}const Un=new WeakMap,An=new WeakMap,oh=t=>{const e=Un.get(t.target);e&&e(t)},ah=t=>{t.forEach(oh)};function lh({root:t,...e}){const n=t||document;An.has(n)||An.set(n,{});const i=An.get(n),r=JSON.stringify(e);return i[r]||(i[r]=new IntersectionObserver(ah,{root:t,...e})),i[r]}function ch(t,e,n){const i=lh(e);return Un.set(t,n),i.observe(t),()=>{Un.delete(t),i.unobserve(t)}}const uh={some:0,all:1};class dh extends Ft{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:e={}}=this.node.getProps(),{root:n,margin:i,amount:r="some",once:o}=e,s={root:n?n.current:void 0,rootMargin:i,threshold:typeof r=="number"?r:uh[r]},a=l=>{const{isIntersecting:c}=l;if(this.isInView===c||(this.isInView=c,o&&!c&&this.hasEnteredView))return;c&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",c);const{onViewportEnter:u,onViewportLeave:d}=this.node.getProps(),f=c?u:d;f&&f(l)};return ch(this.node.current,s,a)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:e,prevProps:n}=this.node;["amount","margin","root"].some(fh(e,n))&&this.startObserver()}unmount(){}}function fh({viewport:t={}},{viewport:e={}}={}){return n=>t[n]!==e[n]}const hh={inView:{Feature:dh},tap:{Feature:sh},focus:{Feature:rh},hover:{Feature:ih}},mh={layout:{ProjectionNode:aa,MeasureLayout:Qo}},Gn={current:null},la={current:!1};function ph(){if(la.current=!0,!!Zn)if(window.matchMedia){const t=window.matchMedia("(prefers-reduced-motion)"),e=()=>Gn.current=t.matches;t.addListener(e),e()}else Gn.current=!1}const gh=[...Lo,ot,Nt],vh=t=>gh.find(Vo(t)),bs=new WeakMap;function yh(t,e,n){for(const i in e){const r=e[i],o=n[i];if(at(r))t.addValue(i,r);else if(at(o))t.addValue(i,Ce(r,{owner:t}));else if(o!==r)if(t.hasValue(i)){const s=t.getValue(i);s.liveStyle===!0?s.jump(r):s.hasAnimated||s.set(r)}else{const s=t.getStaticValue(i);t.addValue(i,Ce(s!==void 0?s:r,{owner:t}))}}for(const i in n)e[i]===void 0&&t.removeValue(i);return e}const Ss=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class bh{scrapeMotionValuesFromProps(e,n,i){return{}}constructor({parent:e,props:n,presenceContext:i,reducedMotionConfig:r,blockInitialAnimation:o,visualState:s},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=Ci,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const m=$t.now();this.renderScheduledAt<m&&(this.renderScheduledAt=m,U.render(this.render,!1,!0))};const{latestValues:l,renderState:c,onUpdate:u}=s;this.onUpdate=u,this.latestValues=l,this.baseTarget={...l},this.initialValues=n.initial?{...l}:{},this.renderState=c,this.parent=e,this.props=n,this.presenceContext=i,this.depth=e?e.depth+1:0,this.reducedMotionConfig=r,this.options=a,this.blockInitialAnimation=!!o,this.isControllingVariants=nn(n),this.isVariantNode=Fs(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);const{willChange:d,...f}=this.scrapeMotionValuesFromProps(n,{},this);for(const m in f){const v=f[m];l[m]!==void 0&&at(v)&&v.set(l[m],!1)}}mount(e){this.current=e,bs.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((n,i)=>this.bindToMotionValue(i,n)),la.current||ph(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:Gn.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){bs.delete(this.current),this.projection&&this.projection.unmount(),jt(this.notifyUpdate),jt(this.render),this.valueSubscriptions.forEach(e=>e()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const e in this.events)this.events[e].clear();for(const e in this.features){const n=this.features[e];n&&(n.unmount(),n.isMounted=!1)}this.current=null}bindToMotionValue(e,n){this.valueSubscriptions.has(e)&&this.valueSubscriptions.get(e)();const i=Xt.has(e),r=n.on("change",a=>{this.latestValues[e]=a,this.props.onUpdate&&U.preRender(this.notifyUpdate),i&&this.projection&&(this.projection.isTransformDirty=!0)}),o=n.on("renderRequest",this.scheduleRender);let s;window.MotionCheckAppearSync&&(s=window.MotionCheckAppearSync(this,e,n)),this.valueSubscriptions.set(e,()=>{r(),o(),s&&s(),n.owner&&n.stop()})}sortNodePosition(e){return!this.current||!this.sortInstanceNodePosition||this.type!==e.type?0:this.sortInstanceNodePosition(this.current,e.current)}updateFeatures(){let e="animation";for(e in oe){const n=oe[e];if(!n)continue;const{isEnabled:i,Feature:r}=n;if(!this.features[e]&&r&&i(this.props)&&(this.features[e]=new r(this)),this.features[e]){const o=this.features[e];o.isMounted?o.update():(o.mount(),o.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):J()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,n){this.latestValues[e]=n}update(e,n){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=n;for(let i=0;i<Ss.length;i++){const r=Ss[i];this.propEventSubscriptions[r]&&(this.propEventSubscriptions[r](),delete this.propEventSubscriptions[r]);const o="on"+r,s=e[o];s&&(this.propEventSubscriptions[r]=this.on(r,s))}this.prevMotionValues=yh(this,this.scrapeMotionValuesFromProps(e,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue(),this.onUpdate&&this.onUpdate(this)}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(e){const n=this.getClosestVariantNode();if(n)return n.variantChildren&&n.variantChildren.add(e),()=>n.variantChildren.delete(e)}addValue(e,n){const i=this.values.get(e);n!==i&&(i&&this.removeValue(e),this.bindToMotionValue(e,n),this.values.set(e,n),this.latestValues[e]=n.get())}removeValue(e){this.values.delete(e);const n=this.valueSubscriptions.get(e);n&&(n(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,n){if(this.props.values&&this.props.values[e])return this.props.values[e];let i=this.values.get(e);return i===void 0&&n!==void 0&&(i=Ce(n===null?void 0:n,{owner:this}),this.addValue(e,i)),i}readValue(e,n){var i;let r=this.latestValues[e]!==void 0||!this.current?this.latestValues[e]:(i=this.getBaseTargetFromProps(this.props,e))!==null&&i!==void 0?i:this.readValueFromInstance(this.current,e,this.options);return r!=null&&(typeof r=="string"&&(Mo(r)||xo(r))?r=parseFloat(r):!vh(r)&&Nt.test(n)&&(r=Ao(e,n)),this.setBaseTarget(e,at(r)?r.get():r)),at(r)?r.get():r}setBaseTarget(e,n){this.baseTarget[e]=n}getBaseTarget(e){var n;const{initial:i}=this.props;let r;if(typeof i=="string"||typeof i=="object"){const s=ri(this.props,i,(n=this.presenceContext)===null||n===void 0?void 0:n.custom);s&&(r=s[e])}if(i&&r!==void 0)return r;const o=this.getBaseTargetFromProps(this.props,e);return o!==void 0&&!at(o)?o:this.initialValues[e]!==void 0&&r===void 0?void 0:this.baseTarget[e]}on(e,n){return this.events[e]||(this.events[e]=new yi),this.events[e].add(n)}notify(e,...n){this.events[e]&&this.events[e].notify(...n)}}class ca extends bh{constructor(){super(...arguments),this.KeyframeResolver=_o}sortInstanceNodePosition(e,n){return e.compareDocumentPosition(n)&2?1:-1}getBaseTargetFromProps(e,n){return e.style?e.style[n]:void 0}removeValueFromRenderState(e,{vars:n,style:i}){delete n[e],delete i[e]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:e}=this.props;at(e)&&(this.childSubscription=e.on("change",n=>{this.current&&(this.current.textContent=`${n}`)}))}}function Sh(t){return window.getComputedStyle(t)}class xh extends ca{constructor(){super(...arguments),this.type="html",this.renderInstance=qs}readValueFromInstance(e,n){if(Xt.has(n)){const i=Pi(n);return i&&i.default||0}else{const i=Sh(e),r=(Ks(n)?i.getPropertyValue(n):i[n])||0;return typeof r=="string"?r.trim():r}}measureInstanceViewportBox(e,{transformPagePoint:n}){return Zo(e,n)}build(e,n,i){ai(e,n,i.transformTemplate)}scrapeMotionValuesFromProps(e,n,i){return di(e,n,i)}}class Th extends ca{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=J}getBaseTargetFromProps(e,n){return e[n]}readValueFromInstance(e,n){if(Xt.has(n)){const i=Pi(n);return i&&i.default||0}return n=Ys.has(n)?n:ei(n),e.getAttribute(n)}scrapeMotionValuesFromProps(e,n,i){return Qs(e,n,i)}build(e,n,i){li(e,n,this.isSVGTag,i.transformTemplate)}renderInstance(e,n,i,r){Zs(e,n,i,r)}mount(e){this.isSVGTag=ui(e.tagName),super.mount(e)}}const Ph=(t,e)=>ii(t)?new Th(e):new xh(e,{allowProjection:t!==h.Fragment}),Ch=Xc({...Kd,...hh,...nh,...mh},Ph),Mh=cc(Ch);export{Vs as C,Rh as R,Ah as a,$h as b,Za as c,Mh as m};
