import{g as te,F as oe,m as re,f as L,h as se,r as l,C as W,d as N,w as A,x as ae,y as ne,z as le,D as ie,E as ce,G as q,H as de,W as ue,j as s,S as P,T as ge,B as v,J as me}from"./index-P7H5iTop.js";import{u as pe,a as he,b as Ce,c as fe}from"./usersApi-CReKEQkX.js";import{F as j,I as z}from"./index-C73uzCkN.js";import{m as ye,C as be,b as xe,c as Se}from"./proxy-OhLL-xTq.js";import{R as ve,F as je,M as $e,P as Ie,a as Te}from"./Table-bE3bupkb.js";import"./useMutation-BwIlDXdC.js";import"./Pagination-DPfaT0Az.js";const we=e=>{const{paddingXXS:a,lineWidth:n,tagPaddingHorizontal:t,componentCls:r,calc:g}=e,i=g(t).sub(n).equal(),u=g(a).sub(n).equal();return{[r]:Object.assign(Object.assign({},se(e)),{display:"inline-block",height:"auto",marginInlineEnd:e.marginXS,paddingInline:i,fontSize:e.tagFontSize,lineHeight:e.tagLineHeight,whiteSpace:"nowrap",background:e.defaultBg,border:`${L(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusSM,opacity:1,transition:`all ${e.motionDurationMid}`,textAlign:"start",position:"relative",[`&${r}-rtl`]:{direction:"rtl"},"&, a, a:hover":{color:e.defaultColor},[`${r}-close-icon`]:{marginInlineStart:u,fontSize:e.tagIconSize,color:e.colorTextDescription,cursor:"pointer",transition:`all ${e.motionDurationMid}`,"&:hover":{color:e.colorTextHeading}},[`&${r}-has-color`]:{borderColor:"transparent",[`&, a, a:hover, ${e.iconCls}-close, ${e.iconCls}-close:hover`]:{color:e.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",[`&:not(${r}-checkable-checked):hover`]:{color:e.colorPrimary,backgroundColor:e.colorFillSecondary},"&:active, &-checked":{color:e.colorTextLightSolid},"&-checked":{backgroundColor:e.colorPrimary,"&:hover":{backgroundColor:e.colorPrimaryHover}},"&:active":{backgroundColor:e.colorPrimaryActive}},"&-hidden":{display:"none"},[`> ${e.iconCls} + span, > span + ${e.iconCls}`]:{marginInlineStart:i}}),[`${r}-borderless`]:{borderColor:"transparent",background:e.tagBorderlessBg}}},M=e=>{const{lineWidth:a,fontSizeIcon:n,calc:t}=e,r=e.fontSizeSM;return re(e,{tagFontSize:r,tagLineHeight:L(t(e.lineHeightSM).mul(r).equal()),tagIconSize:t(n).sub(t(a).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:e.defaultBg})},R=e=>({defaultBg:new oe(e.colorFillQuaternary).onBackground(e.colorBgContainer).toHexString(),defaultColor:e.colorText}),D=te("Tag",e=>{const a=M(e);return we(a)},R);var Oe=function(e,a){var n={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&a.indexOf(t)<0&&(n[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,t=Object.getOwnPropertySymbols(e);r<t.length;r++)a.indexOf(t[r])<0&&Object.prototype.propertyIsEnumerable.call(e,t[r])&&(n[t[r]]=e[t[r]]);return n};const Pe=l.forwardRef((e,a)=>{const{prefixCls:n,style:t,className:r,checked:g,onChange:i,onClick:u}=e,c=Oe(e,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:f,tag:d}=l.useContext(W),m=x=>{i==null||i(!g),u==null||u(x)},h=f("tag",n),[$,b,p]=D(h),I=N(h,`${h}-checkable`,{[`${h}-checkable-checked`]:g},d==null?void 0:d.className,r,b,p);return $(l.createElement("span",Object.assign({},c,{ref:a,style:Object.assign(Object.assign({},t),d==null?void 0:d.style),className:I,onClick:m})))}),ke=e=>ae(e,(a,n)=>{let{textColor:t,lightBorderColor:r,lightColor:g,darkColor:i}=n;return{[`${e.componentCls}${e.componentCls}-${a}`]:{color:t,background:g,borderColor:r,"&-inverse":{color:e.colorTextLightSolid,background:i,borderColor:i},[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}}),ze=A(["Tag","preset"],e=>{const a=M(e);return ke(a)},R);function Ee(e){return typeof e!="string"?e:e.charAt(0).toUpperCase()+e.slice(1)}const k=(e,a,n)=>{const t=Ee(n);return{[`${e.componentCls}${e.componentCls}-${a}`]:{color:e[`color${n}`],background:e[`color${t}Bg`],borderColor:e[`color${t}Border`],[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}},Be=A(["Tag","status"],e=>{const a=M(e);return[k(a,"success","Success"),k(a,"processing","Info"),k(a,"error","Error"),k(a,"warning","Warning")]},R);var Fe=function(e,a){var n={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&a.indexOf(t)<0&&(n[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,t=Object.getOwnPropertySymbols(e);r<t.length;r++)a.indexOf(t[r])<0&&Object.prototype.propertyIsEnumerable.call(e,t[r])&&(n[t[r]]=e[t[r]]);return n};const Ne=l.forwardRef((e,a)=>{const{prefixCls:n,className:t,rootClassName:r,style:g,children:i,icon:u,color:c,onClose:f,bordered:d=!0,visible:m}=e,h=Fe(e,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:$,direction:b,tag:p}=l.useContext(W),[I,x]=l.useState(!0),E=ne(h,["closeIcon","closable"]);l.useEffect(()=>{m!==void 0&&x(m)},[m]);const w=le(c),O=ie(c),T=w||O,B=Object.assign(Object.assign({backgroundColor:c&&!T?c:void 0},p==null?void 0:p.style),g),o=$("tag",n),[C,J,Q]=D(o),G=N(o,p==null?void 0:p.className,{[`${o}-${c}`]:T,[`${o}-has-color`]:c&&!T,[`${o}-hidden`]:!I,[`${o}-rtl`]:b==="rtl",[`${o}-borderless`]:!d},t,r,J,Q),U=S=>{S.stopPropagation(),f==null||f(S),!S.defaultPrevented&&x(!1)},[,K]=ce(q(e),q(p),{closable:!1,closeIconRender:S=>{const ee=l.createElement("span",{className:`${o}-close-icon`,onClick:U},S);return de(S,ee,y=>({onClick:V=>{var F;(F=y==null?void 0:y.onClick)===null||F===void 0||F.call(y,V),U(V)},className:N(y==null?void 0:y.className,`${o}-close-icon`)}))}}),Y=typeof h.onClick=="function"||i&&i.type==="a",H=u||null,Z=H?l.createElement(l.Fragment,null,H,i&&l.createElement("span",null,i)):i,_=l.createElement("span",Object.assign({},E,{ref:a,className:G,style:B}),Z,K,w&&l.createElement(ze,{key:"preset",prefixCls:o}),O&&l.createElement(Be,{key:"status",prefixCls:o}));return C(Y?l.createElement(ue,{component:"Tag"},_):_)}),X=Ne;X.CheckableTag=Pe;const{Title:Me}=ge,{Search:Re}=z;function Ae(){const[e,a]=l.useState(""),[n,t]=l.useState(1),[r,g]=l.useState(10),[i,u]=l.useState(!1),[c,f]=l.useState(null),[d]=j.useForm(),{data:m,isLoading:h,refetch:$}=pe({page:n,pageSize:r,keyword:e}),b=he(),p=Ce(),I=fe(),x=[{title:"ID",dataIndex:"id",key:"id",width:80},{title:"用户名",dataIndex:"username",key:"username",ellipsis:!0},{title:"邮箱",dataIndex:"email",key:"email",ellipsis:!0},{title:"角色",dataIndex:"role",key:"role",render:o=>s.jsx(X,{color:o==="admin"?"red":"blue",children:o||"user"})},{title:"创建时间",dataIndex:"created_at",key:"created_at",render:o=>o?new Date(o).toLocaleDateString():"-"},{title:"操作",key:"actions",width:150,render:(o,C)=>s.jsxs(P,{children:[s.jsx(v,{type:"link",icon:s.jsx(me,{}),onClick:()=>O(C),children:"编辑"}),s.jsx(Ie,{title:"确定删除这个用户吗？",onConfirm:()=>T(C.id),okText:"确定",cancelText:"取消",children:s.jsx(v,{type:"link",danger:!0,icon:s.jsx(Te,{}),children:"删除"})})]})}],E=o=>{a(o),t(1)},w=()=>{f(null),d.resetFields(),u(!0)},O=o=>{f(o),d.setFieldsValue({username:o.username,email:o.email}),u(!0)},T=o=>{I.mutate(o)},B=async o=>{try{c?await p.mutateAsync({id:c.id,data:o}):await b.mutateAsync(o),u(!1),d.resetFields()}catch{}};return s.jsxs(ye.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},children:[s.jsx(be,{children:s.jsxs(P,{direction:"vertical",size:"large",style:{width:"100%"},children:[s.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[s.jsx(Me,{level:3,style:{margin:0},children:"用户管理"}),s.jsxs(P,{children:[s.jsx(Re,{placeholder:"搜索用户名或邮箱",allowClear:!0,onSearch:E,style:{width:250},enterButton:s.jsx(xe,{})}),s.jsx(v,{icon:s.jsx(ve,{}),onClick:()=>$(),loading:h,children:"刷新"}),s.jsx(v,{type:"primary",icon:s.jsx(Se,{}),onClick:w,children:"新建用户"})]})]}),s.jsx(je,{columns:x,dataSource:(m==null?void 0:m.items)||[],rowKey:"id",loading:h,pagination:{current:n,pageSize:r,total:(m==null?void 0:m.total)||0,showSizeChanger:!0,showQuickJumper:!0,showTotal:(o,C)=>`第 ${C[0]}-${C[1]} 条，共 ${o} 条`,onChange:(o,C)=>{t(o),g(C||10)}}})]})}),s.jsx($e,{title:c?"编辑用户":"新建用户",open:i,onCancel:()=>{u(!1),d.resetFields()},footer:null,destroyOnClose:!0,children:s.jsxs(j,{form:d,layout:"vertical",onFinish:B,children:[s.jsx(j.Item,{name:"username",label:"用户名",rules:[{required:!0,message:"请输入用户名"},{min:2,message:"用户名至少2位字符"}],children:s.jsx(z,{placeholder:"请输入用户名"})}),s.jsx(j.Item,{name:"email",label:"邮箱",rules:[{required:!0,message:"请输入邮箱"},{type:"email",message:"请输入有效的邮箱地址"}],children:s.jsx(z,{placeholder:"请输入邮箱"})}),!c&&s.jsx(j.Item,{name:"password",label:"密码",rules:[{required:!0,message:"请输入密码"},{min:6,message:"密码至少6位字符"}],children:s.jsx(z.Password,{placeholder:"请输入密码"})}),s.jsx(j.Item,{style:{marginBottom:0,textAlign:"right"},children:s.jsxs(P,{children:[s.jsx(v,{onClick:()=>u(!1),children:"取消"}),s.jsx(v,{type:"primary",htmlType:"submit",loading:b.isPending||p.isPending,children:c?"更新":"创建"})]})})]})})]})}export{Ae as default};
