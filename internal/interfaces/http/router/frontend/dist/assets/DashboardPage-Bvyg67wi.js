import{c as j,r,C as re,d as M,e as ue,g as fe,m as ve,f as h,h as xe,i as $e,k as De,l as me,n as _e,o as ge,p as Ve,I as he,_ as ye,q as Ae,s as We,t as ke,b as Fe,j as o,S as pe,T as Ge,R as ie,v as ae,A as G}from"./index-P7H5iTop.js";import{a as V,R as Se,m as H,C as D}from"./proxy-OhLL-xTq.js";import{e as Xe,P as Ue,D as qe}from"./Pagination-DPfaT0Az.js";const oe=j.createContext({});oe.Consumer;var be=function(t,e){var n={};for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&e.indexOf(i)<0&&(n[i]=t[i]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,i=Object.getOwnPropertySymbols(t);a<i.length;a++)e.indexOf(i[a])<0&&Object.prototype.propertyIsEnumerable.call(t,i[a])&&(n[i[a]]=t[i[a]]);return n};const Je=t=>{var{prefixCls:e,className:n,avatar:i,title:a,description:d}=t,m=be(t,["prefixCls","className","avatar","title","description"]);const{getPrefixCls:c}=r.useContext(re),l=c("list",e),p=M(`${l}-item-meta`,n),u=j.createElement("div",{className:`${l}-item-meta-content`},a&&j.createElement("h4",{className:`${l}-item-meta-title`},a),d&&j.createElement("div",{className:`${l}-item-meta-description`},d));return j.createElement("div",Object.assign({},m,{className:p}),i&&j.createElement("div",{className:`${l}-item-meta-avatar`},i),(a||d)&&u)},Ke=j.forwardRef((t,e)=>{const{prefixCls:n,children:i,actions:a,extra:d,styles:m,className:c,classNames:l,colStyle:p}=t,u=be(t,["prefixCls","children","actions","extra","styles","className","classNames","colStyle"]),{grid:f,itemLayout:s}=r.useContext(oe),{getPrefixCls:b,list:x}=r.useContext(re),E=g=>{var $,O;return M((O=($=x==null?void 0:x.item)===null||$===void 0?void 0:$.classNames)===null||O===void 0?void 0:O[g],l==null?void 0:l[g])},N=g=>{var $,O;return Object.assign(Object.assign({},(O=($=x==null?void 0:x.item)===null||$===void 0?void 0:$.styles)===null||O===void 0?void 0:O[g]),m==null?void 0:m[g])},R=()=>{let g=!1;return r.Children.forEach(i,$=>{typeof $=="string"&&(g=!0)}),g&&r.Children.count(i)>1},P=()=>s==="vertical"?!!d:!R(),C=b("list",n),I=a&&a.length>0&&j.createElement("ul",{className:M(`${C}-item-action`,E("actions")),key:"actions",style:N("actions")},a.map((g,$)=>j.createElement("li",{key:`${C}-item-action-${$}`},g,$!==a.length-1&&j.createElement("em",{className:`${C}-item-action-split`})))),T=f?"div":"li",B=j.createElement(T,Object.assign({},u,f?{}:{ref:e},{className:M(`${C}-item`,{[`${C}-item-no-flex`]:!P()},c)}),s==="vertical"&&d?[j.createElement("div",{className:`${C}-item-main`,key:"content"},i,I),j.createElement("div",{className:M(`${C}-item-extra`,E("extra")),key:"extra",style:N("extra")},d)]:[i,I,ue(d,{key:"extra"})]);return f?j.createElement(V,{ref:e,flex:1,style:p},B):B}),Ce=Ke;Ce.Meta=Je;const Ye=t=>{const{listBorderedCls:e,componentCls:n,paddingLG:i,margin:a,itemPaddingSM:d,itemPaddingLG:m,marginLG:c,borderRadiusLG:l}=t;return{[e]:{border:`${h(t.lineWidth)} ${t.lineType} ${t.colorBorder}`,borderRadius:l,[`${n}-header,${n}-footer,${n}-item`]:{paddingInline:i},[`${n}-pagination`]:{margin:`${h(a)} ${h(c)}`}},[`${e}${n}-sm`]:{[`${n}-item,${n}-header,${n}-footer`]:{padding:d}},[`${e}${n}-lg`]:{[`${n}-item,${n}-header,${n}-footer`]:{padding:m}}}},Qe=t=>{const{componentCls:e,screenSM:n,screenMD:i,marginLG:a,marginSM:d,margin:m}=t;return{[`@media screen and (max-width:${i}px)`]:{[e]:{[`${e}-item`]:{[`${e}-item-action`]:{marginInlineStart:a}}},[`${e}-vertical`]:{[`${e}-item`]:{[`${e}-item-extra`]:{marginInlineStart:a}}}},[`@media screen and (max-width: ${n}px)`]:{[e]:{[`${e}-item`]:{flexWrap:"wrap",[`${e}-action`]:{marginInlineStart:d}}},[`${e}-vertical`]:{[`${e}-item`]:{flexWrap:"wrap-reverse",[`${e}-item-main`]:{minWidth:t.contentWidth},[`${e}-item-extra`]:{margin:`auto auto ${h(m)}`}}}}}},Ze=t=>{const{componentCls:e,antCls:n,controlHeight:i,minHeight:a,paddingSM:d,marginLG:m,padding:c,itemPadding:l,colorPrimary:p,itemPaddingSM:u,itemPaddingLG:f,paddingXS:s,margin:b,colorText:x,colorTextDescription:E,motionDurationSlow:N,lineWidth:R,headerBg:P,footerBg:C,emptyTextPadding:I,metaMarginBottom:T,avatarMarginRight:B,titleMarginBottom:g,descriptionFontSize:$}=t;return{[e]:Object.assign(Object.assign({},xe(t)),{position:"relative","*":{outline:"none"},[`${e}-header`]:{background:P},[`${e}-footer`]:{background:C},[`${e}-header, ${e}-footer`]:{paddingBlock:d},[`${e}-pagination`]:{marginBlockStart:m,[`${n}-pagination-options`]:{textAlign:"start"}},[`${e}-spin`]:{minHeight:a,textAlign:"center"},[`${e}-items`]:{margin:0,padding:0,listStyle:"none"},[`${e}-item`]:{display:"flex",alignItems:"center",justifyContent:"space-between",padding:l,color:x,[`${e}-item-meta`]:{display:"flex",flex:1,alignItems:"flex-start",maxWidth:"100%",[`${e}-item-meta-avatar`]:{marginInlineEnd:B},[`${e}-item-meta-content`]:{flex:"1 0",width:0,color:x},[`${e}-item-meta-title`]:{margin:`0 0 ${h(t.marginXXS)} 0`,color:x,fontSize:t.fontSize,lineHeight:t.lineHeight,"> a":{color:x,transition:`all ${N}`,"&:hover":{color:p}}},[`${e}-item-meta-description`]:{color:E,fontSize:$,lineHeight:t.lineHeight}},[`${e}-item-action`]:{flex:"0 0 auto",marginInlineStart:t.marginXXL,padding:0,fontSize:0,listStyle:"none","& > li":{position:"relative",display:"inline-block",padding:`0 ${h(s)}`,color:E,fontSize:t.fontSize,lineHeight:t.lineHeight,textAlign:"center","&:first-child":{paddingInlineStart:0}},[`${e}-item-action-split`]:{position:"absolute",insetBlockStart:"50%",insetInlineEnd:0,width:R,height:t.calc(t.fontHeight).sub(t.calc(t.marginXXS).mul(2)).equal(),transform:"translateY(-50%)",backgroundColor:t.colorSplit}}},[`${e}-empty`]:{padding:`${h(c)} 0`,color:E,fontSize:t.fontSizeSM,textAlign:"center"},[`${e}-empty-text`]:{padding:I,color:t.colorTextDisabled,fontSize:t.fontSize,textAlign:"center"},[`${e}-item-no-flex`]:{display:"block"}}),[`${e}-grid ${n}-col > ${e}-item`]:{display:"block",maxWidth:"100%",marginBlockEnd:b,paddingBlock:0,borderBlockEnd:"none"},[`${e}-vertical ${e}-item`]:{alignItems:"initial",[`${e}-item-main`]:{display:"block",flex:1},[`${e}-item-extra`]:{marginInlineStart:m},[`${e}-item-meta`]:{marginBlockEnd:T,[`${e}-item-meta-title`]:{marginBlockStart:0,marginBlockEnd:g,color:x,fontSize:t.fontSizeLG,lineHeight:t.lineHeightLG}},[`${e}-item-action`]:{marginBlockStart:c,marginInlineStart:"auto","> li":{padding:`0 ${h(c)}`,"&:first-child":{paddingInlineStart:0}}}},[`${e}-split ${e}-item`]:{borderBlockEnd:`${h(t.lineWidth)} ${t.lineType} ${t.colorSplit}`,"&:last-child":{borderBlockEnd:"none"}},[`${e}-split ${e}-header`]:{borderBlockEnd:`${h(t.lineWidth)} ${t.lineType} ${t.colorSplit}`},[`${e}-split${e}-empty ${e}-footer`]:{borderTop:`${h(t.lineWidth)} ${t.lineType} ${t.colorSplit}`},[`${e}-loading ${e}-spin-nested-loading`]:{minHeight:i},[`${e}-split${e}-something-after-last-item ${n}-spin-container > ${e}-items > ${e}-item:last-child`]:{borderBlockEnd:`${h(t.lineWidth)} ${t.lineType} ${t.colorSplit}`},[`${e}-lg ${e}-item`]:{padding:f},[`${e}-sm ${e}-item`]:{padding:u},[`${e}:not(${e}-vertical)`]:{[`${e}-item-no-flex`]:{[`${e}-item-action`]:{float:"right"}}}}},et=t=>({contentWidth:220,itemPadding:`${h(t.paddingContentVertical)} 0`,itemPaddingSM:`${h(t.paddingContentVerticalSM)} ${h(t.paddingContentHorizontal)}`,itemPaddingLG:`${h(t.paddingContentVerticalLG)} ${h(t.paddingContentHorizontalLG)}`,headerBg:"transparent",footerBg:"transparent",emptyTextPadding:t.padding,metaMarginBottom:t.padding,avatarMarginRight:t.padding,titleMarginBottom:t.paddingSM,descriptionFontSize:t.fontSize}),tt=fe("List",t=>{const e=ve(t,{listBorderedCls:`${t.componentCls}-bordered`,minHeight:t.controlHeightLG});return[Ze(e),Ye(e),Qe(e)]},et);var nt=function(t,e){var n={};for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&e.indexOf(i)<0&&(n[i]=t[i]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,i=Object.getOwnPropertySymbols(t);a<i.length;a++)e.indexOf(i[a])<0&&Object.prototype.propertyIsEnumerable.call(t,i[a])&&(n[i[a]]=t[i[a]]);return n};function it(t,e){var{pagination:n=!1,prefixCls:i,bordered:a=!1,split:d=!0,className:m,rootClassName:c,style:l,children:p,itemLayout:u,loadMore:f,grid:s,dataSource:b=[],size:x,header:E,footer:N,loading:R=!1,rowKey:P,renderItem:C,locale:I}=t,T=nt(t,["pagination","prefixCls","bordered","split","className","rootClassName","style","children","itemLayout","loadMore","grid","dataSource","size","header","footer","loading","rowKey","renderItem","locale"]);const B=n&&typeof n=="object"?n:{},[g,$]=r.useState(B.defaultCurrent||1),[O,J]=r.useState(B.defaultPageSize||10),{getPrefixCls:A,direction:K,className:Y,style:je}=$e("list"),{renderEmpty:Q}=r.useContext(re),Ee={current:1,total:0},se=v=>(S,w)=>{var ne;$(S),J(w),n&&((ne=n==null?void 0:n[v])===null||ne===void 0||ne.call(n,S,w))},Oe=se("onChange"),ze=se("onShowSizeChange"),we=(v,S)=>{if(!C)return null;let w;return typeof P=="function"?w=P(v):P?w=v[P]:w=v.key,w||(w=`list-item-${S}`),r.createElement(r.Fragment,{key:w},C(v,S))},Ne=()=>!!(f||n||N),y=A("list",i),[Pe,Ie,Be]=tt(y);let L=R;typeof L=="boolean"&&(L={spinning:L});const Z=!!(L!=null&&L.spinning),Me=De(x);let W="";switch(Me){case"large":W="lg";break;case"small":W="sm";break}const Re=M(y,{[`${y}-vertical`]:u==="vertical",[`${y}-${W}`]:W,[`${y}-split`]:d,[`${y}-bordered`]:a,[`${y}-loading`]:Z,[`${y}-grid`]:!!s,[`${y}-something-after-last-item`]:Ne(),[`${y}-rtl`]:K==="rtl"},Y,m,c,Ie,Be),z=Xe(Ee,{total:b.length,current:g,pageSize:O},n||{}),le=Math.ceil(z.total/z.pageSize);z.current>le&&(z.current=le);const ce=n&&r.createElement("div",{className:M(`${y}-pagination`)},r.createElement(Ue,Object.assign({align:"end"},z,{onChange:Oe,onShowSizeChange:ze})));let ee=me(b);n&&b.length>(z.current-1)*z.pageSize&&(ee=me(b).splice((z.current-1)*z.pageSize,z.pageSize));const Te=Object.keys(s||{}).some(v=>["xs","sm","md","lg","xl","xxl"].includes(v)),de=_e(Te),k=r.useMemo(()=>{for(let v=0;v<ge.length;v+=1){const S=ge[v];if(de[S])return S}},[de]),Le=r.useMemo(()=>{if(!s)return;const v=k&&s[k]?s[k]:s.column;if(v)return{width:`${100/v}%`,maxWidth:`${100/v}%`}},[JSON.stringify(s),k]);let te=Z&&r.createElement("div",{style:{minHeight:53}});if(ee.length>0){const v=ee.map((S,w)=>we(S,w));te=s?r.createElement(Se,{gutter:s.gutter},r.Children.map(v,S=>r.createElement("div",{key:S==null?void 0:S.key,style:Le},S))):r.createElement("ul",{className:`${y}-items`},v)}else!p&&!Z&&(te=r.createElement("div",{className:`${y}-empty-text`},(I==null?void 0:I.emptyText)||(Q==null?void 0:Q("List"))||r.createElement(qe,{componentName:"List"})));const F=z.position||"bottom",He=r.useMemo(()=>({grid:s,itemLayout:u}),[JSON.stringify(s),u]);return Pe(r.createElement(oe.Provider,{value:He},r.createElement("div",Object.assign({ref:e,style:Object.assign(Object.assign({},je),l),className:Re},T),(F==="top"||F==="both")&&ce,E&&r.createElement("div",{className:`${y}-header`},E),r.createElement(Ve,Object.assign({},L),te,p),N&&r.createElement("div",{className:`${y}-footer`},N),f||(F==="bottom"||F==="both")&&ce)))}const at=r.forwardRef(it),q=at;q.Item=Ce;var rt={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M917 211.1l-199.2 24c-6.6.8-9.4 8.9-4.7 13.6l59.3 59.3-226 226-101.8-101.7c-6.3-6.3-16.4-6.2-22.6 0L100.3 754.1a8.03 8.03 0 000 11.3l45 45.2c3.1 3.1 8.2 3.1 11.3 0L433.3 534 535 635.7c6.3 6.2 16.4 6.2 22.6 0L829 364.5l59.3 59.3a8.01 8.01 0 0013.6-4.7l24-199.2c.7-5.1-3.7-9.5-8.9-8.8z"}}]},name:"rise",theme:"outlined"},ot=function(e,n){return r.createElement(he,ye({},e,{ref:n,icon:rt}))},st=r.forwardRef(ot),lt={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M868 160h-92v-40c0-4.4-3.6-8-8-8H256c-4.4 0-8 3.6-8 8v40h-92a44 44 0 00-44 44v148c0 81.7 60 149.6 138.2 162C265.7 630.2 359 721.7 476 734.5v105.2H280c-17.7 0-32 14.3-32 32V904c0 4.4 3.6 8 8 8h512c4.4 0 8-3.6 8-8v-32.3c0-17.7-14.3-32-32-32H548V734.5C665 721.7 758.3 630.2 773.8 514 852 501.6 912 433.7 912 352V204a44 44 0 00-44-44zM184 352V232h64v207.6a91.99 91.99 0 01-64-87.6zm520 128c0 49.1-19.1 95.4-53.9 130.1-34.8 34.8-81 53.9-130.1 53.9h-16c-49.1 0-95.4-19.1-130.1-53.9-34.8-34.8-53.9-81-53.9-130.1V184h384v296zm136-128c0 41-26.9 75.8-64 87.6V232h64v120z"}}]},name:"trophy",theme:"outlined"},ct=function(e,n){return r.createElement(he,ye({},e,{ref:n,icon:lt}))},dt=r.forwardRef(ct);const mt=t=>{const{value:e,formatter:n,precision:i,decimalSeparator:a,groupSeparator:d="",prefixCls:m}=t;let c;if(typeof n=="function")c=n(e);else{const l=String(e),p=l.match(/^(-?)(\d*)(\.(\d+))?$/);if(!p||l==="-")c=l;else{const u=p[1];let f=p[2]||"0",s=p[4]||"";f=f.replace(/\B(?=(\d{3})+(?!\d))/g,d),typeof i=="number"&&(s=s.padEnd(i,"0").slice(0,i>0?i:0)),s&&(s=`${a}${s}`),c=[r.createElement("span",{key:"int",className:`${m}-content-value-int`},u,f),s&&r.createElement("span",{key:"decimal",className:`${m}-content-value-decimal`},s)]}}return r.createElement("span",{className:`${m}-content-value`},c)},gt=t=>{const{componentCls:e,marginXXS:n,padding:i,colorTextDescription:a,titleFontSize:d,colorTextHeading:m,contentFontSize:c,fontFamily:l}=t;return{[e]:Object.assign(Object.assign({},xe(t)),{[`${e}-title`]:{marginBottom:n,color:a,fontSize:d},[`${e}-skeleton`]:{paddingTop:i},[`${e}-content`]:{color:m,fontSize:c,fontFamily:l,[`${e}-content-value`]:{display:"inline-block",direction:"ltr"},[`${e}-content-prefix, ${e}-content-suffix`]:{display:"inline-block"},[`${e}-content-prefix`]:{marginInlineEnd:n},[`${e}-content-suffix`]:{marginInlineStart:n}}})}},pt=t=>{const{fontSizeHeading3:e,fontSize:n}=t;return{titleFontSize:n,contentFontSize:e}},ut=fe("Statistic",t=>{const e=ve(t,{});return[gt(e)]},pt);var ft=function(t,e){var n={};for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&e.indexOf(i)<0&&(n[i]=t[i]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,i=Object.getOwnPropertySymbols(t);a<i.length;a++)e.indexOf(i[a])<0&&Object.prototype.propertyIsEnumerable.call(t,i[a])&&(n[i[a]]=t[i[a]]);return n};const _=t=>{const{prefixCls:e,className:n,rootClassName:i,style:a,valueStyle:d,value:m=0,title:c,valueRender:l,prefix:p,suffix:u,loading:f=!1,formatter:s,precision:b,decimalSeparator:x=".",groupSeparator:E=",",onMouseEnter:N,onMouseLeave:R}=t,P=ft(t,["prefixCls","className","rootClassName","style","valueStyle","value","title","valueRender","prefix","suffix","loading","formatter","precision","decimalSeparator","groupSeparator","onMouseEnter","onMouseLeave"]),{getPrefixCls:C,direction:I,className:T,style:B}=$e("statistic"),g=C("statistic",e),[$,O,J]=ut(g),A=r.createElement(mt,{decimalSeparator:x,groupSeparator:E,prefixCls:g,formatter:s,precision:b,value:m}),K=M(g,{[`${g}-rtl`]:I==="rtl"},T,n,i,O,J),Y=Ae(P,{aria:!0,data:!0});return $(r.createElement("div",Object.assign({},Y,{className:K,style:Object.assign(Object.assign({},B),a),onMouseEnter:N,onMouseLeave:R}),c&&r.createElement("div",{className:`${g}-title`},c),r.createElement(We,{paragraph:!1,loading:f,className:`${g}-skeleton`},r.createElement("div",{style:d,className:`${g}-content`},p&&r.createElement("span",{className:`${g}-content-prefix`},p),l?l(A):A,u&&r.createElement("span",{className:`${g}-content-suffix`},u)))))},vt=[["Y",1e3*60*60*24*365],["M",1e3*60*60*24*30],["D",1e3*60*60*24],["H",1e3*60*60],["m",1e3*60],["s",1e3],["S",1]];function xt(t,e){let n=t;const i=/\[[^\]]*]/g,a=(e.match(i)||[]).map(l=>l.slice(1,-1)),d=e.replace(i,"[]"),m=vt.reduce((l,p)=>{let[u,f]=p;if(l.includes(u)){const s=Math.floor(n/f);return n-=s*f,l.replace(new RegExp(`${u}+`,"g"),b=>{const x=b.length;return s.toString().padStart(x,"0")})}return l},d);let c=0;return m.replace(i,()=>{const l=a[c];return c+=1,l})}function $t(t,e){const{format:n=""}=e,i=new Date(t).getTime(),a=Date.now(),d=Math.max(i-a,0);return xt(d,n)}var ht=function(t,e){var n={};for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&e.indexOf(i)<0&&(n[i]=t[i]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,i=Object.getOwnPropertySymbols(t);a<i.length;a++)e.indexOf(i[a])<0&&Object.prototype.propertyIsEnumerable.call(t,i[a])&&(n[i[a]]=t[i[a]]);return n};const yt=1e3/30;function St(t){return new Date(t).getTime()}const bt=t=>{const{value:e,format:n="HH:mm:ss",onChange:i,onFinish:a}=t,d=ht(t,["value","format","onChange","onFinish"]),m=ke(),c=r.useRef(null),l=()=>{a==null||a(),c.current&&(clearInterval(c.current),c.current=null)},p=()=>{const s=St(e);s>=Date.now()&&(c.current=setInterval(()=>{m(),i==null||i(s-Date.now()),s<Date.now()&&l()},yt))};r.useEffect(()=>(p(),()=>{c.current&&(clearInterval(c.current),c.current=null)}),[e]);const u=(s,b)=>$t(s,Object.assign(Object.assign({},b),{format:n})),f=s=>ue(s,{title:void 0});return r.createElement(_,Object.assign({},d,{value:e,valueRender:f,formatter:u}))},Ct=r.memo(bt);_.Countdown=Ct;const{Title:jt,Text:X}=Ge,U={totalUsers:1234,totalBooks:5678,activeUsers:890,newBooks:123},Et=[{id:"1",type:"user",action:"新用户注册",description:"张三 注册了新账户",timestamp:"2分钟前",avatar:o.jsx(G,{icon:o.jsx(ie,{})})},{id:"2",type:"book",action:"添加图书",description:"《React 实战指南》 已添加到图书库",timestamp:"5分钟前",avatar:o.jsx(G,{icon:o.jsx(ae,{})})},{id:"3",type:"user",action:"用户活动",description:"李四 更新了个人资料",timestamp:"10分钟前",avatar:o.jsx(G,{icon:o.jsx(ie,{})})},{id:"4",type:"book",action:"图书更新",description:"《Vue.js 开发实战》 信息已更新",timestamp:"15分钟前",avatar:o.jsx(G,{icon:o.jsx(ae,{})})}];function Nt(){const{user:t}=Fe(),e={hidden:{opacity:0,y:20},visible:{opacity:1,y:0}};return o.jsx("div",{children:o.jsx(H.div,{initial:"hidden",animate:"visible",variants:{visible:{transition:{staggerChildren:.1}}},children:o.jsxs(pe,{direction:"vertical",size:"large",style:{width:"100%"},children:[o.jsx(H.div,{variants:e,children:o.jsxs(D,{children:[o.jsxs(jt,{level:3,style:{marginBottom:8},children:["欢迎回来，",t==null?void 0:t.username,"！"]}),o.jsxs(X,{type:"secondary",children:["今天是 ",new Date().toLocaleDateString("zh-CN",{year:"numeric",month:"long",day:"numeric",weekday:"long"})]})]})}),o.jsxs(Se,{gutter:[16,16],children:[o.jsx(V,{xs:24,sm:12,lg:6,children:o.jsx(H.div,{variants:e,children:o.jsx(D,{children:o.jsx(_,{title:"总用户数",value:U.totalUsers,prefix:o.jsx(ie,{}),valueStyle:{color:"#3f8600"}})})})}),o.jsx(V,{xs:24,sm:12,lg:6,children:o.jsx(H.div,{variants:e,children:o.jsx(D,{children:o.jsx(_,{title:"总图书数",value:U.totalBooks,prefix:o.jsx(ae,{}),valueStyle:{color:"#1677ff"}})})})}),o.jsx(V,{xs:24,sm:12,lg:6,children:o.jsx(H.div,{variants:e,children:o.jsx(D,{children:o.jsx(_,{title:"活跃用户",value:U.activeUsers,prefix:o.jsx(dt,{}),valueStyle:{color:"#cf1322"}})})})}),o.jsx(V,{xs:24,sm:12,lg:6,children:o.jsx(H.div,{variants:e,children:o.jsx(D,{children:o.jsx(_,{title:"新增图书",value:U.newBooks,prefix:o.jsx(st,{}),valueStyle:{color:"#722ed1"},suffix:"本"})})})})]}),o.jsx(H.div,{variants:e,children:o.jsx(D,{title:"最近活动",extra:o.jsx(X,{type:"secondary",children:"实时更新"}),children:o.jsx(q,{itemLayout:"horizontal",dataSource:Et,renderItem:n=>o.jsx(q.Item,{children:o.jsx(q.Item.Meta,{avatar:n.avatar,title:n.action,description:o.jsxs(pe,{direction:"vertical",size:0,children:[o.jsx(X,{children:n.description}),o.jsx(X,{type:"secondary",style:{fontSize:12},children:n.timestamp})]})})})})})})]})})})}export{Nt as default};
