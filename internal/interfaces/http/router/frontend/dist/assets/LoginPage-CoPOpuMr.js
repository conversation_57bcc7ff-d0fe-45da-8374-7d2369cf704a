import{r as u,u as y,a as g,b as j,j as e,S as f,T as b,R as I,B as o}from"./index-P7H5iTop.js";import{m as R,C as v}from"./proxy-OhLL-xTq.js";import{F as t,I as l,R as S}from"./index-C73uzCkN.js";import{R as w,a as C}from"./LockOutlined-DkqxqBe7.js";import{D as T}from"./index-BJejOudn.js";const{Title:k,Text:d}=b;function A(){var i,n;const[c,a]=u.useState(!1),r=y(),m=g(),{login:p}=j(),x=((n=(i=m.state)==null?void 0:i.from)==null?void 0:n.pathname)||"/dashboard",h=async s=>{try{a(!0),await p(s),r(x,{replace:!0})}catch{}finally{a(!1)}};return e.jsx("div",{style:{minHeight:"100vh",display:"flex",alignItems:"center",justifyContent:"center",background:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",padding:"20px"},children:e.jsx(R.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},children:e.jsx(v,{style:{width:400,boxShadow:"0 8px 32px rgba(0, 0, 0, 0.1)",borderRadius:16},bodyStyle:{padding:"40px"},children:e.jsxs(f,{direction:"vertical",size:"large",style:{width:"100%"},children:[e.jsxs("div",{style:{textAlign:"center"},children:[e.jsx(k,{level:2,style:{marginBottom:8},children:"欢迎回来"}),e.jsx(d,{type:"secondary",children:"登录到您的账户"})]}),e.jsxs(t,{name:"login",onFinish:h,autoComplete:"off",size:"large",children:[e.jsx(t.Item,{name:"email",rules:[{required:!0,message:"请输入邮箱地址"},{type:"email",message:"请输入有效的邮箱地址"}],children:e.jsx(l,{prefix:e.jsx(I,{}),placeholder:"邮箱地址"})}),e.jsx(t.Item,{name:"password",rules:[{required:!0,message:"请输入密码"},{min:6,message:"密码至少6位字符"}],children:e.jsx(l.Password,{prefix:e.jsx(C,{}),placeholder:"密码",iconRender:s=>s?e.jsx(w,{}):e.jsx(S,{})})}),e.jsx(t.Item,{children:e.jsx(o,{type:"primary",htmlType:"submit",loading:c,block:!0,style:{height:48},children:"登录"})})]}),e.jsx(T,{plain:!0,children:e.jsx(d,{type:"secondary",children:"还没有账户？"})}),e.jsx(o,{type:"default",block:!0,style:{height:48},onClick:()=>r("/register"),children:"立即注册"})]})})})})}export{A as default};
